import { defineStore } from 'pinia'

export const useTestRecordStore = defineStore('testRecord', {
    state: () => ({
        testPlanId: null,
        tab: null,
    }),
    actions: {
        setTestPlanId(testPlanId) {
            this.testPlanId = testPlanId
        },
        clearTestPlanId() {
            this.testPlanId = null
        },
        setTab(tab) {
            this.tab = tab
        },
    },
    persist: {
        key: 'testRecord',
        storage: sessionStorage
      }
  })