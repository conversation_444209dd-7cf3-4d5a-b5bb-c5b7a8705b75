<template>

    <div class="tool-bar-container">
       
        <div style="margin-left: auto; display: flex; gap: 10px;">
            <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                <el-button text bg @click="handleReset">重置</el-button>
            </el-tooltip>
            <filterButton @click="onFilterStatusChange" :count="filterCount" />
            <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
        </div>
    </div>

    <div class="filter-container" v-if="showFilterContainer">
        <el-input v-model="form.name_re" placeholder="请输入计划名称" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
    </div>

    <el-table :data="tableData" stripe style="width: 100%" class="table-container">
        <!-- <el-table-column type="expand">
            <template #default="props">
                <div style="padding: 10px 50px;">
                    <h4>子计划：</h4>
                    <el-table :data="props.row.sub_plans">
                        <el-table-column label="名称" prop="name" width="200" />
                        <el-table-column label="机台" prop="machine_number" width="200" />
                        <el-table-column label="测试状态" prop="status" width="200">
                            <template #default="{ row }">
                                <el-tag v-if="row.test_record === null" type="danger">未开始</el-tag>
                                <el-tag v-if="row.test_record?.status === 0" type="warning">进行中</el-tag>
                                <el-tag v-else-if="row.test_record?.status === 1" type="success">已完成</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="已测试用例" width="300">
                            <template #default="{ row }">
                                <template v-if="row.test_record !== null">
                                    <span>PASS: {{ row.test_record?.pass_count }}</span>
                                    <el-divider direction="vertical" />
                                    <span>NG: {{ row.test_record?.ng_count }}</span>
                                    <el-divider direction="vertical" />
                                    <span>待判定: {{ row.test_record?.unknown_count }}</span>
                                </template>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </template>
        </el-table-column> -->
        <el-table-column prop="project_name" label="所属项目" width="300"></el-table-column>
        <el-table-column prop="name" label="计划名称" width="200"></el-table-column>
        <el-table-column prop="software_version" label="产品软件版本" width="300"></el-table-column>
        <el-table-column prop="plan_type" label="计划类型" width="200">
            <template #default="{ row }">
                <el-tag v-if="row.plan_type == 'FULL_FUNCTIONALITY_TEST'" type="success">全功能测试</el-tag>
                <el-tag v-else-if="row.plan_type == 'VERSION_REGRESSION_TEST'" type="success">版本回归测试</el-tag>
                <el-tag v-else-if="row.plan_type == 'SPECIFIC_VALIDATION_TEST'" type="success">专项验证测试</el-tag>
                <el-tag v-else-if="row.plan_type == 'PROBLEM_VALIDATION_TEST'" type="success">问题验证测试</el-tag>
                <el-tag v-else-if="row.plan_type == 'DURABILITY_TEST'" type="primary">耐久测试</el-tag>
                <el-tag v-else type="danger">未知</el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="status" label="计划状态" width="300">
            <template #default="{ row }">
                <el-tag v-if="row.status === 0" type="danger">未开始</el-tag>
                <el-tag v-else-if="row.status === 1" type="warning">进行中</el-tag>
                <el-tag v-else-if="row.status === 2" type="success">已完成</el-tag>
            </template>
        </el-table-column>
        <el-table-column label="操作" min-width="150" fixed="right" align="left">
            <template #default="{ row }">
                <el-button type="primary" size="small" @click="handleDetail(row)" v-if="row.status === 2">详情</el-button>
            </template>
        </el-table-column>

    </el-table>

    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
            @change="onPageChange" />
    </div>

</template>


<script setup>
import { ref, reactive, watch, onMounted, onActivated } from 'vue';
import http from '@/utils/http/http.js';
import { useRouter, useRoute } from 'vue-router';
import { useProjectStore } from '@/stores/project.js';

const router = useRouter();
const route = useRoute();
let projectStore = useProjectStore();
let filterCount = ref(0);
const tableData = ref([]);

let form = reactive({
    page: 1,
    pagesize: 10,
    project_number: '',
});

let total = ref(0);

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/test_reports', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'project_number'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleDetail(row) {
    router.push(`/test_reports/${row.id}`);
};

function handleRefresh() {
    update_table();
};

watch(() => projectStore.project_info, () => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

onMounted(() => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

onActivated(() => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});


</script>


<style lang="scss" scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input,
    .el-select {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;

    .el-select {
        width: 500px;
        margin-left: 10px;
    }

}

.el-aside {
    width: 200px;
    height: 100% !important;

    margin-top: 20px;
}

.el-main {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);

    margin-top: 0px;

    .table-container {
        flex: 1;
        overflow: auto;
    }
}
</style>