<template>
    <h2>{{ test_case.name }}</h2>

    <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane label="基本信息" name="1">
            <el-form style="text-align: left;">
                <el-form-item label="编号：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal; margin-left: 55px;"
                    >{{ test_case.number }}</span>
                </el-form-item>
                <el-form-item label="所属项目：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal; margin-left: 27px;"
                    >{{ test_case.project_name }}({{ test_case.project_number }})</span>
                </el-form-item>
                <el-form-item label="所属模块：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal; margin-left: 27px;"
                    >{{ moduleMap[test_case.module] || test_case.module }}</span>
                    <span style="font-weight: normal; margin-left: 27px;" v-if="test_case.module_2level">&nbsp;/&nbsp;{{ moduleMap[test_case.module + '-' +
                        test_case.module_2level] ||
                        test_case.module_2level }}</span>
                    <span style="font-weight: normal; margin-left: 27px;" v-if="test_case.module_3level">&nbsp;/&nbsp;{{ moduleMap[test_case.module + '-' +
                        test_case.module_2level + '-' +
                        test_case.module_3level] || test_case.module_3level }}</span>
                </el-form-item>
                <el-form-item label="用例类型：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal; margin-left: 27px;"
                    >{{ typeMap[test_case.type] || test_case.type }}</span>
                </el-form-item>
                <el-form-item label="用例活动类型：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal;"
                    >{{ actionTypeMap[test_case.action_type] || test_case.action_type }}</span>
                </el-form-item>
                <el-form-item label="优先级：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal; margin-left: 42px;"
                    >{{ priorityMap[test_case.priority] || test_case.priority }}</span>
                </el-form-item>
                <el-form-item label="用例来源：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal; margin-left: 27px;"
                    >{{ sourceMap[test_case.source] || test_case.source }}</span>
                </el-form-item>
                <el-form-item label="执行标准：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal; margin-left: 27px;"
                    >{{ { nio: '蔚来', voyah: '岚图' }[test_case.es_source] || test_case.es_source }}</span>
                </el-form-item>
                <el-form-item label="执行标准ID：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal; margin-left: 20px;"
                    >{{ test_case.es_id }}</span>
                </el-form-item>
                <el-form-item label="产品类型：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal; margin-left: 27px;"
                    >{{ producttypesMap[test_case.product_type_id] || test_case.product_type_id }}</span>
                </el-form-item>
                <el-form-item label="用例生成方法：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal;"
                    >{{ generationMethodMap[test_case.generation_method] || test_case.generation_method }}</span>
                </el-form-item>
                <el-form-item label="执行方式：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal; margin-left: 27px;"
                    >{{ executeModeMap[test_case.execute_mode] || test_case.execute_mode }}</span>
                </el-form-item>
                <el-form-item label="功能安全属性：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal;"
                    >{{ functionSafeAttribMap[test_case.function_safe_attrib] || test_case.function_safe_attrib
                    }}</span>
                </el-form-item>
                <el-form-item label="测试方法：" style="font-weight: bold;">
                    <el-tag style="font-weight: normal; margin-left: 27px;" type="primary" v-for="i in test_case.test_method">{{ testMethodMap[i] || i }}</el-tag>
                </el-form-item>
                <el-form-item label="标签：" style="font-weight: bold;">
                    <el-tag style="font-weight: normal; margin-left: 55px;" v-for="tag in test_case.tag_name || []" :key="tag" type="primary">{{ tag }}</el-tag>
                </el-form-item>
                <el-form-item label="备注：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal; margin-left: 55px;"
                    >{{ test_case.remark }}</span>
                </el-form-item>
                <el-form-item label="用例版本：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal; margin-left: 27px;"
                    >V{{ test_case.version }}.0</span>
                </el-form-item>
                <el-form-item label="用例状态：" style="font-weight: bold;">
                    <el-tag style="font-weight: normal; margin-left: 27px;" v-if="test_case.status == 'PENDING'" type="info">待评审</el-tag>
                    <el-tag style="font-weight: normal; margin-left: 27px;" v-else-if="test_case.status == 'REVIEWING'" type="warning">评审中</el-tag>
                    <el-tag style="font-weight: normal; margin-left: 27px;" v-else-if="test_case.status == 'APPROVED'" type="success">评审通过</el-tag>
                    <el-tag style="font-weight: normal; margin-left: 27px;" v-else-if="test_case.status == 'REJECTED'" type="warning">评审不通过</el-tag>
                    <el-tag style="font-weight: normal; margin-left: 27px;" v-else-if="test_case.status == 'CANCEL'" type="warning">已撤销</el-tag>
                    <el-tag style="font-weight: normal; margin-left: 27px;" v-else-if="test_case.status == 'DEPRECATED'" type="success">废弃</el-tag>
                    <el-tag style="font-weight: normal; margin-left: 27px;" v-else type="danger">未知</el-tag>
                </el-form-item>
                <el-form-item label="创建人：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal; margin-left: 42px;"
                    >{{ test_case.creator_name }}</span>
                </el-form-item>
                <el-form-item label="已关联需求：" style="font-weight: bold;">
                    <span
                    style="font-weight: normal; margin-left: 13px;"
                    >{{ test_case.requirements?.[0]?.requirement_number }}</span>
                </el-form-item>
            </el-form>

        </el-tab-pane>
        <el-tab-pane label="执行信息" name="2">
            <el-form class="tc-form" style="color: #606266; text-align: left;">
                <el-row :gutter="10">
                    <el-col :span="8">
                        <el-form-item label="用例执行次数：" label-position="left"
                            style="text-align: left; margin-left: -30px; font-weight: bold;">
                            <span
                                style="display: inline-block; text-align: left; color: #606266; margin-left: -10px; font-weight: normal;">{{
                                test_case.cycle }}</span>
                        </el-form-item>

                    </el-col>
                </el-row>
                <el-form-item label="前置条件：" label-position="left"
                    style="text-align: left; margin-top: 10px; margin-left: -30px; font-weight: bold;">
                    <span style="display: inline-block; text-align: left; color: #606266; margin-left: 18px; font-weight: normal;">{{ test_case.preconditions
                        }}</span>
                </el-form-item>

                <el-form-item label="步骤：" label-position="left"
                    style="text-align: left; margin-top: 10px; margin-left: -30px; font-weight: bold;">
                    <span style="display: inline-block; text-align: left; color: #606266; white-space: pre-wrap; margin-left: 45.5px; font-weight: normal;">{{ test_case.steps
                        }}</span>
                </el-form-item>

                <el-form-item label="期望结果：" label-position="left"
                    style="text-align: left; margin-top: 10px; margin-left: -30px; font-weight: bold;">
                    <span style="display: inline-block; text-align: left; color: #606266;white-space: pre-wrap; margin-left: 16.5px; font-weight: normal;">{{ test_case.expected
                        }}</span>
                </el-form-item>

            </el-form>
            <div style="padding: 0 10px; text-align: left;">
                <h4 style="color: #606266; text-align: left; margin-left: -10px;">测试步骤：</h4>
                <el-table :data="test_case.test_steps" stripe border style="width: 100%; margin-left: -10px;" row-key="id" height="800px">
                    <el-table-column label="序号" width="120" align="center">
                        <template #default="{ row, $index }">
                            {{ $index + 1 }}
                        </template>
                    </el-table-column>
                    <el-table-column label="步骤类型" width="150" align="center">
                        <template #default="{ row }">
                            <span v-if="row.type == 'MANUAL'">手动执行</span>
                            <span v-if="row.type == 'CAN'">CAN报文</span>
                            <span v-else-if="row.type == 'LIN'">LIN报文</span>
                            <span v-else-if="row.type == 'I2C'">I2C指令</span>
                            <span v-else-if="row.type == 'CUSTOM_CMD'">自定义指令</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="test_case.execute_mode != 'MANUAL_EXECUTION'" label="步骤参数" min-width="200"
                        align="center">
                        <template #default="{ row }">
                            <ShowStep :type="row.type" :params="row.params" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="desc" label="步骤描述" min-width="200" align="center"></el-table-column>
                    <el-table-column prop="expectation" label="期望结果" min-width="200" align="center"></el-table-column>
                </el-table>
            </div>
        </el-tab-pane>
        <el-tab-pane label="历史版本" name="3">
            <div style="padding: 0 10px;">
                <el-table :data="archiveData">
                    <el-table-column min-width="100" property="tag" label="历史版本">
                        <template #default="{ row }">
                            <el-link type="primary" @click="onDetail(row)">V{{ row.tag }}.0</el-link>
                        </template>
                    </el-table-column>
                    <el-table-column min-width="200" property="create_time" label="存档时间" />
                </el-table>
            </div>
        </el-tab-pane>
    </el-tabs>

</template>

<script setup>
import { ref, onMounted } from 'vue'
import http from '@/utils/http/http.js';
import ShowStep from '@/views/test_cases2/step/showStep.vue';

const props = defineProps({
    id: {
        type: Number,
        default: 0,
    },
    testCaseNumber: {
        type: String,
        default: "",
    },
    testCaseData: {
        type: Object,
        default: {},
    }
})
const activeName = ref('2');
const test_case = ref({});
const archiveData = ref([]);
const test_case_id = ref(0);
const producttypesMap = ref({})

let moduleMap = ref({});
let actionTypeMap = ref({});
let typeMap = ref({
    "DURABLE_TEST": "耐久测试",
    "PERFORMANCE_TEST": "性能测试",
    "FUNCTION_TEST": "功能测试",
    "PROTOCOL_STACK_TEST": "协议栈测试"
});
let sourceMap = ref({
    "TASK_CHANGE": "用例库沿用",
    "TASK_AFFIRM": "需求分析",
    "NEW_PROJECT": "需求变更",
    "HORIZONTAL_SCALING": "横向扩展"
});
let priorityMap = ref({
    "HIGH": "高",
    "MIDDLE": "中",
    "LOW": "低",
});
let generationMethodMap = ref({
    "BOUNDARY_VALUE_METHOD": "边界值法",
    "FRUIT_GRAPH_METHOD": "因果法",
    "DECISION_TABLE_DRIVE": "判定表驱法",
    "FUNCTION_DIAGRAM_METHOD": "功能图法",
    "SCENE_METHOD": "场景法",
    "EQUIVALENCE_CLASS": "等价类",
    "FIELD_EXPERIENCE_ANALYSIS": "现场经验分析",
    "EXTERNAL_AND_INTERNAL_INTERFACE_ANALYSIS": "外部和内部接口分析法",
    "PROCESS_ANALYSIS": "流程分析法",
    "BACKWARD_ANALYSIS": "反向分析",
    "FORWARD_ANALYSIS": "正向分析",
    "ENVIRONMENTAL_CONDITIONS_AND_OPERATIONAL_USE_CASE_ANALYSIS": "环境条件和操作用例分析",
    "MISGUESS": "错误猜错法",
    "SEQUENCE_AND_SOURCE_ANALYSIS": "序列和来源的分析",
    "COMMON_LIMIT_CONDITIONS_4_DEPENDENCE": "相依性的常见极限条件",
    "ANALYSIS_OPERATING_CONDITIONS_USE_CASES": "用例的运行条件分析",
    "DEMAND_ANALYSIS": "基于需求分析",
});
let executeModeMap = ref({
    "AUTOMATED_EXECUTION": "自动化测试",
    "MANUAL_EXECUTION": "手动测试",
    "SEMI_AUTOMATED_EXECUTION": "半自动化测试",
});
let functionSafeAttribMap = ref({
    "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AA": "ASIL A",
    "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AD": "ASIL D",
    "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AQD": "ASIL QM(D)",
    "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AQC": "ASIL QM(C)",
    "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_NA": "N/A",
    "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AQB": "ASIL QM(B)",
    "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AQA": "ASIL QM(A)",
    "TEST_CASE_FUNCTIONAL_SAFETY_ATTRIBUTE_AB": "ASIL B",
});
let testMethodMap = ref({
    "PRESSURE_TEST": "压力测试",
    "RESOURCE_USAGE_TESTING": "资源使用情况测试",
    "INTERACTION_COMMUNICATION_TESTING": "互动/沟通测试",
    "INTERFACE_CONSISTENCY_CHECK": "接口一致性检查",
    "TESTS_BASED_ON_FIELD_EXPERIENCE": "根据现场经验进行的测试",
    "FALSE_GUESS_TEST": "错误猜测测试",
    "PERFORMANCE_TEST": "性能测试",
    "FAULT_INJECTION_TEST": "故障注入测试",
    "BACK_TO_BACK_TESTING": "背靠背测试",
    "INTERFACE_TEST": "基于接口测试",
    "DEMAND_TEST": "基于需求测试",
});

// let producttypesMap = ref({
//     "11": "仪表屏",
//     "23": "中控屏" ,
//     "24": "副驾屏",
//     "25": "后排屏",
//     "26": "扶手屏左屏",
//     "27": "扶手屏右屏",
//     "28": "吸顶屏",
//     "29": "氛围灯",
//     "30": "机器人",
//     "33": "主机",
// })


http.get('/product_types', { params: { pagesize: 100000 } }).then(res => {
    let data = res.data.data.results;
    producttypesMap.value = {}
    data.forEach((item) => {
        producttypesMap.value[item.id] = item.name
    })

}).catch(err => {
    console.log(err);
});

onMounted(() => {

    if (props.testCaseData?.id) {
        test_case.value = props.testCaseData;
        test_case_id.value = props.testCaseData.id;
    } else if (props.id > 0) {
        test_case_id.value = props.id;
        http.get(`/test_cases/${props.id}`).then(res => {
            let data = res.data.data;
            test_case.value = data;
        }).catch(err => {
            console.log(err);
        });
    } else if (props.testCaseNumber) {
        http.get(`/test_cases/detail_by_number`, { params: { number: props.testCaseNumber }}).then(res => {
            let data = res.data.data;
            test_case.value = data;
            test_case_id.value = data.id;
        }).catch(err => {
            console.log(err);
        });
    }

    http.get('/functions').then(res => {
        res.data.data.results.forEach(item => {
            moduleMap.value[item.number] = item.name;
            if (item.children) {
                item.children.forEach(item2 => {
                    moduleMap.value[item.number + '-' + item2.number] = item2.name;
                    if (item2.children) {
                        item2.children.forEach(item3 => {
                            moduleMap.value[item.number + '-' + item2.number + '-' + item3.number] = item3.name;
                        });
                    }
                });
            }
        });
    });

    http.get('/test_m/test_case_types', { params: { pagesize: 10000 } }).then(res => {
        res.data.data.results.forEach(item => {
            actionTypeMap.value[item.number] = item.name;
        });

    });

    http.get('/test_cases/archives', { params: { test_case_id: test_case_id.value } }).then(res => {
        archiveData.value = res.data.data.results;
    }).catch(err => {
        console.log(err);
    });
});



</script>


<style lang="scss" scoped>
.tc-form {
    padding: 0 30px;

    .el-form-item {
        margin-bottom: 0;
    }
}

.title_color {
    color: #666;
}

h4 {

    font-weight: bold;
}
</style>