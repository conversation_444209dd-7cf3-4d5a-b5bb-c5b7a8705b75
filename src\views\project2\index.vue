<template>
    <div class="container">

        <div class="tool-bar-container">

            <div style="margin-left: auto; display: flex; gap: 10px;">
                <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                    <el-button text bg @click="handleReset">重置</el-button>
                </el-tooltip>
                <filterButton @click="onFilterStatusChange" :count="filterCount" />
                <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
            </div>
        </div>

        <div class="filter-container" v-if="showFilterContainer">
            <el-input v-model="form.name" placeholder="请输入项目名称或者编号" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
        </div>

        <div class="table-container">
            <el-table :data="tableData" stripe border>

                <el-table-column prop="code" label="项目编号" width="200" align="left"></el-table-column>
                <el-table-column prop="name" label="项目名称" width="200" align="left"></el-table-column>
                <el-table-column label="负责人" width="150" align="left">
                    <template #default="{ row }">
                        <div style="display: flex; align-items: center; justify-content: flex-start;">
                            <el-avatar v-if="row.managerInfo?.avatar" :size="25" :src="row.managerInfo?.avatar" />
                            &nbsp&nbsp{{ row.managerInfo?.name }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="progress" label="项目进度" width="150" align="center"></el-table-column>
                <el-table-column prop="projectStatus" label="项目状态" width="180" align="center"></el-table-column>
                <el-table-column label="测试用例" width="150" align="center">
                    <template #default="{ row }">
                        <el-button type="primary" plain size="small" @click="handleTestcase(row)">测试用例</el-button>
                    </template>
                </el-table-column>
                <el-table-column label="测试计划" width="150" align="center">
                    <template #default="{ row }">
                        <el-button type="primary" plain size="small" @click="handleVersion(row)">测试计划</el-button>
                    </template>
                </el-table-column>
                <el-table-column label="测试记录" width="150" align="center">
                    <template #default="{ row }">
                        <el-button type="primary" plain size="small" @click="handleTestRecord(row)">测试记录</el-button>
                    </template>
                </el-table-column>
                <el-table-column label="操作" min-width="150" fixed="right" align="left">
                    <template #default="{ row }">
                        <el-button type="primary" size="small" @click="handleDetail(row)">详情</el-button>
                    </template>
                </el-table-column>

            </el-table>
        </div>

        <div class="pagination-container">
            <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
                v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
                @change="onPageChange" />
        </div>
    </div>

</template>


<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import http from '@/utils/http/http.js';
import { useProjectStore } from '@/stores/project.js';
import filterButton from '@/components/filterButton.vue';

const router = useRouter();
let projectStore = useProjectStore();
const tableData = ref([]);

let form = reactive({
    page: 1,
    pagesize: 10,
});

const filterCount = ref(0);

let total = ref(0);
let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/projects/p', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => key !== 'page' && key !== 'pagesize' && key !== 'project_number').reduce((count, key) => {
        return form[key] ? count + 1 : count;
    }, 0)
};

function onPageChange() {
    update_table();
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
    });
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleDetail(row) {
    router.push({ path: `/projects/${row.projectId}` });
};

function handleTestcase(row) {
    projectStore.update({ projectCode: row.code, id: row.projectId, name: row.name });
    router.push({ path: `/test_cases2/list`, query: { project_number: row.code } });
};

function handleTestRecord(row) {
    projectStore.update({ projectCode: row.code, id: row.projectId, name: row.name });
    router.push({ path: `/test_records/list`, query: { project_number: row.code } });
};

function handleVersion(row) {
    projectStore.update({ projectCode: row.code, id: row.projectId, name: row.name });
    router.push({ path: `/test_plans/list`, query: { project_number: row.code } });
};

function handleRefresh() {
    update_table();
};

onMounted(() => {
    update_table();
});

</script>


<style lang="scss" scoped>
.container {
    display: flex;
    flex-direction: column;
    align-items: left;
}

.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;

    .el-select {
        width: 500px;
    }

}
</style>