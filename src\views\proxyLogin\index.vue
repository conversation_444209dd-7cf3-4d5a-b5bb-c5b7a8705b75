<template>

</template>

<script setup>
import { onMounted } from 'vue';

onMounted(() => {
    const base_url = "https://open.feishu.cn/open-apis/authen/v1/authorize";
    const app_id = "cli_a560d402f139d00b";
    const redirect_uri = encodeURIComponent("http://www.hwauto.com.cn:59999" + "/proxy_login");
    const scope = ['contact:contact.base:readonly', 'contact:contact:readonly_as_app'];
    const state = "123";

    let url = `${base_url}?app_id=${app_id}&redirect_uri=${redirect_uri}&scope=${scope.join(" ")}&state=${state}`;

    window.location.href = url;
});

</script>