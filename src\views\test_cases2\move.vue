<template>
    <el-row :gutter="20">
        <el-col :span="8">
            <el-form label-width="auto">
                <el-form-item label="所属模块">
                    <span>{{ s_module }}</span>
                    <el-input style="display: none;" />
                </el-form-item>

                <el-form-item label="用例活动类型">
                    <span>{{ s_aciton_type }}</span>
                    <el-input style="display: none;" />
                </el-form-item>
            </el-form>
        </el-col>
        <el-col :span="3">
            <div style="margin-top: 30px;"><span>移动到</span></div>
        </el-col>
        <el-col :span="13">
            <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">
                <el-form-item label="所属模块" prop="module">
                    <el-tree-select v-model="form.module" :data="modules" :props="{ label: 'name', value: 'm', disabled: 'deprecated' }"
                        ref="moduleRef" placeholder="请选择所属模块" clearable check-strictly :render-after-expand="false"
                        node-key="m" show-checkbox>
                    </el-tree-select>
                </el-form-item>

                <el-form-item label="用例活动类型" prop="action_type">
                    <el-select v-model="form.action_type" placeholder="用例活动类型">
                        <el-option v-for="item in action_types" :label="item.name" :value="item.number"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
        </el-col>
    </el-row>

    <div class="submit-button-container">
        <el-button type="default" @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onConfirm">提交</el-button>
    </div>

</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import http from '@/utils/http/http.js'

const props = defineProps({
    r_id: {
        type: Number,
        required: true
    }
});

const formRef = ref(null);
const moduleRef = ref(null);
const action_types = ref([]);
const modules = ref([]);
const s_module = ref('');
const s_aciton_type = ref('');
const s_module_number = ref('');
const s_aciton_type_number = ref('');

const form = ref({
    action_type: '',
    module: '',
});

const rules = {
    module: [
        { required: true, message: '请选择所属模块', trigger: 'change' }
    ],
    action_type: [
        { required: true, message: '请选择用例活动类型', trigger: 'change' }
    ]
};

const r_id = computed(() => {
    return props.r_id;
});

const emit = defineEmits(['cancel', 'confirm']);

const onCancel = () => {
    emit('cancel');
}

function parseModuleValue(node) {
    if (node.level == 1) {
        return node.data.number;
    } else {
        return parseModuleValue(node.parent) + ', ' + node.data.number;
    }
}

const onConfirm = () => {
    formRef.value.validate((valid) => {
        if (valid) {

            let data = {
                ...form.value,
            };

            const node = moduleRef.value.getNode(form.value.module);
            data.module = parseModuleValue(node);

            http.post(`/test_cases/${r_id.value}/move`, data).then(res => {
                emit('confirm');
                ElMessage({
                    message: '移动成功.',
                    type: 'success',
                });

            }).catch(err => {
                ElMessage({
                    type: 'error',
                    message: err.response.data.msg
                });
            });
        }
    });
}

watch([modules, s_module_number], () => {
    s_module.value = modules.value.find(item => item.m == s_module_number.value)?.name;
});

watch([action_types, s_aciton_type_number], () => {
    s_aciton_type.value = action_types.value.find(item => item.number == s_aciton_type_number.value)?.name;
});


onMounted(() => {

    http.get('/test_m/test_case_types', { params: { pagesize: 1000 } }).then(res => {
        let data = res.data.data.results;
        action_types.value = data;
    }).catch(err => {
        console.log(err);
    });

    http.get('/functions', { params: { pagesize: 1000 } }).then(res => {
        let data = res.data.data.results;
        data.forEach(item => {
            item.m = item.number;
            if (item.children) {
                item.children.forEach(item2 => {
                    item2.m = item.number + '-' + item2.number;
                    if (item2.children) {
                        item2.children.forEach(item3 => {
                            item3.m = item.number + '-' + item2.number + '-' + item3.number;
                        });
                    }
                });
            }
        });
        modules.value = data;
    }).catch(err => {
        console.log(err);
    });

    http.get(`/test_cases/${r_id.value}`).then(res => {

        let data = res.data.data;

        form.value.action_type = data.action_type;
        form.value.module = [data.module, data.module_2level, data.module_3level].filter(item => item).join('-');

        s_module_number.value = data.module;
        s_aciton_type_number.value = data.action_type;
    }).catch(err => {

    });
})


</script>

<style scoped>
.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>