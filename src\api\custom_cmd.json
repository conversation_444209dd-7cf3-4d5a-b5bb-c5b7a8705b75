[{"value": "ReadScreenTemp", "label": "读取屏幕温度"}, {"value": "ReadPCBTemp", "label": "读取PCB温度"}, {"value": "ReadSoftwareVersion", "label": "读取软件版本", "params": [{"type": "el-input", "label": "软件版本号", "value": ""}]}, {"value": "WriteHardwareVersion", "label": "写入硬件版本", "params": [{"type": "el-input", "label": "硬件版本号", "value": ""}]}, {"value": "ReadHardwareVersion", "label": "读取硬件版本", "params": [{"type": "el-input-number", "label": "硬件版本长度", "value": 0, "min": 0}]}, {"value": "ReadInnerSoftwareVersion", "label": "读取内部软件版本"}, {"value": "ReadInnerHardwareVersion", "label": "读取内部硬件版本", "params": [{"type": "el-input-number", "label": "内部硬件版本长度", "value": 0, "min": 0}]}, {"value": "WritePartNumber", "label": "写入零件号", "params": [{"type": "el-input", "label": "零件号", "value": ""}]}, {"value": "ReadPartNumber", "label": "读取零件号"}, {"value": "ReadLDVersion", "label": "读取LocalDimming版本"}, {"value": "ReadTDDIVersion", "label": "读取TDDI版本"}, {"value": "ReadTCONVersion", "label": "读取TCON版本"}, {"value": "ReadBootVersion", "label": "读取Boot版本"}, {"value": "SwitchBrightness", "label": "切换亮度", "params": [{"type": "el-select", "label": "亮度模式", "value": "1", "options": [{"value": "1", "label": "百分比亮度"}, {"value": "2", "label": "实际亮度"}]}, {"type": "el-input-number", "label": "亮度", "value": 0, "min": 0}]}, {"value": "ReadBrightness", "label": "读取亮度"}, {"value": "ReadLightSensor", "label": "读取光感"}, {"value": "SwitchColor", "label": "切换背景颜色", "params": [{"type": "el-input", "label": "背景色", "value": ""}]}, {"value": "SwitchPattern", "label": "切换背景画面", "params": [{"type": "el-input-number", "label": "画面索引", "value": 0, "min": 0}]}, {"value": "SwitchSleep", "label": "休眠"}, {"value": "SwitchWakeup", "label": "唤醒"}, {"value": "ReadHWSN", "label": "读取HWSN", "params": [{"type": "el-input-number", "label": "HWSN的长度", "value": 0, "min": 0}]}, {"value": "WriteHWSN", "label": "写入HWSN", "params": [{"type": "el-input", "label": "HWSN", "value": ""}]}, {"value": "ReadPSN", "label": "读取PSN", "params": [{"type": "el-input-number", "label": "PSN的长度", "value": 0, "min": 0}]}, {"value": "WritePSN", "label": "写入PSN", "params": [{"type": "el-input", "label": "PSN", "value": ""}]}, {"value": "ReadTpVersion", "label": "读取TP版本"}, {"value": "ReadAssemblyVersion", "label": "读取总成版本"}, {"value": "TpcmTest", "label": "TPCM测试"}, {"value": "TouchStillTest", "label": "触摸静止测试【测试静止状态下是否触摸跳点】", "params": [{"type": "el-input-number", "label": "测试时长(s)", "value": 0, "min": 0}]}, {"value": "TouchMarkingTest", "label": "触摸划线测试", "params": [{"type": "el-select", "label": "划线测试模式", "value": "0", "options": [{"value": "0", "label": "从左到右"}, {"value": "1", "label": "从上到下"}, {"value": "2", "label": "从左上到右下"}, {"value": "3", "label": "从右上到左下"}]}]}, {"value": "TouchRespTimes", "label": "触摸响应次数测试", "params": [{"type": "el-input-number", "label": "触摸下压时间(ms)", "value": 0, "min": 0}, {"type": "el-input-number", "label": "标定响应次数", "value": 0, "min": 0}]}, {"value": "TouchRespTime", "label": "触摸响应时间测试", "params": [{"type": "el-input-number", "label": "触摸下压时间(ms)", "value": 0, "min": 0}, {"type": "el-input-number", "label": "标定响应时间(ms)", "value": 0, "min": 0}]}, {"value": "TouchReportRate", "label": "触摸报点率测试", "params": [{"type": "el-input-number", "label": "标定报点率", "value": 0, "min": 0}]}, {"value": "SwitchVoltage", "label": "切换电压", "params": [{"type": "el-input-number", "label": "电压值(V)", "value": 0, "min": 0}, {"type": "el-select", "label": "程控电源", "value": "IT-M3200", "options": [{"value": "IT-M3200", "label": "IT-M3200"}, {"value": "TOMMENS", "label": "TOMMENS"}]}, {"type": "el-select", "label": "电源通道", "value": "1", "options": [{"value": "1", "label": "1"}, {"value": "2", "label": "2"}, {"value": "3", "label": "3"}]}]}, {"value": "SwitchStepVoltage", "label": "切换步进电压", "params": [{"type": "el-input-number", "label": "起始电压值(V)", "value": 0, "min": 0}, {"type": "el-input-number", "label": "终止电压值(V)", "value": 0, "min": 0}, {"type": "el-select", "label": "电源通道", "value": "1", "options": [{"value": "1", "label": "1"}, {"value": "2", "label": "2"}, {"value": "3", "label": "3"}]}, {"type": "el-input-number", "label": "间隔时间(ms)", "value": 0, "min": 0}, {"type": "el-input-number", "label": "步进电压(V)", "value": 0, "min": 0}]}, {"value": "VdsPowerOff", "label": "Vds下电指令"}, {"value": "HostPowerOff", "label": "主机下电指令"}, {"value": "HostExitBootAnim", "label": "主机退出Boot动画"}, {"value": "SwitchVisionCollect", "label": "打开/关闭功能视觉检测", "params": [{"type": "el-select", "label": "操作", "value": "", "options": [{"value": "打开", "label": "打开"}, {"value": "关闭", "label": "关闭"}]}]}, {"value": "StartRecord", "label": "打开功能视觉检测录像"}, {"value": "StopRecord", "label": "关闭功能视觉检测录像", "params": [{"type": "el-select", "label": "测试模式", "value": "0", "options": [{"value": "0", "label": "所有模式"}, {"value": "1", "label": "闪屏模式"}, {"value": "2", "label": "花屏模式"}]}, {"type": "el-input-number", "label": "闪屏检测结果阈值", "value": 0.1, "min": 0}, {"type": "el-input-number", "label": "花屏检测结果阈值", "value": 1000, "min": 0}]}, {"value": "SetDelayTime", "label": "设置延时时间", "params": [{"type": "el-input-number", "label": "延时时间(s)", "value": 0, "min": 0}]}, {"value": "SetRandomDelayTime", "label": "设置随机延时时间", "params": [{"type": "el-input-number", "label": "随机延时时间最小值(s)", "value": 0, "min": 0}, {"type": "el-input-number", "label": "随机延时时间最大值(s)", "value": 0, "min": 0}]}, {"value": "SwitchVdsAdbForward", "label": "打开/关闭Vds的Adb Forward功能", "params": [{"type": "el-select", "label": "操作", "value": "", "options": [{"value": "打开", "label": "打开"}, {"value": "关闭", "label": "关闭"}]}]}, {"value": "StartAdbForwardDurabilityTest", "label": "开启Adb Forward耐久测试"}, {"value": "StartCollectVisionBrightness", "label": "开启采集视觉亮度"}, {"value": "ClearCycleCANMsg", "label": "停止循环发送所有CAN消息"}, {"value": "DetectVisionBrightness", "label": "检测视觉亮度", "params": [{"type": "el-input-number", "label": "亮度标定最小值", "value": 0, "min": 0}, {"type": "el-input-number", "label": "亮度标定最大值", "value": 0, "min": 0}]}, {"value": "ReadWorkCurrent", "label": "检测工作电流", "params": [{"type": "el-input-number", "label": "最小值", "value": 0, "min": 0}, {"type": "el-input-number", "label": "最大值", "value": 0, "min": 0}, {"type": "el-select", "label": "程控电源", "value": "IT-M3200", "options": [{"value": "IT-M3200", "label": "IT-M3200"}, {"value": "TOMMENS", "label": "TOMMENS"}]}, {"type": "el-select", "label": "程控电源通道", "value": "1", "options": [{"value": "1", "label": "1"}, {"value": "2", "label": "2"}, {"value": "3", "label": "3"}]}]}, {"value": "ReadWorkVoltage", "label": "检测工作电压", "params": [{"type": "el-input-number", "label": "电压最小值", "value": 0, "min": 0}, {"type": "el-input-number", "label": "电压最大值", "value": 0, "min": 0}]}, {"value": "StopCycleCANMsg", "label": "停止循环发送指定消息ID的CAN消息", "params": [{"type": "el-input", "label": "消息ID", "value": ""}]}, {"value": "UpdateTpFw", "label": "通过bat脚本升级Tp固件", "params": [{"type": "el-input", "label": "bat升级脚本路径", "value": ""}, {"type": "el-input-number", "label": "随机升级时间最小值", "value": 0, "min": 0}, {"type": "el-input-number", "label": "随机升级时间最大值", "value": 0, "min": 0}]}, {"value": "ExecuteAdbCmd", "label": "执行adb指令", "params": [{"type": "el-input", "label": "adb指令", "value": ""}, {"type": "el-input", "label": "期望返回结果", "value": ""}]}, {"value": "ExecuteBat", "label": "执行bat脚本", "params": [{"type": "el-input", "label": "bat执行脚本路径", "value": ""}, {"type": "el-input-number", "label": "随机执行时间最小值", "value": 0, "min": 0}, {"type": "el-input-number", "label": "随机执行时间最大值", "value": 0, "min": 0}]}, {"value": "ReadNitBrightness", "label": "使用CA410读取产品亮度值", "params": [{"type": "el-input-number", "label": "期望亮度最小值", "value": 0, "min": 0}, {"type": "el-input-number", "label": "期望亮度最大值", "value": 0, "min": 0}]}, {"value": "I2cChecksumDetect", "label": "I2C的checksum故障模拟功能", "params": [{"type": "el-input", "label": "I2C功能报文", "value": ""}, {"type": "el-input", "label": "模拟的异常Checksum值", "value": ""}]}, {"value": "ReadPeriodWorkCurrent", "label": "检测周期工作电流", "params": [{"type": "el-input-number", "label": "读取电流间隔时间(s)", "value": 0, "min": 0}, {"type": "el-input-number", "label": "读取电流总时间(s)", "value": 0, "min": 0}, {"type": "el-input-number", "label": "工作电流最小值(A)", "value": 0, "min": 0}, {"type": "el-input-number", "label": "工作电流最大值(A)", "value": 0, "min": 0}, {"type": "el-select", "label": "电源通道", "value": "1", "options": [{"value": "1", "label": "1"}, {"value": "2", "label": "2"}, {"value": "3", "label": "3"}]}]}, {"value": "CalibrateColorTemperature", "label": "校准色温", "params": [{"type": "el-input-number", "label": "色温误差百分比(%)", "value": 0, "min": 0}]}, {"value": "TestColorTemperature", "label": "检测色温", "params": [{"type": "el-input-number", "label": "色温误差百分比(%)", "value": 0, "min": 0}]}, {"value": "CalibrateBrightness", "label": "校准亮度", "params": [{"type": "el-input-number", "label": "色温误差百分比(%)", "value": 0, "min": 0}]}, {"value": "TestBrightness", "label": "检测亮度", "params": [{"type": "el-input-number", "label": "色温误差百分比(%)", "value": 0, "min": 0}]}, {"value": "DetectMcuLog", "label": "检测MCU日志", "params": [{"type": "el-input", "label": "tag标签", "value": ""}, {"type": "el-input", "label": "mark标识", "value": ""}]}, {"value": "DetectSocLog", "label": "检测SOC日志", "params": [{"type": "el-input", "label": "mark标识", "value": "Start Power On"}]}, {"value": "DetectOsLog", "label": "检测OS日志", "params": [{"type": "el-input", "label": "mark标识", "value": ""}]}, {"value": "DetectVdsAppLog", "label": "检测VDS APP日志", "params": [{"type": "el-input", "label": "mark标识", "value": ""}]}, {"value": "SecurityLevel", "label": "27服务预备", "params": [{"type": "el-select", "label": "屏位置", "value": "right", "options": [{"value": "left", "label": "左屏"}, {"value": "right", "label": "右屏"}, {"value": "none", "label": "无"}]}, {"type": "el-select", "label": "安全等级", "value": "4", "options": [{"value": "1", "label": "1"}, {"value": "2", "label": "2"}, {"value": "3", "label": "3"}, {"value": "4", "label": "4"}]}, {"type": "el-input", "label": "send_id", "value": ""}, {"type": "el-input", "label": "send_msg", "value": ""}, {"type": "el-input", "label": "recv_id", "value": ""}, {"type": "el-input", "label": "recv_msg", "value": ""}, {"type": "el-select", "label": "通道", "value": "1", "options": [{"value": "1", "label": "通道1"}, {"value": "2", "label": "通道2"}]}]}, {"value": "SecurityLevelEnter", "label": "27服务进入", "params": [{"type": "el-select", "label": "屏位置", "value": "right", "options": [{"value": "left", "label": "左屏"}, {"value": "right", "label": "右屏"}, {"value": "none", "label": "无"}]}, {"type": "el-select", "label": "安全等级", "value": "4", "options": [{"value": "1", "label": "1"}, {"value": "2", "label": "2"}, {"value": "3", "label": "3"}, {"value": "4", "label": "4"}]}, {"type": "el-input", "label": "send_id", "value": ""}, {"type": "el-input", "label": "send_msg", "value": ""}, {"type": "el-input", "label": "recv_id", "value": ""}, {"type": "el-input", "label": "recv_msg", "value": ""}, {"type": "el-select", "label": "通道", "value": "1", "options": [{"value": "1", "label": "通道1"}, {"value": "2", "label": "通道2"}]}]}, {"value": "SetCanMsgDelayTime", "label": "设置接收can消息延时时间", "params": [{"type": "el-input-number", "label": "延迟时间(s)", "value": 0.5, "min": 0, "max": 3, "step": 0.1}]}, {"value": "CanProtocolStartApp", "label": "开启canoe软件", "params": [{"type": "el-input", "label": "cfg文件路径", "value": ""}, {"type": "el-input", "label": "tse文件路径", "value": ""}]}, {"value": "CanProtocolAddTestID", "label": "输入canoe协议测试id项目", "params": [{"type": "el-select", "label": "模块类型", "value": "FLASH", "options": [{"value": "FLASH", "label": "FLASH"}, {"value": "UDS-TP", "label": "UDS-TP"}, {"value": "CANFD", "label": "CANFD"}, {"value": "AUTOSAR", "label": "AUTOSAR"}, {"value": "UDS-TP-BOOT", "label": "UDS-TP-BOOT"}]}, {"type": "el-input-number", "label": "测试的id", "value": 1, "min": 0}]}, {"value": "CanProtocolStopApp", "label": "关闭canoe软件"}, {"value": "Mate3BeforeRotate", "label": "mate转动之前"}, {"value": "Mate3AfterRotate", "label": "mate转动之后", "params": [{"type": "el-select", "label": "选择转动部位", "value": "YAW", "options": [{"value": "YAW", "label": "YAW"}, {"value": "PITCH", "label": "PITCH"}]}, {"type": "el-input-number", "label": "角度值", "value": 0, "min": 0}]}, {"value": "ServoMotorPositioning", "label": "伺服电机定位", "params": [{"type": "el-select", "label": "选项", "value": "center_point", "options": [{"value": "center_point", "label": "中心点定位"}, {"value": "9_point", "label": "9点定位"}]}]}, {"value": "DetectMcuError", "label": "检测MCU异常", "params": [{"type": "el-input", "label": "mark标识", "value": ""}]}, {"value": "DetectOsError", "label": "检测OS异常", "params": [{"type": "el-input", "label": "mark标识", "value": ""}]}, {"value": "DetectVdsAppError", "label": "检测VDS APP异常", "params": [{"type": "el-input", "label": "mark标识", "value": ""}]}, {"value": "CheckCanMsgThreading", "label": "开启检测CAN报文线程"}, {"value": "PushExceptCanMsg", "label": "输入期望Can报文的ID", "params": [{"type": "el-input", "label": "报文ID", "value": ""}, {"type": "el-input", "label": "报文内容", "value": ""}]}, {"value": "TMfingerClick", "label": "同茂机械手指点击"}, {"value": "SwitchBackLight", "label": "切换背光开关", "params": [{"type": "el-select", "label": "操作", "value": "0", "options": [{"value": "0", "label": "息屏"}, {"value": "1", "label": "亮屏"}]}]}, {"value": "SwitchBistPattern", "label": "切换Bist画面", "params": [{"type": "el-select", "label": "操作", "value": "0", "options": [{"value": "0", "label": "显示"}, {"value": "1", "label": "退出"}]}]}, {"value": "DisplayReboot", "label": "显示屏重启"}, {"value": "TconReset", "label": "TCON复位"}, {"value": "ReadBackLightStatus", "label": "读取背光开关状态"}, {"value": "ReadBistPatternStatus", "label": "读取Bist画面状态"}, {"value": "SwitchStepBrightness", "label": "切换步进亮度", "params": [{"type": "el-select", "label": "亮度模式", "value": "1", "options": [{"value": "1", "label": "百分比亮度"}, {"value": "2", "label": "实际亮度"}]}, {"type": "el-input-number", "label": "起始亮度", "value": 0, "min": 0}, {"type": "el-input-number", "label": "终止亮度", "value": 0, "min": 0}, {"type": "el-input-number", "label": "间隔时间(ms)", "value": 0, "min": 0}, {"type": "el-input-number", "label": "步进亮度", "value": 0, "min": 0}]}, {"value": "SetRelayStatus", "label": "设置继电器开关状态", "params": [{"type": "el-input-number", "label": "继电器通道", "value": 1, "min": 1, "max": 16}, {"type": "el-select", "label": "继电器开关", "value": "0", "options": [{"value": "0", "label": "打开"}, {"value": "1", "label": "关闭"}]}]}, {"value": "LightSensorAutoTest", "label": "光感测试", "params": [{"type": "el-select", "label": "测试模式", "value": "percentage", "options": [{"value": "percentage", "label": "百分比"}]}, {"type": "el-input-number", "label": "测量间隔时间(s)", "value": 2, "min": 0}, {"type": "el-input-number", "label": "结果判定值", "value": 0.8, "min": 0}, {"type": "el-input", "label": "自定义画面(R|G|B)", "value": "255|255|255"}]}, {"value": "EnvTemperatureTest", "label": "温升测试", "params": [{"type": "el-select", "label": "设备类型", "value": "TP700", "options": [{"value": "TP700", "label": "TP700"}, {"value": "TP1000", "label": "TP1000"}, {"value": "TP710A", "label": "TP710A"}]}, {"type": "el-input-number", "label": "采集频率(s/次)", "value": 3, "min": 0}, {"type": "el-input-number", "label": "总测试时间(m)", "value": 30, "min": 0}, {"type": "el-input", "label": "采集通道(默认最后一个为环境通道,'|'隔开)", "value": "1|2|3|4"}]}, {"value": "M410Test--GAMMA_CURVE", "label": "使用410测试:Gamma曲线测试", "params": [{"type": "el-select", "label": "采集通道", "value": "DCI-P3", "options": [{"value": "DCI-P3", "label": "DCI-P3"}, {"value": "NTSC", "label": "NTSC"}]}, {"type": "el-select", "label": "通信控制方式", "value": "ADB", "options": [{"value": "ADB", "label": "ADB"}, {"value": "SIMBOX", "label": "SIMBOX"}, {"value": "CAN", "label": "CAN"}]}, {"type": "el-input-number", "label": "CAN通信参数", "value": 0, "min": 0}, {"type": "el-input-number", "label": "最小值", "value": 2.0, "min": 0}, {"type": "el-input-number", "label": "最大值", "value": 2.4, "min": 0}]}, {"value": "M410Test--BRIGHTNESS_CURVE", "label": "使用410测试:亮度曲线测试", "params": [{"type": "el-select", "label": "采集通道", "value": "DCI-P3", "options": [{"value": "DCI-P3", "label": "DCI-P3"}, {"value": "NTSC", "label": "NTSC"}]}, {"type": "el-select", "label": "通信控制方式", "value": "ADB", "options": [{"value": "ADB", "label": "ADB"}, {"value": "SIMBOX", "label": "SIMBOX"}, {"value": "CAN", "label": "CAN"}]}, {"type": "el-input-number", "label": "CAN通信参数", "value": 0, "min": 0}]}, {"value": "M410Test--CONTRAST_RATIO", "label": "使用410测试:对比度测试", "params": [{"type": "el-select", "label": "采集通道", "value": "DCI-P3", "options": [{"value": "DCI-P3", "label": "DCI-P3"}, {"value": "NTSC", "label": "NTSC"}]}, {"type": "el-select", "label": "通信控制方式", "value": "ADB", "options": [{"value": "ADB", "label": "ADB"}, {"value": "SIMBOX", "label": "SIMBOX"}, {"value": "CAN", "label": "CAN"}]}, {"type": "el-input-number", "label": "CAN通信参数", "value": 0, "min": 0}, {"type": "el-input-number", "label": "最小值", "value": 2.0, "min": 0}]}, {"value": "M410Test--COLOUR_GAMUT", "label": "使用410测试:色域测试", "params": [{"type": "el-select", "label": "采集通道", "value": "DCI-P3", "options": [{"value": "DCI-P3", "label": "DCI-P3"}, {"value": "NTSC", "label": "NTSC"}]}, {"type": "el-select", "label": "通信控制方式", "value": "ADB", "options": [{"value": "ADB", "label": "ADB"}, {"value": "SIMBOX", "label": "SIMBOX"}, {"value": "CAN", "label": "CAN"}]}, {"type": "el-input-number", "label": "CAN通信参数", "value": 0, "min": 0}, {"type": "el-input-number", "label": "色域要求", "value": 0.85, "min": 0}]}, {"value": "M410Test--UNIFORMITY", "label": "使用410测试:均一性测试", "params": [{"type": "el-select", "label": "采集通道", "value": "DCI-P3", "options": [{"value": "DCI-P3", "label": "DCI-P3"}, {"value": "NTSC", "label": "NTSC"}]}, {"type": "el-select", "label": "通信控制方式", "value": "ADB", "options": [{"value": "ADB", "label": "ADB"}, {"value": "SIMBOX", "label": "SIMBOX"}, {"value": "CAN", "label": "CAN"}]}, {"type": "el-input-number", "label": "CAN通信参数", "value": 0, "min": 0}, {"type": "el-input-number", "label": "均一性要要求", "value": 0.8, "min": 0}, {"type": "el-color-picker", "label": "均一性测试画面", "value": "#FFFFFF"}]}, {"value": "DrawLine", "label": "划线测试", "params": [{"type": "el-input", "label": "手指编号", "value": "1|2"}, {"type": "el-input-number", "label": "划线次数", "value": 1, "min": 0}]}]