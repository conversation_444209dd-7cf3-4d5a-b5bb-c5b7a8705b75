// 芯片图数据
export  const chipConfigData = 
{
  "KF32A158": {
    "Chip_Info": {
      "GpioNumber": 64,
      "Bind_rule": {
        "gpio": "PIN_MODE_GPIO",
        "adc": "PIN_MODE_ADC_INPUT",
        "pwm": "PIN_MODE_PWM_OUTPUT",
        "iic_SCL": "PIN_MODE_I2C_SCL",
        "iic_SDA": "PIN_MODE_I2C_SDA",
        "spi_CS": "PIN_MODE_SPI_CS",
        "spi_CLK": "PIN_MODE_SPI_SCLK",
        "spi_MOSI": "PIN_MODE_SPI_MOSI",
        "spi_MISO": "PIN_MODE_SPI_MISO",
        "uart_RX": "PIN_MODE_UART_RX",
        "uart_TX": "PIN_MODE_UART_TX",
        "exit": "PIN_MODE_EXINT_IN",
        "other": "PIN_MODE_OTHERS"
      },
      "InterruptPriority_rule": {
        "exit": {
          "preemptPriority": 1,
          "subPriority": 15
        }
      },
      "Derection_rule": {
        "input": [
          "PIN_MODE_ADC_INPUT",
          "PIN_MODE_SPI_MOSI",
          "PIN_MODE_UART_RX",
          "PIN_MODE_EXINT_IN"
        ],
        "output": [
          "PIN_MODE_PWM_OUTPUT",
          "PIN_MODE_I2C_SCL",
          "PIN_MODE_SPI_CS",
          "PIN_MODE_SPI_SCLK",
          "PIN_MODE_SPI_MISO",
          "PIN_MODE_UART_TX"
        ],
        "input_output": [
          "PIN_MODE_GPIO",
          "PIN_MODE_I2C_SDA"
        ]
      },
      "Pin_Config": [
        {
          "id": 1,
          "name": "PD_4",
          "enable": false,
          "default": null,
          "alt_value": "15,4",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_MISO"
        },
        {
          "id": 2,
          "name": "PD_5",
          "enable": false,
          "default": null,
          "alt_value": "15,4",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_SCLK"
        },
        {
          "id": 3,
          "name": "PD_6",
          "enable": false,
          "default": null,
          "alt_value": "15,4",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_CS"
        },
        {
          "id": 4,
          "name": "PD_7",
          "enable": false,
          "default": "None",
          "alt_value": "15,4",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_CS"
        },
        {
          "id": 5,
          "name": "PH_8",
          "enable": true,
          "default": "PIN_MODE_PWM_OUTPUT",
          "alt_value": "15,2,6",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_PWM_OUTPUT,PIN_MODE_MCAN_TX"
        },
        {
          "id": 6,
          "name": "PH_9",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,6",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_MCAN_RX"
        },
        {
          "id": 7,
          "name": "VDD",
          "enable": false,
          "default": "None",
          "alt_value": 15,
          "function_list": "PIN_MODE_OTHERS"
        },
        {
          "id": 8,
          "name": "VDDA",
          "enable": false,
          "default": "None",
          "alt_value": 15,
          "function_list": "PIN_MODE_OTHERS"
        },
        {
          "id": 9,
          "name": "VREF+",
          "enable": false,
          "default": "None",
          "alt_value": 15,
          "function_list": "PIN_MODE_OTHERS"
        },
        {
          "id": 10,
          "name": "VSS",
          "enable": false,
          "default": "None",
          "alt_value": 15,
          "function_list": "PIN_MODE_OTHERS"
        },
        {
          "id": 11,
          "name": "PH_11",
          "enable": false,
          "default": null,
          "alt_value": 15,
          "function_list": "PIN_MODE_GPIO"
        },
        {
          "id": 12,
          "name": "PH_2",
          "enable": false,
          "default": "None",
          "alt_value": 15,
          "function_list": "PIN_MODE_GPIO"
        },
        {
          "id": 13,
          "name": "PH_4",
          "enable": true,
          "default": "PIN_MODE_ADC_INPUT",
          "alt_value": "15,3,14",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_UART_TX,PIN_MODE_ADC_INPUT"
        },
        {
          "id": 14,
          "name": "PH_1",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,4",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_MISO"
        },
        {
          "id": 15,
          "name": "PD_14",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,4",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_SCLK"
        },
        {
          "id": 16,
          "name": "PD_15",
          "enable": true,
          "default": "PIN_MODE_EXINT_IN",
          "alt_value": "15,16",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_EXINT_IN"
        },
        {
          "id": 17,
          "name": "PA_0",
          "enable": false,
          "default": "None",
          "alt_value": "15,9",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_PWM_OUTPUT"
        },
        {
          "id": 18,
          "name": "PA_1",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,4,5",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_CS,PIN_MODE_SPI_CS"
        },
        {
          "id": 19,
          "name": "PA_2",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,4,5",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_MOSI,PIN_MODE_SPI_CS"
        },
        {
          "id": 20,
          "name": "PA_3",
          "enable": false,
          "default": "None",
          "alt_value": "15,3,6",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_UART_TX,PIN_MODE_MCAN_TX"
        },
        {
          "id": 21,
          "name": "PE_7",
          "enable": false,
          "default": "None",
          "alt_value": "15,3,6",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_UART_RX,PIN_MODE_MCAN_RX"
        },
        {
          "id": 22,
          "name": "PE_8",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,3",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_UART_TX"
        },
        {
          "id": 23,
          "name": "PE_9",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,3",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_UART_RX"
        },
        {
          "id": 24,
          "name": "PE_10",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,2",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_PWM_OUTPUT"
        },
        {
          "id": 25,
          "name": "PA_4",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,4,5",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_MOSI,PIN_MODE_I2C_SCL"
        },
        {
          "id": 26,
          "name": "PA_5",
          "enable": false,
          "default": "None",
          "alt_value": "15,4,6",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_MISO,PIN_MODE_SPI_CS"
        },
        {
          "id": 27,
          "name": "PA_8",
          "enable": false,
          "default": null,
          "alt_value": "15,5",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_I2C_SCL"
        },
        {
          "id": 28,
          "name": "PA_9",
          "enable": false,
          "default": null,
          "alt_value": "15,5",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_I2C_SDA"
        },
        {
          "id": 29,
          "name": "PA_11",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,4,5",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_SCLK,PIN_MODE_I2C_SDA"
        },
        {
          "id": 30,
          "name": "PA_13",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,4,5",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_CS,PIN_MODE_I2C_SCL"
        },
        {
          "id": 31,
          "name": "PE_6",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,4,6,14",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_MISO,PIN_MODE_SPI_CS,PIN_MODE_ADC_INPUT"
        },
        {
          "id": 32,
          "name": "PE_0",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,4,6,14",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_SCLK,PIN_MODE_SPI_SCLK,PIN_MODE_ADC_INPUT"
        },
        {
          "id": 33,
          "name": "PB_3",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,3,4,6,14",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_UART_TX,PIN_MODE_SPI_MISO,PIN_MODE_MCAN_TX,PIN_MODE_ADC_INPUT"
        },
        {
          "id": 34,
          "name": "PB_4",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,3,4,6,14",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_UART_RX,PIN_MODE_SPI_CS,PIN_MODE_MCAN_RX,PIN_MODE_ADC_INPUT"
        },
        {
          "id": 35,
          "name": "PF_7",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,3,9",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_UART_TX,PIN_MODE_PWM_OUTPUT"
        },
        {
          "id": 36,
          "name": "PB_6",
          "enable": false,
          "default": null,
          "alt_value": "15,3",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_UART_RX"
        },
        {
          "id": 37,
          "name": "PF_11",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,2,14",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_PWM_OUTPUT,PIN_MODE_ADC_INPUT"
        },
        {
          "id": 38,
          "name": "PB_8",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,4,14",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_CS,PIN_MODE_ADC_INPUT"
        },
        {
          "id": 39,
          "name": "PB_10",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,14",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_ADC_INPUT"
        },
        {
          "id": 40,
          "name": "VSS",
          "enable": false,
          "default": "None",
          "alt_value": 15,
          "function_list": "PIN_MODE_OTHERS"
        },
        {
          "id": 41,
          "name": "VDD",
          "enable": false,
          "default": "None",
          "alt_value": 15,
          "function_list": "PIN_MODE_OTHERS"
        },
        {
          "id": 42,
          "name": "PF_2",
          "enable": true,
          "default": "PIN_MODE_I2C_SCL",
          "alt_value": "15,5,14",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_I2C_SCL,PIN_MODE_ADC_INPUT"
        },
        {
          "id": 43,
          "name": "PF_3",
          "enable": true,
          "default": "PIN_MODE_I2C_SDA",
          "alt_value": "15,5,14",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_I2C_SDA,PIN_MODE_ADC_INPUT"
        },
        {
          "id": 44,
          "name": "PF_5",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,5,14",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_I2C_SDA,PIN_MODE_ADC_INPUT"
        },
        {
          "id": 45,
          "name": "PF_6",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,4,5,14",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_CS,PIN_MODE_I2C_SCL,PIN_MODE_ADC_INPUT"
        },
        {
          "id": 46,
          "name": "PF_8",
          "enable": true,
          "default": "PIN_MODE_GPIO",
          "alt_value": "15,4,14",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_MOSI,PIN_MODE_ADC_INPUT"
        },
        {
          "id": 47,
          "name": "PF_10",
          "enable": true,
          "default": "PIN_MODE_UART_TX",
          "alt_value": "15,3,5",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_UART_TX,PIN_MODE_I2C_SCL"
        },
        {
          "id": 48,
          "name": "PF_12",
          "enable": true,
          "default": "PIN_MODE_UART_RX",
          "alt_value": "15,3,5",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_UART_RX,PIN_MODE_I2C_SDA"
        },
        {
          "id": 49,
          "name": "PG_4",
          "enable": true,
          "default": "PIN_MODE_I2C_SDA",
          "alt_value": "15,5",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_I2C_SDA"
        },
        {
          "id": 50,
          "name": "PC_0",
          "enable": true,
          "default": "PIN_MODE_I2C_SCL",
          "alt_value": "15,5,10",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_I2C_SCL,PIN_MODE_SPI_CS"
        },
        {
          "id": 51,
          "name": "PC_2",
          "enable": false,
          "default": "None",
          "alt_value": "15,3,6",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_UART_TX,PIN_MODE_MCAN_TX"
        },
        {
          "id": 52,
          "name": "PG_9",
          "enable": false,
          "default": "None",
          "alt_value": "15,3,6",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_UART_RX,PIN_MODE_MCAN_RX"
        },
        {
          "id": 53,
          "name": "PG_12",
          "enable": true,
          "default": "PIN_MODE_ADC_INPUT",
          "alt_value": "15,4,14",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_CS,PIN_MODE_ADC_INPUT"
        },
        {
          "id": 54,
          "name": "PG_13",
          "enable": true,
          "default": "PIN_MODE_ADC_INPUT",
          "alt_value": "15,4,14",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_MOSI,PIN_MODE_ADC_INPUT"
        },
        {
          "id": 55,
          "name": "PC_6",
          "enable": true,
          "default": "PIN_MODE_ADC_INPUT",
          "alt_value": "15,14",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_ADC_INPUT"
        },
        {
          "id": 56,
          "name": "PC_10",
          "enable": false,
          "default": "None",
          "alt_value": "15,5,6",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_I2C_SDA,PIN_MODE_MCAN_RX"
        },
        {
          "id": 57,
          "name": "PC_11",
          "enable": false,
          "default": null,
          "alt_value": "15,16",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_EXINT_IN"
        },
        {
          "id": 58,
          "name": "PC_12",
          "enable": false,
          "default": "None",
          "alt_value": 15,
          "function_list": "PIN_MODE_GPIO"
        },
        {
          "id": 59,
          "name": "PC_13",
          "enable": true,
          "default": "PIN_MODE_I2C_SCL",
          "alt_value": "15,4,5,6,13",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_CS,PIN_MODE_I2C_SCL,PIN_MODE_SPI_MISO,PIN_MODE_SPI_MOSI"
        },
        {
          "id": 60,
          "name": "PC_14",
          "enable": true,
          "default": "PIN_MODE_GPIO,PIN_MODE_I2C_SDA",
          "alt_value": "15,4,5,6",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_SPI_MOSI,PIN_MODE_I2C_SDA,PIN_MODE_SPI_SCLK"
        },
        {
          "id": 61,
          "name": "PG_8",
          "enable": true,
          "default": "PIN_MODE_EXINT_IN",
          "alt_value": "15,16",
          "function_list": "PIN_MODE_GPIO,PIN_MODE_EXINT_IN"
        },
        {
          "id": 62,
          "name": "PC_15",
          "enable": false,
          "default": "None",
          "alt_value": 15,
          "function_list": "PIN_MODE_GPIO"
        },
        {
          "id": 63,
          "name": "PD_0",
          "enable": false,
          "default": "None",
          "alt_value": 15,
          "function_list": "PIN_MODE_GPIO"
        },
        {
          "id": 64,
          "name": "PD_1",
          "enable": false,
          "default": "None",
          "alt_value": 15,
          "function_list": "PIN_MODE_GPIO"
        }
      ]
    }
  }
};