<template>
    <!-- <div class="search-container">
        <el-input placeholder="请输入搜索内容" class="search-input">
            <template #append>
                <el-button icon="Search" @click="onSearch" class="search-button">搜索</el-button>
            </template>
        </el-input>
    </div> -->

    <div class="tool-bar-container">
        <el-select v-model="searchParams.status" placeholder="全部" style="width: 120px; margin-right: 20px;"
            @change="onStatusChange">
            <el-option label="全部" value=""></el-option>
            <el-option label="未处理" value="0"></el-option>
            <el-option label="已推送" value="1"></el-option>
            <el-option label="已取消" value="2"></el-option>
        </el-select>

        <el-select v-model="form.project_number" placeholder="所有项目" @change="onFilter" filterable>
            <el-option label="所有项目" value=""></el-option>
            <el-option v-for="item in projects" :label="`${item.name}(${item.code})`" :value="item.code"></el-option>
        </el-select>

        <el-button type="info" plain @click="onFilterStatusChange">
            筛选<el-icon class="el-icon--right">
                <component :is="filterButtonIcon"></component>
            </el-icon>
        </el-button>
    </div>

    <div v-if="showFilterContainer">

        <div class="filter-container">
            <el-input v-model="form.name" size="large" placeholder="请输入名称" suffix-icon="Search"
                @keyup.enter="onFilter"></el-input>
            <el-input v-model="form.tester" size="large" placeholder="请输入测试人" suffix-icon="Search"
                @keyup.enter="onFilter"></el-input>
            <el-input v-model="form.machine_number" size="large" placeholder="请输入机台编号" suffix-icon="Search"
                @keyup.enter="onFilter"></el-input>
        </div>

        <div class="filter-container">
            <el-date-picker v-model="form.start_time" type="datetime" placeholder="开始时间" @change="onFilter" />
            <span style="margin: 0 10px;"> - </span>
            <el-date-picker v-model="form.end_time" type="datetime" placeholder="结束时间" @change="onFilter" />
        </div>

    </div>

    <div class="table-container">
        <el-table :data="tableData" stripe border style="width: 100%">
            <el-table-column prop="project_number" label="项目编号" width="120" align="center"></el-table-column>
            <el-table-column prop="name" label="项目名称" width="250" align="center"></el-table-column>
            <el-table-column prop="occur_time" label="发生时间" width="200" align="center"></el-table-column>
            <el-table-column prop="tester" label="测试人" width="200" align="center"></el-table-column>
            <el-table-column prop="machine_number" label="机台编号" width="120" align="center"></el-table-column>
            <el-table-column label="图片" width="120" align="center">
                <template #default="{ row }">
                    <el-link v-for="(image, index) in row.images" :href="base_url + image" target="_blank">图片{{ index +
                    1 }}&nbsp;</el-link>
                </template>
            </el-table-column>

            <el-table-column label="视频" width="120" align="center">
                <template #default="{ row }">
                    <el-link :href="base_url + row.video" target="_blank">视频</el-link>
                </template>
            </el-table-column>

            <el-table-column label="状态" width="120" align="center">
                <template #default="{ row }">
                    <el-tag v-if="row.status == 0" type="warning">未处理</el-tag>
                    <el-tag v-else-if="row.status == 1" type="success">已推送</el-tag>
                    <el-tag v-else-if="row.status == 2" type="info">已取消</el-tag>
                    <el-tag v-else type="danger">未知</el-tag>
                </template>
            </el-table-column>


            <el-table-column label="操作" min-width="150" fixed="right" align="left">
                <template #default="{ row }">
                    <el-button type="primary" size="small" @click="handlePush(row)"
                        :disabled="row.status != 0">推送</el-button>
                    <el-popconfirm confirmButtonText="确定" cancelButtonText="取消" title="确定取消该问题吗？" placement="top"
                        @confirm="handleCancel(row)">
                        <template #reference>
                            <el-button type="danger" size="small" :disabled="row.status != 0">取消</el-button>
                        </template>
                    </el-popconfirm>
                </template>
            </el-table-column>

        </el-table>
    </div>

    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            :total="total" background @change="onPaginate" />
    </div>

    <el-dialog v-if="dialogPushVisible" v-model="dialogPushVisible" title="推送问题到产品开发平台" width="920"
        :close-on-click-modal="false">
        <IssuePush :r_id="r_id" @affirm="onPushAffirm" @cancel="onPushCancel" />
    </el-dialog>

</template>


<script setup>

import { ref, reactive, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import IssuePush from './push.vue';
import { useRoute } from 'vue-router';

import dayjs from 'dayjs';

const route = useRoute();

const tableData = ref([]);

const base_url = import.meta.env.VITE_BASE_URL;

let r_id = ref(0);

const dialogPushVisible = ref(false);

let form = reactive({
    name: '',
    number: '',
    project_number: '',
});

let total = ref(0);

let searchParams = {
    status: "0",
};

const projects = ref([]);

let showFilterContainer = ref(false);
let filterButtonIcon = ref("ArrowDown");

function onStatusChange(value) {
    Object.assign(searchParams, { status: value })
    update_table(searchParams);
};

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
    if (showFilterContainer.value) {
        filterButtonIcon.value = "ArrowUp";
    } else {
        filterButtonIcon.value = "ArrowDown";
    }
};

function update_table(params) {
    Object.assign(searchParams, params)
    http.get('/issues', { params: searchParams }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });
};

function onSearch() {
    update_table({});
};

function onFilter() {
    let params = {};
    Object.assign(params, form);
    params.start_time = params.start_time ? dayjs(params.start_time).format("YYYY-MM-DD HH:mm:ss") : '';
    params.end_time = params.end_time ? dayjs(params.end_time).format("YYYY-MM-DD HH:mm:ss") : '';
    update_table(params);
};

function onPaginate(currentPage, pageSize) {
    update_table({ page: currentPage, pagesize: pageSize });
};

function handlePush(row) {
    r_id.value = row.id;
    dialogPushVisible.value = true;
};

function handleCancel(row) {
    http.post(`/issues/${row.id}/cancel`).then(res => {
        update_table();
    }).catch(err => {
        console.log(err);
    });
};

function onPushAffirm() {
    dialogPushVisible.value = false;
    update_table();
};

function onPushCancel() {
    dialogPushVisible.value = false;
};

onMounted(() => {
    form.project_number = route.query.project_number;
    update_table(form);

    http.get('/projects/p', { params: { pagesize: 10000 } }).then(res => {
        let data = res.data.data.results;
        projects.value = data;
    }).catch(err => {
        console.log(err);
    });
});

</script>


<style lang="scss" scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    margin-bottom: 10px;

    .el-input,
    .el-date-picker {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;

    .el-select {
        width: 500px;
        margin-right: 10px;
    }

}

</style>