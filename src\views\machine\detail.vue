<template>
    <div>
        <div style="display: flex; align-items: center;">
            <h3>{{ machine.name }}【{{ machine.m_number }}】</h3>
            <el-button @click="onBack" style="margin-left: auto;">返回</el-button>
        </div>

        <el-collapse v-model="activeNames">
            <el-collapse-item title="基本信息" name="1">
                <div class="base_info_container">
                    <el-row>
                        <el-col :span="12">
                            <el-form label-position="right">
                                <el-form-item label="机台名称：">
                                    <span>{{ machine.name }}</span>
                                </el-form-item>
                                <el-form-item label="机台编号：">
                                    <span>{{ machine.m_number }}</span>
                                </el-form-item>
                                <el-form-item label="机台类型：">
                                    <span>{{ machine.type }}</span>
                                </el-form-item>
                                <el-form-item label="机台资产编号：">
                                    <span>{{ machine.number }}</span>
                                </el-form-item>
                                <el-form-item label="机台状态：">
                                    <el-tag v-if="machine.status == 0" type="info">空闲中</el-tag>
                                    <el-tag v-else-if="machine.status == 1" type="success">使用中</el-tag>
                                    <el-tag v-else-if="machine.status == 2" type="warning">报修中</el-tag>
                                    <el-tag v-else-if="machine.status == 3" type="warning">维修中</el-tag>
                                    <el-tag v-else-if="machine.status == 4" type="warning">保养中</el-tag>
                                    <el-tag v-else-if="machine.status == 5" type="danger">报废</el-tag>
                                    <el-tag v-else type="danger">未知</el-tag>
                                </el-form-item>
                            </el-form>
                        </el-col>
                        <el-col :span="12">
                            <el-form label-position="right">
                                <el-form-item label="维护人：">
                                    <span>{{ machine.username }}</span>
                                </el-form-item>
                                <el-form-item label="位置：">
                                    <span>{{ machine.position }}</span>
                                </el-form-item>
                                <el-form-item label="描述：">
                                    <span>{{ machine.desc }}</span>
                                </el-form-item>
                                <el-form-item label="多项目共享：">
                                    <el-tag v-if="machine.is_share" type="success">是</el-tag>
                                    <el-tag v-else type="danger">否</el-tag>
                                </el-form-item>
                            </el-form>
                        </el-col>
                    </el-row>
                </div>
            </el-collapse-item>
            <el-collapse-item title="预约状态" name="2">
                <div class="canlendar-container">
                    <FullCalendar :options="calendarOptions" ref="canlendarRef" />
                </div>
            </el-collapse-item>
            <el-collapse-item title="关联设备" name="3">
                <el-table :data="deviceTableData" style="width: 100%" border>
                    <el-table-column prop="name" label="设备名称" width="180"></el-table-column>
                    <el-table-column prop="type" label="设备类型" width="300"></el-table-column>
                    <el-table-column prop="d_number" label="设备编号" width="180"></el-table-column>
                    <el-table-column label="设备状态" width="180">
                        <template #default="{ row }">
                            <el-tag v-if="row.status == 0" type="success">空闲中</el-tag>
                            <el-tag v-else-if="row.status == 1" type="info">使用中</el-tag>
                            <el-tag v-else-if="row.status == 2" type="warning">报修中</el-tag>
                            <el-tag v-else-if="row.status == 3" type="danger">维修中</el-tag>
                            <el-tag v-else-if="row.status == 4" type="primary">保养中</el-tag>
                            <el-tag v-else-if="row.status == 5" type="danger">报废</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="desc" label="设备描述"></el-table-column>
                </el-table>
            </el-collapse-item>
            <el-collapse-item title="关联功能" name="4">
                <el-table :data="functionTableData" style="width: 100%" border>
                    <el-table-column prop="name" label="功能名称" width="180"></el-table-column>
                    <el-table-column prop="number" label="功能编号" width="300"></el-table-column>
                    <el-table-column prop="desc" label="功能描述"></el-table-column>
                </el-table>
            </el-collapse-item>
        </el-collapse>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import http from '@/utils/http/http.js';
import FullCalendar from '@fullcalendar/vue3'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import listPlugin from '@fullcalendar/list'

const canlendarRef = ref(null);

const route = useRoute();
const router = useRouter();

const activeNames = ref(['1', '2'])

const machine = reactive({
    name: '',
    m_number: '',
    number: '',
    type: '',
    status: '',
    desc: '',
    position: '',
    username: '',
    is_share: '',
});

let functionTableData = ref([]);
let deviceTableData = ref([]);

const calendarOptions = ref({
    plugins: [dayGridPlugin, timeGridPlugin, listPlugin],
    initialView: 'timeGridWeek',
    headerToolbar: {
        left: 'prev,next today',
        center: 'title',
        right: 'dayGridMonth,timeGridWeek,timeGridDay',
    },
    buttonText: {
        today: '今天',
        month: '月',
        week: '周',
        day: '日',
        list: '列表'
    },
    firstDay: 1,
    allDayText: '全天',
    locale: 'zh-cn',
    height: '100%',
    events: (fetchInfo, successCallback, failureCallback) => {
        let start = fetchInfo.startStr;
        let end = fetchInfo.endStr;

        http.get("/machines/reservations", {
            params: {
                machine_id: route.params.id,
                start_time: start,
                end_time: end,
                status_list: [1, 3],
            }
        }).then(res => {
            let events = res.data.data.results.map(item => {
                return {
                    title: item.project + " " + item.user,
                    start: item.start_time,
                    end: item.end_time,
                }
            });
            successCallback(events);
        });
    },
});

function onBack() {
    router.push({ path: '/machines' });
}


onMounted(() => {
    const id = route.params.id;
    http.get("/machines/" + id).then(res => {
        machine.name = res.data.data.name;
        machine.m_number = res.data.data.m_number;
        machine.type = res.data.data.type;
        machine.number = res.data.data.number;
        machine.status = res.data.data.status;
        machine.desc = res.data.data.desc;
        machine.position = res.data.data.position;
        machine.username = res.data.data.username;
        machine.is_share = res.data.data.is_share;

        deviceTableData.value = res.data.data.devices;
        functionTableData.value = res.data.data.functions;
    });
});
</script>

<style scoped>
.base_info_container {
    padding: 20px;
    max-width: 1000px;
}

.canlendar-container {
    width: 900px;
    height: 500px;
    padding: 20px;
}

:deep(.el-collapse-item__arrow) {
    order: -1;
    margin: 0 8px 0 0;
}

::v-deep .el-collapse-item__header {
    font-size: 16px;
}

::v-deep .el-collapse-item__header{
    font-weight: bold;
    font-size: 15px;
}
</style>