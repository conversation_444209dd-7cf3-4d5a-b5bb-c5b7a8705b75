<template>
    <div style="padding: auto;">
        <div style="display: flex;justify-content: space-between;align-items: center;">
            <h2>【{{ role.name }}】的技能</h2>
            <div>
                <el-button @click="handleBack">返回</el-button>
                <el-button type="primary" @click="handleSave">保存</el-button>
            </div>
        </div>
        <el-divider style="margin: 0" />

        <div style="padding: 20px;">
            <div style="margin-top: 30px;">
                <el-button icon="Plus" type="primary" plain @click="handleAdd">添加技能</el-button>
            </div>

            <el-table ref="tableRef" :data="skills" stripe style="width: 100%" class="table-container"
                row-key="skill_id">
                <el-table-column label="序号" width="60" align="center" fixed="left">
                    <template #default="{ row, $index }">
                        {{ $index + 1 }}
                    </template>
                </el-table-column>
                <el-table-column prop="name" label="技能" width="500" align="center" fixed="left">
                    <template #default="{ row }">
                        {{ row.skill_name }}
                    </template>
                </el-table-column>
                <el-table-column label="最大评分" width="300" align="center">
                    <template #default="{ row, $index }">
                        <el-input-number v-model="row.max_score" style="width: 200px;" :min="0"
                            :max="100"></el-input-number>
                    </template>
                </el-table-column>
                <el-table-column label="权重" width="300" align="center">
                    <template #default="{ row }">
                        <el-input-number v-model="row.weight" style="width: 200px;" :min="0"
                            :max="100"></el-input-number>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200" align="left" fixed="right">
                    <template #default="{ row }">
                        <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
                    </template>
                </el-table-column>

            </el-table>
        </div>

        <el-dialog v-if="dialogAddVisible" v-model="dialogAddVisible" title="添加技能" width="600"
            :close-on-click-modal="false">
            <AddSkill :preSkills="preSkills2" @submit="onAddSubmit" @cancel="dialogAddVisible = false" />
        </el-dialog>

    </div>

</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import http from '@/utils/http/http.js';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import AddSkill from './addSkill.vue';
import Sortable from 'sortablejs';

const tableRef = ref(null);
const dialogAddVisible = ref(false);
const route = useRoute();
const router = useRouter();
const role = ref({});
const skills = ref([]);
const preSkills = ref([]);

const handleBack = () => {
    router.push('/tester_roles');
};

const handleSave = () => {
    let id = route.params.id;
    let data = skills.value.map((item, index) => {
        return {
            role_id: id,
            skill_id: item.skill_id,
            max_score: item.max_score,
            weight: item.weight,
            order: index,
        };
    });

    http.post(`/testers/roles/${id}/skills`, { skills: data }).then(res => {
        ElMessage.success('保存成功');
    });

};

const handleAdd = () => {
    dialogAddVisible.value = true;
};

const handleDelete = (row) => {
    let index = skills.value.findIndex(item => item.skill_id === row.skill_id);
    skills.value.splice(index, 1);
};

const onAddSubmit = (data) => {
    skills.value.push(data);
    dialogAddVisible.value = false;
};

const preSkills2 = computed(() => {
    return preSkills.value.filter(item => !skills.value.map(skill => skill.skill_id).includes(item.id));
});


onMounted(() => {
    const sortable = new Sortable(tableRef.value.$el.querySelector('tbody'), {
        handle: '.el-table__row',
        animation: 150,
        onEnd: ({ newIndex, oldIndex }) => {
            const item = skills.value.splice(oldIndex, 1)[0];
            skills.value.splice(newIndex, 0, item);
        },
    });


    let id = route.params.id;
    http.get(`/testers/roles/${id}`).then(res => {
        let data = res.data.data;
        role.value = data;
    });

    http.get(`/testers/roles/${id}/skills`).then(res => {
        let data = res.data.data;
        skills.value = data;
    });

    http.get('/testers/skills', { params: { pagesize: 100000, department_re: "系统测试部"} }).then(res => {
        preSkills.value = res.data.data.results;
    });

});

</script>

<style lang="scss" scoped>
.base-info {
    padding-left: 20px;
    padding-top: 10px;
    padding-bottom: 10px;
    max-width: 1000px;
}

.base-info .el-form-item {
    margin: 0px;
}

.role-info {
    padding: 20px;
}
</style>