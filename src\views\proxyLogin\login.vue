<template>

</template>

<script setup>
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user.js';
import axios from 'axios';
import { useRoute } from 'vue-router';

let userStore = useUserStore();

let router = useRouter();
let route = useRoute();

onMounted(() => {
    axios.post(import.meta.env.VITE_BASE_URL + '/users/login', {
        code: route.query.code, 
        model: 'web'
    }).then(res => {
        let data = res.data.data;
        sessionStorage.setItem('token', data.access_token);
        userStore.update(data.user_info || {});

        let redirectUrl = localStorage.getItem('redirectUrl');
        if (redirectUrl) {
            router.push(redirectUrl);
            localStorage.removeItem('redirectUrl');
        } else {
            router.push('/');
        }
    }).catch(err => {
        console.log(err);
    });
});

</script>