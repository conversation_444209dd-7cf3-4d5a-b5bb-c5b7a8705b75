<template>
    <div class="timeline-container">
        <div ref="timelineRef" id="timeline"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import "vis-timeline/styles/vis-timeline-graph2d.min.css";
import { DataSet } from 'vis-data';
import { Timeline } from 'vis-timeline';
import moment from 'moment';
import "moment/dist/locale/zh-cn.js";

let timelineRef = ref(null);

const props = defineProps({
    config: {
        type: Object,
        default: () => { },
    },
    items: {
        type: Array,
        default: () => [],
    },
    groups: {
        type: Array,
        default: () => [],
    },
});

let options = {
    locale: 'zh-cn',
    moment: moment,
    stack: false,
    orientation: 'top',
    showCurrentTime: true,
    moment: function (date) {
        return moment(date).locale('zh-cn');
    },
};

let items = new DataSet(props.items);
let groups = new DataSet(props.groups);

watch(() => props.items, (newItems) => {
    items.clear();
    items.add(newItems);
    console.log('items', items);
});

watch(() => props.groups, (newGroups) => {
    groups.clear();
    groups.add(newGroups);
    console.log('groups', groups);
});

onMounted(() => {
    const timeline = new Timeline(timelineRef.value, items, groups, options);
});

</script>