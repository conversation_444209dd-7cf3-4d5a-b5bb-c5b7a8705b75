<template>
    <el-tree :data="treeData" :props="{ label: 'name', value: 'number' }" show-checkbox @check="onCheck" check-strictly
        ref="treeRef" check-on-click-node :expand-on-click-node="false">

        <template #default="{ node, data }">
            <span>{{ node.label }}</span>
        </template>

    </el-tree>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http/http.js';

const treeData = ref([]);
const treeRef = ref(null);

const emit = defineEmits(['check']);

const onCheck = (curData, checkedData) => {
    let module1 = checkedData.checkedNodes.filter(node => node.level == 1).map(node => node.number2);
    let module2 = checkedData.checkedNodes.filter(node => node.level == 2).map(node => node.number2);
    let module3 = checkedData.checkedNodes.filter(node => node.level == 3).map(node => node.number2);

    emit('check', module1, module2, module3);
};

function filterItems(items) {
    items = items.filter(item => item.deprecated === false);

    items.forEach(item => {
        if (item.children && item.children.length > 0) {
            item.children = filterItems(item.children);
        }
    });

    return items;
};

onMounted(() => {
    http.get('/functions').then(res => {
        let items = res.data.data.results;
        items = filterItems(items);
        treeData.value = items;
    });
});

</script>

<style scoped>
:deep(.el-tree-node__content) {
    height: 40px;
}
</style>