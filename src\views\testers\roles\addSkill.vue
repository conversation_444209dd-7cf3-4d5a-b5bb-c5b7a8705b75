<template>
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="技能" prop="skill_id">
                <el-select v-model="form.skill_id" placeholder="请选择技能">
                    <el-option v-for="item in props.preSkills" :key="item.id" :label="item.name" :value="item.id" :disabled="item.disabled" />
                </el-select>
            </el-form-item>

            <el-form-item label="最大评分" prop="max_score">
                <el-input-number v-model="form.max_score" style="width: 100%;"></el-input-number>
            </el-form-item>

            <el-form-item label="权重" prop="weight">
                <el-input-number v-model="form.weight" style="width: 100%;"></el-input-number>
            </el-form-item>

            <div class="submit-button-container">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
    preSkills: Array,
});

const formRef = ref(null);

const form = ref({
    skill_id: '',
    max_score: 10,
    weight: 10,
});

const rules = ref({
    skill_id: [
        { required: true, message: '请选择技能', trigger: 'change' },
    ],
    max_score: [
        { required: true, message: '请输入最大评分', trigger: 'change' },
    ],
    weight: [
        { required: true, message: '请输入权重', trigger: 'change' },
    ],
});

const emit = defineEmits(['submit', 'cancel'])

const onSubmit = () => {
    formRef.value.validate((valid) => {

        form.value.skill_name = props.preSkills.find(item => item.id === form.value.skill_id).name;

        if (valid) {
            emit('submit', form.value);
        };
    });
};

const onCancel = () => {
    emit('cancel');
};

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>