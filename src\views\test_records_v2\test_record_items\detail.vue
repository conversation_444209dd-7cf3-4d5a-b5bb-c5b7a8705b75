<template>
    <div class="n-container">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/test_records_v2' }">测试记录</el-breadcrumb-item>
            <el-breadcrumb-item
                :to="{ path: '/test_records_v2/items', query: { id: test_record_item.raw?.test_record_id } }">记录项</el-breadcrumb-item>
            <el-breadcrumb-item>详情</el-breadcrumb-item>
        </el-breadcrumb>
    </div>

    <div class="header-container">
        <h2>{{ test_record_item.raw?.test_case_name }}</h2>
        <div>
            <template v-if="!$route.query.back_target">
                <span v-if="!jump_flag" style="margin-right: 10px;">{{ current_index + ' / ' + count }}</span>
                <el-button v-if="!jump_flag && current_index > 1" size="small" @click="onLast">上一个</el-button>
                <el-button v-if="!jump_flag && current_index < count" size="small" @click="onNext">下一个</el-button>
            </template>
            <el-button v-if="!jump_flag" size="small" @click="onBack">返回</el-button>
        </div>
    </div>
    <div class="tool-container ">
        <!-- <el-button :disabled="!(test_record_item.result === null)" text bg icon="Pointer"
            @click="handleDecide">判定</el-button> -->
        <el-button :disabled="!(test_record_item.result === false && test_record_item.status === 0)" text bg
            icon="Pointer" @click="handlePush">上报问题</el-button>
        <el-button :text bg icon="download" @click="handleDownloadGraphData">下载图表数据</el-button>
        <!-- <el-button :disabled="!(test_record_item.result === false && test_record_item.status === 0)" text bg
            icon="Pointer" @click="handleFasleAlarm">误判</el-button> -->
    </div>

    <el-collapse v-model="activeNames" class="record-detail">
        <el-collapse-item title="状态信息" name="1">
            <div style="max-width: 1200px; margin-left: 20px;">
                <el-row>
                    <el-col :span="12">
                        <el-form label-position="right">
                            <el-form-item label="测试结果：">
                                <el-tag v-if="test_record_item.result === null" type="warning">待判定</el-tag>
                                <el-tag v-else-if="test_record_item.result" type="success">PASS</el-tag>
                                <el-tag v-else type="danger">NG</el-tag>
                            </el-form-item>
                            <el-form-item label="处理状态：">
                                <el-tag v-if="test_record_item.status == 0" type="warning">待处理</el-tag>
                                <el-tag v-else-if="test_record_item.status == 1" type="success">已处理</el-tag>
                            </el-form-item>
                        </el-form>
                    </el-col>
                    <el-col :span="12">
                        <el-form label-position="right">
                            <el-form-item label="测试时间：">
                                <span>{{ test_record_item.start_time + " ~ " + test_record_item.end_time }}</span>
                            </el-form-item>
                            <el-form-item label="测试人员：">
                                <span>{{ test_record_item.tester_name }}</span>
                            </el-form-item>
                        </el-form>
                    </el-col>
                </el-row>
            </div>
        </el-collapse-item>
        <el-collapse-item title="用例信息" name="2">
            <div style="max-width: 1200px; margin-left: 20px;">
                <el-row>
                    <el-col :span="12">
                        <el-form label-position="right">
                            <el-form-item label="项目名称：">
                                <span>{{ test_record_item.project_name }}</span>
                            </el-form-item>
                            <el-form-item label="项目编号：">
                                <span>{{ test_record_item.project_number }}</span>
                            </el-form-item>
                            <el-form-item label="机台编号：">
                                <span>{{ test_record_item.machine_number }}</span>
                            </el-form-item>
                        </el-form>
                    </el-col>
                    <el-col :span="12">
                        <el-form label-position="right">
                            <el-form-item label="用例名称：">
                                <span>{{ test_record_item.test_case_name }}</span>
                            </el-form-item>
                            <el-form-item label="用例编号：">
                                <span>{{ test_record_item.test_case_number }}</span>
                            </el-form-item>
                            <el-form-item label="用例版本：">
                                <span>V{{ test_record_item.test_case_version }}.0</span>
                            </el-form-item>
                        </el-form>
                    </el-col>
                </el-row>
            </div>
        </el-collapse-item>
        <el-collapse-item title="测试步骤详情" name="3">
            <div class="table-container">
                <el-table ref="stepTableRef" :data="stepData" stripe border style="width: 100%" row-key="id"
                    height="600">

                    <el-table-column label="序号" min-width="100" align="center">
                        <template #default="{ row, $index }">
                            {{ $index + 1 }}
                        </template>
                    </el-table-column>
                    <el-table-column label="步骤类型" min-width="120" align="center">
                        <template #default="{ row }">
                            <span v-if="row.step_type == 'CAN'">CAN报文</span>
                            <span v-else-if="row.step_type == 'LIN'">LIN报文</span>
                            <span v-else-if="row.step_type == 'I2C'">I2C指令</span>
                            <span v-else-if="row.step_type == 'CustomizeCMD'">自定义指令</span>
                            <span v-else-if="row.step_type == 'MANUAL'">手动执行</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="步骤指令" prop="command" min-width="200" align="center"></el-table-column>
                    <el-table-column label="步骤参数" prop="params" min-width="400" align="center">
                    </el-table-column>
                    <el-table-column prop="expect" label="期望值" min-width="200" align="center"></el-table-column>
                    <el-table-column prop="step_actual" label="实测值" min-width="200" align="center"></el-table-column>
                    <el-table-column prop="step_result" label="测试结果" min-width="200" align="center">
                        <template #default="{ row }">
                            <span
                                :style="{ color: row.step_result === 'PASS' ? '#008000' : (row.step_result === 'NG' ? 'red' : 'inherit') }">
                                {{ row.step_result }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="start_time" label="开始时间" min-width="200" align="center"></el-table-column>
                    <el-table-column prop="end_time" label="结束时间" min-width="200" align="center"></el-table-column>

                </el-table>
            </div>
        </el-collapse-item>
        <el-collapse-item title="图表" name="4">
            <Graph v-for="d in graph_datas" :graph_data="d" />
        </el-collapse-item>
        <el-collapse-item title="关联问题" name="5">
            <el-table :data="bugs" stripe style="width: 100%" row-key="id" height="200">

                <el-table-column prop="num" label="问题编号" min-width="200" align="center"></el-table-column>
                <el-table-column prop="title" label="问题名称" min-width="300" align="center">
                    <template #default="{ row }">
                        <el-link type="primary" :href="'https://ipd.hiway.com:56289/project-client/#/issueManage/all/detail/' + row.id" target="_blank">{{ row.title }}</el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" min-width="200" align="center">
                    <template #default="{ row }">
                        <el-tag v-if="row.status == 'OPEN'" type="success">打开</el-tag>
                        <el-tag v-else-if="row.status == 'DOING'" type="success">进行中</el-tag>
                        <el-tag v-else-if="row.status == 'DONE'" type="success">已解决</el-tag>
                        <el-tag v-else-if="row.status == 'KEEP'" type="success">保留</el-tag>
                        <el-tag v-else-if="row.status == 'UNTREATED'" type="success">不处理</el-tag>
                        <el-tag v-else-if="row.status == 'CLOSED'" type="success">关闭</el-tag>
                        <el-tag v-else-if="row.status == 'REOPEN'" type="success">重新打开</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right" align="center">
                    <template #default="{ row, $index }">
                        <el-button type="primary" size="small" @click="handleAddTestLog(row)">添加记录</el-button>
                    </template>
                </el-table-column>

            </el-table>

        </el-collapse-item>
        <el-collapse-item title="关联资料" name="6">
            <el-table ref="stepTableRef" :data="resources" stripe style="width: 100%" row-key="id" height="600">

                <el-table-column prop="resource_name" label="关联资源名称" width="200" align="center"></el-table-column>
                <el-table-column prop="resource_path" label="关联资源url" min-width="200" align="center">
                    <template #default="{ row }">
                        <el-link type="primary" :href="row.resource_path" target="_blank">{{ row.resource_path
                            }}</el-link>
                    </template>
                </el-table-column>

            </el-table>
        </el-collapse-item>
    </el-collapse>


    <el-dialog v-if="dialogDecideVisible" v-model="dialogDecideVisible" title="人工判定测试项结果" width="800"
        :close-on-click-modal="false" center>
        <Decide @pass="onDecidePass" @ng="onDecideNg" @cancel="onDecideCancel" :r_id="test_record_item_id" />
    </el-dialog>

    <el-dialog v-if="dialogFalseAlarmVisible" v-model="dialogFalseAlarmVisible" title="测试项结果误判" width="800"
        :close-on-click-modal="false">
        <FalseAlarm @cancel="onFalseAlarmCancel" @confirm="onFalseAlarmConfirm" :r_id="test_record_item_id" />
    </el-dialog>

    <el-dialog v-if="dialogPushVisible" v-model="dialogPushVisible" width="920" :close-on-click-modal="false">
        <template #title>
            <div style="font-size:18px;font-weight: bold;">上报问题到产品开发平台</div>
        </template>
        <IssuePush :r_id="test_record_item_id" @confirm="onPushConfirm" @cancel="onPushCancel" />
    </el-dialog>

    <el-dialog v-if="dialogAddTestLogVisible" v-model="dialogAddTestLogVisible" title="问题添加测试记录" width="800"
        :close-on-click-modal="false">
        <addTestLog @cancel="dialogAddTestLogVisible = false" @confirm="dialogAddTestLogVisible = false" :bug_id="bug_id" />
    </el-dialog>
</template>


<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import http from '@/utils/http/http.js';
import { useRouter, useRoute } from 'vue-router';
import Decide from './decide.vue';
import FalseAlarm from './falseAlarm.vue';
import IssuePush from './push.vue';
import Graph from './graph.vue';
import addTestLog from './addTestLog.vue';

const dialogDecideVisible = ref(false);
const dialogFalseAlarmVisible = ref(false);
const dialogPushVisible = ref(false);
const activeNames = ref(['1', '2', '3']);
const router = useRouter();
const route = useRoute();
const dialogAddTestLogVisible = ref(false);
const bug_id = ref(null);

const test_record_item = ref({});
const test_plan = ref(null);
const count = ref(parseInt(route.query.count));
const current_index = ref(parseInt(route.query.current));
const resources = ref([]);
const bugs = ref([]);
const jump_flag = ref(false);
const graph_datas = computed(() => {
    let datas = test_record_item.value.raw?.function_test_result;
    return datas || [];
})

if (route.query.q) {
    var query = JSON.parse(route.query.q);
} else {
    var query = {};
    jump_flag.value = true;
}

const test_record_item_id = computed(() => {
    return Number(route.params.id);
});

watch(test_record_item_id, () => {
    if (!test_record_item_id.value) {
        resources.value = [];
        return;
    }
    http.get(`/v2/test_records/items/resources`, { params: { test_record_item_id: test_record_item_id.value } }).then(res => {
        resources.value = res.data.data.results;
    });
}, { immediate: true });

watch(() => test_record_item.value.test_case_number, () => {
    if (!test_record_item.value.test_case_number) {
        bugs.value = [];
        return;
    }
    http.get(`/bugs`, { params: { test_case_number: test_record_item.value.test_case_number, project_number:  test_record_item.value.project_number} }).then(res => {
        bugs.value = res.data.data.results;
    });
}, { immediate: true });

const test_plan_id = computed(() => {
    return test_record_item.value.test_plan_id;
});
const test_case_id = computed(() => {
    return test_record_item.value.test_case_id;
});
const stepData = computed(() => {
    return test_record_item.value.raw?.steps;
});
const test_case = computed(() => {
    if (test_plan.value && test_plan_id.value) {
        return test_plan.value.test_cases.find(item => item.id === test_case_id.value);
    } else {
        return {};
    }
});

function onLast() {
    if (current_index.value > 1) {
        let params = {
            ...query
        };
        params.page = current_index.value - 1;
        params.pagesize = 1;
        params.test_record_id = test_record_item.value.raw.test_record_id;
        http.get("/v2/test_records/items", { params }).then(res => {
            if (res.data.data.count == count.value) {
                let id = res.data.data.results[0].id;
                router.push({ path: `/test_records_v2/items/${id}`, query: { q: JSON.stringify(query), count: count.value, current: current_index.value - 1 } });
            } else {
                let id = res.data.data.results[0].id;
                router.push({ path: `/test_records_v2/items/${id}`, query: { q: JSON.stringify(query), count: count.value - 1, current: current_index.value - 1 } });
            }
        });
    }
};

function onNext() {
    if (current_index.value < count.value) {
        let params = {
            ...query,
        };
        params.page = current_index.value + 1;
        params.pagesize = 1;
        params.test_record_id = test_record_item.value.raw.test_record_id;
        http.get("/v2/test_records/items", { params }).then(res => {
            console.log(res.data.data.count, count.value);
            console.log(res.data.data);
            if (res.data.data.count == count.value) {
                let id = res.data.data.results[0].id;
                router.push({ path: `/test_records_v2/items/${id}`, query: { q: JSON.stringify(query), count: count.value, current: current_index.value + 1 } });
            } else {
                current_index.value -= 1;
                count.value -= 1;
                onNext();
            }
        });
    }
};

function onBack() {
    var back_target = route.query.back_target;

    if (back_target) {
        router.push(back_target);
    } else {
        router.push({ path: "/test_records_v2/items/", query: { id: test_record_item.value.raw.test_record_id, f: route.query.f } });
    }
};

function handleAddTestLog(row) {
    bug_id.value = row.id;
    dialogAddTestLogVisible.value = true;
};


function handleDecide() {
    dialogDecideVisible.value = true;
};

function onDecideCancel() {
    dialogDecideVisible.value = false;
};

function onDecidePass() {
    dialogDecideVisible.value = false;
    update_detail();
};

function onDecideNg() {
    dialogDecideVisible.value = false;
    update_detail();
};

function handleFasleAlarm() {
    dialogFalseAlarmVisible.value = true;
};

function onFalseAlarmCancel() {
    dialogFalseAlarmVisible.value = false;
};

function onFalseAlarmConfirm() {
    dialogFalseAlarmVisible.value = false;
    update_detail();
};

function handlePush() {
    dialogPushVisible.value = true;
};

function onPushConfirm() {
    dialogPushVisible.value = false;
    update_detail();
};

function onPushCancel() {
    dialogPushVisible.value = false;
};

function update_detail() {
    http.get(`/v2/test_records/items/${test_record_item_id.value}`).then(res => {
        test_record_item.value = res.data.data;
    });
};

function handleDownloadGraphData() {
    window.open(import.meta.env.VITE_BASE_URL + `/v2/test_records/${route.params.id}/download`);
};

onMounted(() => {
    http.get(`/v2/test_records/items/${test_record_item_id.value}`).then(res => {
        test_record_item.value = res.data.data;
        http.get(`/v2/test_plans/${res.data.data.test_plan_id}`).then(res => {
            test_plan.value = res.data.data;
        });
    });
});

</script>

<style lang="scss" scoped>
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tool-container {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
}

:deep(.el-collapse-item__arrow) {
    order: -1;
    margin: 0 8px 0 0;
}

.record-detail :deep(.el-form-item) {
    margin-bottom: 0;
}

:deep(.el-collapse-item__header) {
    font-size: 16px;
    font-weight: 600;
}
</style>