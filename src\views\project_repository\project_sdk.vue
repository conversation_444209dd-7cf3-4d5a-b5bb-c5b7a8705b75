<template>
  <div class="codemangement-container" v-custom-loading="loading">
    <div class="header-container">
      <div class="title-container">
        <span class="main-title">SDK仓库配置</span>
      </div>
      <div class="logo-container">
        <el-icon class="logo-icon"><StarFilled /></el-icon>
      </div>
    </div>
    <form>
      <!-- SDK GitLab -->
      <div :disabled="isInitialized" class="form-group">
        <span class="label">SDK GitLab：</span>
        <input
          v-model="formData.sdkGitLab"
          type="text"
          placeholder="http://*********/mcu-team/sdk_code/hiwaysdk_2.0"
          class="input-field"
          :disabled="true"
        />
      </div>

      <!-- SDK Branch -->
      <div :disabled="isInitialized" class="form-group">
        <span class="label">SDK Branch：</span>
        <el-select v-model="formData.sdkBranch" class="input-field" placeholder="请选择分支名称" style="border: none; padding: 0px;" filterable clearable :disabled="isInitialized || formData.branchDisabled">
          <el-option v-for="branch in branches" :key="branch" :label="branch" :value="branch">{{ branch }}</el-option>
        </el-select>
      </div>

      <!-- SDK Branch -->
      <div :disabled="isInitialized" class="form-group">
        <span class="label">SDK Tag:</span>
        <el-select v-model="formData.sdkTag" class="input-field" placeholder="请选择release的tag" style="border: none; padding: 0px;" filterable clearable :disabled="isInitialized || formData.tagDisabled">
          <el-option v-for="tag in tags" :key="tag" :label="tag" :value="tag">{{ tag }}</el-option>
        </el-select>
      </div>


      <!-- 项目组 -->
      <div class="form-group" :disabled="isInitialized">
        <span class="label">项目组：</span>
        <el-select
          v-model="formData.group"
          class="input-field"
          placeholder="请选择工作组"
          filterable
          clearable
          style="border: none; padding: 0px;"
          :disabled="isInitialized" 
        >
          <el-option
            v-for="group in teamdata"
            :key="group"
            :label="group"
            :value="group"
          >{{ group }}</el-option>
        </el-select>
      </div>

      <!-- 仓库 -->
      <div class="form-group" :disabled="isInitialized">
        <span class="label">仓库：</span>
        <el-input v-model="formData.space" class="input-field" placeholder="请输入仓库名称" style="border: none; padding: 0px;" filterable clearable/>
        
      </div>

      <!-- 初始化按钮 -->
      <div class="button-container">
        <button @click.prevent="openInitDialog">初始化</button>
      </div>
      <!-- 初始化弹窗 -->
      <el-dialog
        v-model="dialogInitVisible"
        title="初始化配置"
        width="45%"
        center
      >
      

        <!-- 芯片选择 -->
        <div class="form-group ini_form_group">
          <span class="label">芯片：</span>
          <el-select
            v-model="formData.selectedChip"
            class="input-field"
            placeholder="请选择芯片"
            style="border: none; padding: 0px;"
          >
            <el-option
              v-for="(chip, index) in chipList"
              :key="index"
              :label="chip"
              :value="chip"
            >{{ chip }}</el-option>
          </el-select>
        </div>
        <!-- 功能模块 -->
        <div class="form-group ini_form_group" >
          <span class="label">功能模块：</span>
          <div class="checkbox-container">
            <div
              v-for="(module, index) in functionalModules"
              :key="index"
              class="checkbox-item"
              style="border: none;"
            >
              <input
                type="checkbox"
                v-model="formData.selectedModules"
                :value="module"
                class="checkbox"
              />
              <span class="module-label">{{ module }}</span>
            </div>
          </div>
        </div>

        
        <template #footer>
          <el-button @click="cancelCreation">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </template>
      </el-dialog>
    </form>
  </div>
</template>


<script setup>
import { onMounted,reactive, ref, watch } from 'vue';
import http from '@/utils/http/http';
import { ElMessage } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
import { useProjectStore } from '@/stores/project';

const router = useRouter();



// 使用 reactive 定义表单数据对象
let formData = reactive({
  sdkGitLab: "http://*********/mcu-team/sdk_code/hiwaysdk_2.0",
  sdkBranch: "",
  selectedChip: "",
  selectedModules: [],
  group: '',
  space: '',
   branchDisabled: false, // 控制SDK Branch是否禁用
  tagDisabled: false,   // 控制SDK Tag是否禁用
});



let branches = ref([]); // 示例分支列表
let tags = ref([]); // 示例tag列表

// 功能模块初始化状态
let isInitialized = ref(false);
// 提交功能状态
let submit_status = ref(false);

let init_status = ref();
let message = ref();

let pick_chips = ref([]);

let config_info = ref({});
let config = ref({})
let pick_info = ref({});


let pick_tree_info = ref([]);
let workspace_path = "";
let branch_status = "";
// 项目号
let project_code = "";
// 项目名称
let project_name = "";

// 初始项目号
let init_project_code = "";
let init_project_name = "";


const loading=ref(true);

let teamdata = ref([]);
let projectOptions = ref([]);
let branchOptions = ref([]);

onMounted(() => {
  console.log("GitLab 地址信息：", formData.sdkGitLab);


  // 使用 Promise.all 并行发送请求
  Promise.all([
    // 获取 GitLab 分支信息
    http.get('/code_management/get_branches', {
      params: { sdkGitLab: formData.sdkGitLab }
    }),
    // 获取项目组数据
    retryRequest(() => http.get("/code_management/project_submit", { timeout: 60000 }), 3, 2000)
      ]).then(([branchesResponse, teamDataResponse]) => {
    // 处理分支数据
    if (branchesResponse.data.status === 1) {
      branches.value = branchesResponse.data.branches;
      tags.value =branchesResponse.data.tags;
      console.log("获取到的分支信息：", branchesResponse.data.branches);
    } else {
      console.error("获取分支信息失败:", branchesResponse.data);
    }

    // 处理项目组数据
    if (teamDataResponse.data.project_status === 1) {
      teamdata.value = teamDataResponse.data.subgroup_paths;
      console.log("获取到的项目组信息：", teamDataResponse.data.subgroup_paths);
    } else {
      console.error("获取项目组信息失败:", teamDataResponse.data);
    }

     // 完成请求后，关闭 loading
    loading.value = false;
  }).catch(error => {
    console.error("获取数据失败:", error);
    handleError(error);
     // 完成请求后，关闭 loading
    loading.value = false;
  });

  // 获取项目数据
  const projectStore = useProjectStore();
  console.log("projectStore:", projectStore.project_info);
  project_code = projectStore.project_info.projectCode;
  project_name = projectStore.project_info.name;
  console.log('页面加载时项目号：', project_code);
  console.log('页面加载时项目名称：', project_name);

  watch(() => projectStore.project_info, (newval, oldval) => {
    console.log('项目信息更新：', newval);
    project_code = newval.projectCode;
    project_name = newval.name;
    console.log('当前项目号：', project_code);
    console.log('当前项目名称：', project_name);

    // 清空项目组，工作空间，分支
    formData.group = '';
    formData.space = '';
  });

// 添加互斥选择监听
  watch(
    () => formData.sdkBranch,
    (newValue) => {
      if (newValue) {
        // 选择了Branch，禁用Tag并清空Tag选择
        formData.tagDisabled = true;
        formData.sdkTag = '';
      } else {
        // 取消选择Branch，启用Tag
        formData.tagDisabled = false;
      }
    }
  );

  watch(
    () => formData.sdkTag,
    (newValue) => {
      if (newValue) {
        // 选择了Tag，禁用Branch并清空Branch选择
        formData.branchDisabled = true;
        formData.sdkBranch = '';
      } else {
        // 取消选择Tag，启用Branch
        formData.branchDisabled = false;
      }
    }
  );



});






// 错误处理函数
function handleError(error) {
  if (error.response) {
    ElMessage.error(`请求失败: 状态码 ${error.response.status} - ${error.response.data.message || '未知错误'}`);
  } else if (error.request) {
    ElMessage.error('请求失败: 没有收到服务器响应');
  } else {
    ElMessage.error('请求失败: ' + error.message);
  }
}




const dialogInitVisible = ref(false);
// 功能模块
let functionalModules = ref([]);
// 芯片
const chipList = ref([])


let currentMessage = null; // 用于存储当前消息实例
// 初始化按钮的功能
const openInitDialog = () => {
// console.log("表单数据:", formData);
if (!formData.sdkGitLab.trim() || !formData.space || !formData.group || (!formData.sdkBranch && !formData.sdkTag))
{
   // 关闭之前的消息并显示新消息
    if (currentMessage) {
      currentMessage.close();
    }
  currentMessage = ElMessage.warning('请填写表单信息后再进去初始化');
  
} else {

          // 项目信息初始化
          init_project_code = project_code
          init_project_name = project_name
          console.log("init时选择的项目信息:", init_project_code, init_project_name);
          // 打开弹窗时加载数据
          
          loading.value = true;

          if (init_project_code !== undefined && init_project_name !== undefined) {
              // 初始化请求grpc InitProject
                // initProject()
                // 使用 Axios 实例发送 GET 请求
                console.log("formData.sdkTag:", formData.sdkTag)
                console.log("formData.sdkBranch:", formData.sdkBranch)
                retryRequest(() =>http.post('/code_management/init_project',{
                  params:{
                    sdkGitLab: formData.sdkGitLab,
                    sdkBranch: formData.sdkBranch,
                    sdkTag: formData.sdkTag,
                    group: formData.group,
                    space: formData.space,
                    // 项目信息
                    project_code: init_project_code,
                    project_name: init_project_name,
                  }
                }, {timeout: 60000}
                ), 1, 2000).then(response => {
                      console.log("初始化操作信息:", response.data);
                      if (response.data.init_status === 1) {
                        // 初始化成功， 取消加载动画
                        loading.value = false
                      
                        // 设置gitlab 地址和分支功能不可选, 以及功能模块的选择功能状态为 true
                        isInitialized.value = true;
                        functionalModules.value = response.data.func;
                        pick_chips.value = response.data.pick_chips;
                        // 勾选固定模块
                        formData.selectedModules = pick_chips.value.slice();
                        chipList.value = response.data.chip_list;
                        // 显示弹框
                        dialogInitVisible.value = true;
                        
                      } else {
                        // 关闭之前的消息并显示新消息
                        if (currentMessage) {
                          currentMessage.close();
                        }
                      currentMessage = ElMessage.error('初始化失败');
                        loading.value = false;
                      }
                }).catch(error => { 
                  console.error("初始化失败:", error);
                   // 关闭之前的消息并显示新消息
                        if (currentMessage) {
                          currentMessage.close();
                        }
                      currentMessage = ElMessage.error('初始化失败')
                  loading.value = false;
                });
          } else {
             // 关闭之前的消息并显示新消息
              if (currentMessage) {
                currentMessage.close();
              }
            currentMessage = ElMessage.warning('请选择项目信息，再进行初始化')
            loading.value = false
          }
          
        }
      
};




// 自动重试封装函数
async function retryRequest(fn, retries = 3, delay = 1000) {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      if (attempt < retries) {
        console.warn(`请求失败，正在重试（${attempt}/${retries})...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        throw error;
      }
    }
  }
}






//取消按钮的功能
const cancelCreation = () => {
  router.push('/projects/project_index')
}

// 提交按钮的功能
const handleSubmit = () => {
console.log("表单数据:", formData);
// 非空校验
if (!formData.selectedModules.length) {
  ElMessage.warning('请先单击初始化，选择功能模块');
  return;
} else if (!formData.sdkGitLab.trim() || !formData.group || !formData.space || !formData.selectedChip || !formData.selectedModules.length) {
  if (!formData.sdkBranch && !formData.sdkTag) {
          ElMessage.warning('信息不能有空值');
          return;
  }

} else {
          console.log("初始化：", init_project_code, init_project_name);
          console.log("提交：",project_code, project_name);
          try {
              if (init_project_code === project_code) {

                // 提交sdk配置信息
                submitForm();
            } else {
              ElMessage.warning('项目信息有变化，请选择正确的项目：'+ init_project_code);
            }

          } catch (error) {
            console.error("判断错误", error);
          }
   
           
        }
        
  };



  
// 配置提交按钮功能
  const submitForm = () => {
    console.log("项目信息:", project_code, project_name);
    // 请求芯片和功能模块的 grpc配置 
    console.log("表单数据:", formData);
    // 获取提交配置信息
    loading.value = true;
    // 使用 Axios 实例发送 Get请求
    retryRequest(() => http.post('/code_management/config_submit',{
      params:{
        // 项目信息
        project_code: project_code,
        project_name: project_name,
        // 功能信息
        sdk_gitlab: formData.sdkGitLab,
        sdk_branch: formData.sdkBranch,
        sdk_tag: formData.sdkTag,
        selected_chip: formData.selectedChip,
        selected_modules: formData.selectedModules,
        // 提交配置信息
        workspace: formData.space,
        workgroup: formData.group,

        // config_info: JSON.stringify(config_info.value)
      },timeout: 30000
    }), 1, 2000).then(response => {
      loading.value = false;
      console.log("提交信息状态:", response.data);

      if (response.data.config_status === 1) {
          ElMessage.success('提交成功');
          router.push('/projects_repository/project_index')
      } else {
        ElMessage.error(`提交失败：${response.data.message}`);
      }
    }).catch(error => { 
      loading.value = false;
      ElMessage.error('提交失败')
      console.error("提交失败:", error);
    });
  }


</script>

<style scoped>
/* 容器样式 */
.codemangement-container {
  width: 100%;
  padding: 20px;
  border: 1px solid #ccc;
  box-shadow: 0 0 10px #ccc;
  font-size: 14px;
  font-weight: normal;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #3080dd;
  padding: 15px 0px;
  margin: -20px -20px;
  color: white;
}

.main-title {
  font-size: 24px;
  font-weight: bold;
  padding-left: 20px;
}

.logo-icon {
  font-size: 24px;
  color: white;
  margin-right: 20px;
}

/* 表单样式 */
form {
  margin-top: 50px;
}

/* 表单项样式 */
.form-group {
  margin-bottom: 50px; /* 表单项之间的间距 */
  display: flex;
  align-items: flex-start; /* 确保元素垂直对齐起点 */
  gap: 10px; /* 元素之间的间距 */
}

.ini_form_group{
  margin-bottom: 0px;
}

/* 标签样式 */
.label {
  display: inline-block;
  width: 120px; /* 标签固定宽度 */
  margin-right: -10px; /* 标签与输入框的间距 */
  margin-left: 30px;
  margin-top: 10px;
  color: #757575;
  font-size: 14px;
}

/* 输入框样式 */
.input-field {
  flex: 1; /* 输入框占据剩余空间 */
  height: 30px; /* 设置输入框高度与 el-select 一致 */
  padding: 0 10px; /* 输入框内边距 */
  border: 1px solid #ccc;
  border-radius: 4px;
  outline: none;
  margin-right: 100px;
  color: #757575;
}

.input-field[disabled] {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
}

input:focus {
  border-color: #3080dd;
}

.submit-btn {
  margin-top: -5px;
  margin-left: -70px;
  margin-right: 100px;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 14px;
  border-radius: 4px;
}

.submit-btn:hover {
  background-color: #0056b3;
}

/* el-select 样式 */
.el-select {
  flex: 1; /* el-select 占据剩余空间 */
  height: 40px; /* 设置 el-select 高度 */
  padding: 0 10px; /* el-select 内边距 */
  border: 1px solid #ccc;
  border-radius: 4px;
  outline: none;
}

.el-select[disabled] {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
}

.el-select .el-input__inner {
  height: 40px; /* 设置 el-select 内部输入框高度 */
}

.el-select .el-input__inner:focus {
  border-color: #3080dd;
}

.el-select .el-input__inner:hover {
  background-color: #ccc;
}

.el-select .el-input__inner:active {
  background-color: #e3162b;
  color: white;
}

/* 复选框样式 */
.checkbox-container {
  margin-top: 10px;
  margin-left: 5px; /* 复选框对齐标签的位置 */
  zoom: 100%;
  color: #757575;
}

/* 勾选后的复选框样式 */
input[type="checkbox"]:checked {
  accent-color: #3080dd;/* 设置复选框勾选后的颜色 */
}


.checkbox-item {
  margin-bottom: 20px; /* 复选框之间的间距 */
}

.checkbox {
  margin-right: 10px; /* 复选框与标签的间距 */
  vertical-align: middle;
}

.module-label {
  font-size: 14px; /* 复选框标签字体大小 */
  vertical-align: middle;
}

/* 按钮样式 */
.button-container {
  text-align: right; /* 按钮靠右对齐 */
  margin-top: 20px; /* 按钮与表单的间距 */
  margin-bottom: 50px;
  margin-right: 120px;
  border-radius: 4px;
}

button {
  padding: 10px 20px;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 20px;
}

button:hover {
  background-color: #0056b3;
}

/* 弹窗标题居中 */
.custom-dialog-header {
  text-align: center;
  font-size: 18px;
  font-weight: normal;
  padding-bottom: 30px;
}

/* 自定义表单整体内边距 */
.custom-form {
  padding: 0 20px;
}


/* 自定义 label 的样式 */
.form-label {
  width: 80px;
}
/* 每个表单项的样式 */
.submit-form-group {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

/* 输入框和下拉框的宽度统一 */
.submit-input-field {
  width:350px;
  flex: 1;
  font-size: 14px;
  height: 30px;
  border: none;
  padding-left: 10px;
  padding-right: 30px;
}

</style>
