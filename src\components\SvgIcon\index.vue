<template>
  
  <el-icon v-if="source === 'element-plus'" :size="size">
    <component :is="name" />
  </el-icon>
  
  <el-icon v-else-if="source === 'iconify'" :size="size">
    <Icon :icon="name" />
  </el-icon>

  <el-icon v-else :size="size">
    <svg fill="currentColor">
      <use :href="`#icon-${name}`" :xlink:href="`#icon-${name}`" ></use>
    </svg>
  </el-icon>

</template>

<script setup>

import { Icon } from '@iconify/vue';

let props = defineProps({
  name: {
    type: String,
    required: true
  },
  source: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: '1.5em'
  }
})
</script>