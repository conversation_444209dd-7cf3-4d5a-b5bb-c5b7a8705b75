<template>
    <div>
        <el-form :model="form" :rules="rules" label-width="auto" label-position="left" status-icon ref="formRef"
            style="padding: 20px;">
            <el-form-item label="测试值" prop="value">
                <el-input v-model="form.value" placeholder="请输入测试值"></el-input>
            </el-form-item>
            <el-form-item label="测试结果" prop="value">
                <el-select v-model="form.result_two" placeholder="请选择测试结果">
                    <el-option label="PASS" value="1"></el-option>
                    <el-option label="NG" value="0"></el-option>
                    <el-option label="NA" value="2"></el-option>
                    <el-option label="NT" value="3"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="备注" style="margin-left: 10px">
                <el-input style="margin-left: -10px" type="textarea" :rows="3" v-model="form.remark"
                    placeholder="请输入备注"></el-input>
            </el-form-item>
            <el-form-item label="附件" style="margin-left: 10px">
                <el-upload style="margin-left: -10px;width: 100%" ref="uploadRef" action="" :auto-upload="false"
                    :limit="1" :on-exceed="handleExceed" :on-change="handleChange">
                    <template #tip>
                        <div style="font-size: 12px; color: #999; margin-top: 5px;">已保存附件：{{ at_file }}</div>
                    </template>
                    <el-button size="small" type="primary">点击上传附件</el-button>
                    
                </el-upload>
            </el-form-item>
            <el-form-item label="提示" style="margin-left: 10px">
                <span style="margin-left: -10px">PASS：通过、NG：不通过、NA：不适用、NT：未测试</span>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end;">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="confirm">确 定</el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import { genFileId } from 'element-plus'

const props = defineProps({
    manualExecContext: {
        type: Object,
        required: true
    }
});
const emit = defineEmits(['confirm', 'cancel']);

const formRef = ref(null);
const uploadRef = ref(null);
const uploadFile = ref(null);
const at_file = ref("");

const form = ref({
    test_plan_id: props.manualExecContext.test_plan_id,
    test_case_id: props.manualExecContext.test_case_id,
    test_case_version: props.manualExecContext.test_case_version,
    value: props.manualExecContext.value,
    result_two: props.manualExecContext.result_two,
    remark: props.manualExecContext.remark,
    generation_mode: '2',
});

const rules = ref({
    value: [
        { required: true, message: '请输入测试值', trigger: 'blur' }
    ],
    result: [
        { required: true, message: '请选择测试结果', trigger: 'change' }
    ]
});

const handleExceed = (files) => {
    uploadRef.value.clearFiles();
    const file = files[0];
    file.uid = genFileId();
    uploadRef.value.handleStart(file);
};

const handleChange = (file, fileList) => {
    uploadFile.value = file.raw;
};

const cancel = () => {
    emit('cancel');
};

const confirm = () => {
    formRef.value.validate((valid) => {
        const formData = new FormData();
        Object.keys(form.value).forEach(key => {
            formData.append(key, form.value[key]);
        });
        if (uploadFile.value) {
            formData.append('file', uploadFile.value);
        }

        if (valid) {
            http.post('/v2/test_plans/test_case_exec', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            }).then(res => {
                ElMessage({
                    message: '添加成功.',
                    type: 'success',
                });
                emit('confirm', form.value);
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            })
        } else {
            return false;
        }
    });
}

// 确保 result_two 的值是字符串类型，与 el-option 的 value 类型一致
form.value.result_two = form.value.result_two?.toString();

onMounted(() => {
    let fileIds = props.manualExecContext.file;
    if (fileIds) {
        http.get('/v2/test_plans/at_files', {params: {file_ids: fileIds}}).then(res => {
            let at_files = res.data.data;
            if (at_files.length > 0) {
                const fileSizeKB = (at_files[0].size / 1024).toFixed(2);
                at_file.value = at_files[0].title + '(' + fileSizeKB + 'KB)';
            }
        }).catch(err => {
        })
    }
});
</script>


<style scoped></style>