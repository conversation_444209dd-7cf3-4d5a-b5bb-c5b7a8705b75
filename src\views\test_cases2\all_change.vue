<template>
    <div style="display: flex; justify-content: center;width: 100%">

        <el-form :model="form2" :rules="rules" label-width="auto" status-icon style="width: 600px" ref="formRef">

            <el-form-item label="所属项目" prop="project_number">
                <el-select v-model="form2.project_number" placeholder="请选择所属项目" filterable>
                    <el-option v-for="item in projects" :label="`${item.name}(${item.projectCode})`"
                        :value="item.projectCode"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="产品类型" prop="product_type">
                <el-select v-model="form2.product_type" placeholder="请选择产品类型">
                    <el-option v-for="item in product_types" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>

        </el-form>

    </div>

    <div class="tool-bar-container" style="position: relative;">

        <el-button type="primary" @click="all_export" style="position: relative;">批量导入</el-button>

        <div style="margin-left: auto; display: flex; gap: 10px;">
            <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                <el-button text bg @click="handleReset">重置</el-button>
            </el-tooltip>
            <filterButton @click="onFilterStatusChange" :count="filterCount" :expand="true" />
            <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
        </div>
    </div>

    <div class="filter-container" v-if="showFilterContainer">

        <el-row :gutter="10">
            <el-col :span="8">
                <el-input v-model="form.name" placeholder="请输入名称" @keyup.enter="onFilter" clearable>
                    <template #append>
                        <el-button icon="Search" @click="onFilter"></el-button>
                    </template>
                </el-input>
            </el-col>
            <el-col :span="8">
                <el-select v-model="form.action_type_list" placeholder="请选择用例活动类型" @change="onFilter" clearable
                    multiple>
                    <el-option v-for="item in action_types" :label="item.name" :value="item.number"></el-option>
                </el-select>
            </el-col>
            <el-col :span="8">
                <el-tree-select v-model="form.module" :data="modules" :props="{ label: 'name', value: 'm' }"
                    ref="moduleRef" placeholder="请选择所属模块" clearable :render-after-expand="false" node-key="m"
                    show-checkbox check-strictly multiple @check="onModuleChange">
                </el-tree-select>
            </el-col>
        </el-row>

        <el-row :gutter="10">

            <el-col :span="8">
                <el-select v-model="form.tags" placeholder="请选择标签" multiple @change="onFilter">
                    <el-option v-for="item in test_case_tags" :label="item.name" :value="item.number"></el-option>
                </el-select>
            </el-col>
            <el-col :span="8">
                <el-select v-model="form.es_source" placeholder="请选择执行标准" @change="onFilter" clearable>
                    <el-option label="海微标准" value=""></el-option>
                    <el-option label="蔚来标准" value="nio"></el-option>
                    <el-option label="岚图标准" value="voyah"></el-option>
                </el-select>
            </el-col>

        </el-row>
    </div>

    <div class="table-container">
        <el-table :data="tableData" stripe border style="width: 100%" height="700"
            @selection-change="handleselectionchange">

            <el-table-column type="selection" width="55" /> <!--全选框-->

            <el-table-column prop="name" label="用例名称" width="200" align="center"></el-table-column>
            <el-table-column label="所属模块" width="100" align="center">
                <template #default="{ row }">
                    <span>{{ moduleMap[row.module] || row.module }}</span>
                    <span v-if="row.module_2level"> / {{ moduleMap[row.module + '-' + row.module_2level] ||
                        row.module_2level }}</span>
                    <span v-if="row.module_3level"> / {{ moduleMap[row.module + '-' + row.module_2level + '-' +
                        row.module_3level] || row.module_3level }}</span>
                </template>
            </el-table-column>
            <el-table-column label="用例类型" width="100" align="center">
                <template #default="{ row }">
                    <span>{{ typeMap[row.type] || row.type }}</span>
                </template>
            </el-table-column>
            <el-table-column label="用例活动类型" width="100" align="center">
                <template #default="{ row }">
                    <span>{{ actionTypeMap[row.action_type] || row.action_type }}</span>
                </template>
            </el-table-column>
            <el-table-column label="用例来源" width="100" align="center">
                <template #default="{ row }">
                    <span>{{ sourceMap[row.source] || row.source }}</span>
                </template>
            </el-table-column>
            <el-table-column label="执行方式" width="150" align="center">
                <template #default="{ row }">
                    <el-tag v-if="row.execute_mode == 'AUTOMATED_EXECUTION'" type="success">自动化测试</el-tag>
                    <el-tag v-else-if="row.execute_mode == 'MANUAL_EXECUTION'" type="success">手动测试</el-tag>
                    <el-tag v-else-if="row.execute_mode == 'SEMI_AUTOMATED_EXECUTION'" type="success">半自动化测试</el-tag>
                    <el-tag v-else type="danger">未知</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="creator_name" label="创建人" width="100" align="center"></el-table-column>

        </el-table>
    </div>

    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
            @change="onPageChange" />
    </div>

</template>


<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import http from '@/utils/http/http.js';
import filterButton from '@/components/filterButton.vue';
import { useProjectStore } from '@/stores/project.js';
import { ElMessageBox } from 'element-plus';

const projects = ref([]);
const filterCount = ref(0);
const modules = ref([]);
const action_types = ref([]);
const tableData = ref([]);
const test_case_tags = ref([]);
const product_types = ref([]);
const pre_product_types = ref([]);
const projectStore = useProjectStore();

let form = reactive({
    page: 1,
    pagesize: 10,
});

const form2 = ref({
    project_number: '',
    product_type: '',
});

const formRef = ref(null);
const rules = ref({
    project_number: [{ required: true, message: '请选择所属项目', trigger: 'change' }],
    product_type: [{ required: true, message: '请选择产品类型', trigger: 'change' }],
});

let a = ref([])

function handleselectionchange(row) {
    a.value = row
}

const emit = defineEmits(['submit']);

function all_export() {
    formRef.value.validate((valid) => {
        if (valid) {
            const p = projects.value.find(item => item.projectCode == form2.value.project_number);

            let data = {
                project_number: form2.value.project_number,
                project_name: p?.name,
                project_id: p?.id,
                product_type_id: form2.value.product_type,
                test_case_ids: a.value.map(item => item.id)
            };

            if (a.value.length == 0) {
                ElMessageBox.alert('请选择要导入的用例.', '批量导入失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                });
                return;
            }

            http.post('/test_cases/patch_import', data)
                .then(response => {
                    ElMessage({
                        message: '批量导入成功.',
                        type: 'success',
                    });
                    emit('submit');
                })
                .catch(err => {
                    ElMessageBox.alert(err.response.data.msg, '批量导入失败', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                    });
                })
        }
    });
}

let total = ref(0);

let showFilterContainer = ref(true);
let moduleMap = ref({});


let actionTypeMap = ref({});

let sourceMap = ref({
    "TASK_CHANGE": "用例库沿用",
    "TASK_AFFIRM": "需求分析",
    "NEW_PROJECT": "需求变更",
    "HORIZONTAL_SCALING": "横向扩展"
});

let typeMap = ref({
    "DURABLE_TEST": "耐久测试",
    "PERFORMANCE_TEST": "性能测试",
    "FUNCTION_TEST": "功能测试",
    "PROTOCOL_STACK_TEST": "协议栈测试"
});

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/test_cases/public', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'module_list', 'module_2level_list', 'module_3level_list'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleRefresh() {
    update_table();
};

const onModuleChange = (curData, checkedData) => {
    let module1 = checkedData.checkedNodes.filter(node => node.level == 1).map(node => node.number);
    let module2 = checkedData.checkedNodes.filter(node => node.level == 2).map(node => node.number);
    let module3 = checkedData.checkedNodes.filter(node => node.level == 3).map(node => node.number);

    form.module_list = module1;
    form.module_2level_list = module2;
    form.module_3level_list = module3;

    update_table();
};

watch([() => form2.value.project_number, pre_product_types], () => {

    form2.value.product_type = '';

    if (!form2.value.project_number) {
        product_types.value = [];
        return;
    }

    http.get(`/projects/detail/by_number`, { params: { number: form2.value.project_number } }).then(res => {

        let pt = res.data.data?.configs?.product_types || [];
        product_types.value = pre_product_types.value.filter(item => pt.includes(item.id));

    });
})


onMounted(() => {
    form2.value.project_number = projectStore.project_info.projectCode;

    update_table();

    http.get('/projects/p/all').then(res => {
        let data = res.data.data.results;
        projects.value = data;

    }).catch(err => {
        console.log(err);
    });

    http.get('/functions').then(res => {
        res.data.data.results.forEach(item => {
            moduleMap.value[item.number] = item.name;
            if (item.children) {
                item.children.forEach(item2 => {
                    moduleMap.value[item.number + '-' + item2.number] = item2.name;
                    if (item2.children) {
                        item2.children.forEach(item3 => {
                            moduleMap.value[item.number + '-' + item2.number + '-' + item3.number] = item3.name;
                        });
                    }
                });
            }
        });

        let data = res.data.data.results;
        data.forEach(item => {
            item.m = item.number;
            if (item.children) {
                item.children.forEach(item2 => {
                    item2.m = item.number + '-' + item2.number;
                    if (item2.children) {
                        item2.children.forEach(item3 => {
                            item3.m = item.number + '-' + item2.number + '-' + item3.number;
                        });
                    }
                });
            }
        });
        modules.value = data;
    });

    http.get('/test_m/test_case_types', { params: { pagesize: 10000 } }).then(res => {
        let data = res.data.data.results;
        action_types.value = data;
        res.data.data.results.forEach(item => {
            actionTypeMap.value[item.number] = item.name;
        });

    });

    http.get('/test_case_tags', { params: { pagesize: 100000 } }).then(res => {
        let data = res.data.data.results;
        test_case_tags.value = data;
    }).catch(err => {
        console.log(err);
    });

    http.get('/product_types', { params: { pagesize: 100000 } }).then(res => {
        let data = res.data.data.results;
        pre_product_types.value = data;

    }).catch(err => {
        console.log(err);
    });

});

</script>


<style lang="scss" scoped>
.filter-container {
    .el-row {
        margin-top: 10px;
        margin-bottom: 10px;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;
    margin-top: 10px;

    .el-select {
        width: 500px;
        margin-left: 10px;
    }

}
</style>