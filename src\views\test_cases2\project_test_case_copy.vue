<template>
    <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

        <el-row :gutter="20">
            <el-col :span="11">

                <el-form-item label="源项目" prop="source_project_number">
                    <Projects v-model="form.source_project_number" :includePrefix="false" :includeAll="false" />
                </el-form-item>

            </el-col>
            <el-col :span="2">
                <div style="text-align: center;padding-top: 10px;"><span>复制到</span></div>
            </el-col>
            <el-col :span="11">

                <el-form-item label="目标项目" prop="target_project_number">
                    <Projects v-model="form.target_project_number" :includePrefix="false" :includeAll="false"
                        ref="projectRef" />
                </el-form-item>

            </el-col>
        </el-row>

    </el-form>

    <div class="submit-button-container">
        <el-button type="default" @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onConfirm" :loading="loading">提交</el-button>
    </div>

</template>

<script setup>
import { ref, onMounted } from 'vue'
import http from '@/utils/http/http.js'
import Projects from '@/components/projects.vue';
import { useProjectStore } from '@/stores/project.js';


const form = ref({
    source_project_number: '',
    target_project_number: '',
});

const rules = {
    source_project_number: [
        { required: true, message: '请选择源项目', trigger: 'change' }
    ],
    target_project_number: [
        { required: true, message: '请选择目标项目', trigger: 'change' }
    ]
};

const formRef = ref(null);
const projectRef = ref(null);
const loading = ref(false);
const projectStore = useProjectStore();

const emit = defineEmits(['cancel', 'confirm']);

const onCancel = () => {
    emit('cancel');
}

const onConfirm = () => {
    formRef.value.validate((valid) => {
        if (valid) {
            let data = {
                source_project_number: form.value.source_project_number,
                target_project_number: form.value.target_project_number,
            }

            let pInfo = projectRef.value.getProjectInfo(form.value.target_project_number);

            data.target_project_id = pInfo?.id;
            data.target_project_name = pInfo?.name;

            loading.value = true;
            http.post(`/test_cases/copy_by_project`, data, { timeout: 180000 }).then(res => {
                emit('confirm');
                ElMessage({
                    message: '复制成功.',
                    type: 'success',
                });

            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '复制失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            }).finally(() => {
                loading.value = false;
            });
        }
    });
}

onMounted(() => {
    if (projectStore.project_info.projectCode) {
        form.value.target_project_number = projectStore.project_info.projectCode;
    } 
})

</script>

<style scoped>
.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>