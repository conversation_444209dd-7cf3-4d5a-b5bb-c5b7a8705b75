<template>
  <div class="test-submission-container">
    <div class="page-header">
      <div class="header-right">
        <div class="project-info" v-if="projectStore.project_info?.name">
          <span class="project-label">当前项目：</span>
          <span class="project-name">{{ projectStore.project_info.name }}({{ projectStore.project_info.projectCode }})</span>
        </div>
      </div>
    </div>
    
    <!-- 表格筛选区 -->
    <div class="filter-container">
      <el-tabs v-model="activeTab" class="version-tabs" @tab-click="handleTabChange">
        <el-tab-pane label="全部版本" name="all"></el-tab-pane>
      </el-tabs>
    </div>
    
    <!-- 版本列表表格 -->
    <div class="table-container">
      <el-table 
        :data="submissionList" 
        style="width: 100%" 
        border 
        v-loading="loading"
        row-key="id"
        :header-cell-style="{background:'#f5f7fa',color:'#606266'}"
      >
        <el-table-column prop="project" label="所属项目" min-width="150" />
        <el-table-column prop="title" label="产品版本类型" min-width="180" />
        <el-table-column prop="version" label="产品版本类型详细信息" min-width="200">
          <template #default="scope">
            <div style="white-space: pre-line;">{{ scope.row.version }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="submitTime" label="提交日期" min-width="120" />  
        <el-table-column prop="engineeringProject" label="工程组" min-width="120" />
        <el-table-column prop="engineeringPath" label="工程路径" min-width="150" />
        <el-table-column prop="status" label="版本状态" min-width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="originalBranch" label="源分支" min-width="120" />
        <el-table-column prop="targetBranch" label="目标分支" min-width="120" />
        <el-table-column prop="publisher" label="发布人" min-width="100" />
        <el-table-column prop="version_usage" label="版本用途" min-width="100" />
        <el-table-column prop="content" label="软件版本变更内容" min-width="200">
          <template #default="scope">
            <div style="white-space: pre-line;">{{ scope.row.content }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="180">
          <template #default="scope">
            <div class="button-group">
              <el-button 
                size="small" 
                type="primary" 
                @click="auditSubmission(scope.row)"
                :disabled="scope.row.status !== '待审核'"
                class="action-button"
              >审核</el-button>
              <el-button 
                size="small" 
                type="primary" 
                @click="deleteSubmission(scope.row)"
                :disabled="scope.row.status === '已完成'"
                class="action-button"
              >删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="pageSize"
          :current-page="currentPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, onUnmounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useProjectStore } from '@/stores/project.js';
import http from '@/utils/http/http.js';

// 状态数据
const loading = ref(false);
const submissionList = ref([]);
const total = ref(0);
const pageSize = ref(10);
const currentPage = ref(1);
const activeTab = ref('all');

// 项目store
const projectStore = useProjectStore();

const VERSION_USAGE_MAP = {
     FORMAL: '正式',
     TEMPORARY: '临时',
     EXPERIMENTAL: '实验'
   };

const handleTabChange = () => {
  fetchData(); 
};

// 监听项目变化
watch(() => projectStore.project_info, (newProject, oldProject) => {
  console.log('项目变更:', newProject);
  // 项目变化时重置分页并重新获取数据
  currentPage.value = 1;
  fetchData();
}, { deep: true });

// 模拟数据加载
onMounted(() => {
  fetchData();
});

// 获取测试数据
const fetchData = () => {
  loading.value = true;
  
  // 构建请求参数
  const params = {
    page: currentPage.value,
    page_size: pageSize.value
  };
  
  // 如果选择了项目，添加项目筛选参数
  if (projectStore.project_info?.projectCode) {
    params.project = projectStore.project_info.projectCode;
  }
  
  // 调用实际API获取数据
  http.get('/softtrack/submitsoftware', { params })
    .then(response => {
      if (response.data && response.data.results) {
        submissionList.value = response.data.results.map(item => {
            let versionInfo = '';
            // 动态拼接所有有值的版本字段
            if (Array.isArray(item.version_meta)) {
              const versions = item.version_meta
                .filter(v => v.value) // 只显示有值的字段
                .map(v => `${v.name}: ${v.value}`);
              versionInfo = versions.join('\n');
            }
            // 如果没有任何版本信息，则显示主版本号
            if (!versionInfo && item.main_version) {
              versionInfo = `主版本号: ${item.main_version}`;
            }
            return {
              id: item.id,
              project: item.project,
              title: item.software_type,
              version: versionInfo,
              submitTime: item.creation_time,
              engineeringProject: item.engineering_group || '',
              engineeringPath: item.engineering_path || '',
              status: item.status || '待审核',
              originalBranch: item.source_branch || '',
              targetBranch: item.target_branch || '',
              publisher: item.creator || '',
              version_usage: VERSION_USAGE_MAP[item.version_usage] || '',
              content: item.change_description || ''
            };
          });
        
        total.value = response.data.count || submissionList.value.length;
      } else {
        submissionList.value = [];
        total.value = 0;
      }
    })
    .catch(error => {
      console.error('获取测试数据失败:', error);
      ElMessage.error('获取数据失败，请稍后重试');
      submissionList.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
};

// 根据状态获取Tag类型
const getStatusType = (status) => {
  switch (status) {
    case '待审核':
      return 'warning';
    case '测试中':
      return 'primary';
    case '已审核':
      return 'success';
    case '已拒绝':
      return 'danger';
    default:
      return 'info';
  }
};

// 删除提测
const deleteSubmission = (row) => {
  ElMessageBox.confirm(
    `确定要删除"${row.title}"提测记录吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 这里应该调用删除API
    http.delete('/softtrack/submitsoftware', {
      params: {
        id: row.id
      }
    })
    .then(response => {
      if (response.data.code === 200) {
        ElMessage.success('删除成功');
        fetchData(); // 刷新数据
      } else {
        ElMessage.error('删除失败');
      }
    })
    .catch(error => {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    })
    .finally(() => {
      loading.value = false;
    });
  }).catch(() => {
    // 取消删除
  });
};

// 分页相关方法
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchData();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchData();
};

// 添加审核提交函数
const auditSubmission = (row) => {
  ElMessageBox.confirm(
    `确定要审核"${row.title}"提测记录吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    loading.value = true;
    
    const auditData = {
      engineering_path: row.engineeringPath,
      source_branch: row.originalBranch,
      target_branch: row.targetBranch,
      engineering_group: row.engineeringProject,
      id: row.id
    };
    
    http.post('/softtrack/mergerequest', auditData)
      .then(response => {
        if (response.data && (response.data.mr_iid || response.data.success)) {
          ElMessage.success('审核操作已提交');
          
          // 如果返回了mr_iid，开始轮询合并请求状态
          if (response.data.mr_iid) {
            const projectPath = response.data.project || row.engineeringPath;
            const mrIid = response.data.mr_iid;
            
            // 启动轮询
            startPollingMergeRequestStatus(projectPath, mrIid, row);
          } else {
            // 如果没有返回mr_iid，仅刷新数据
            fetchData();
          }
        } else {
          ElMessage.error(`审核失败: ${response.data?.message || '未知错误'}`);
        }
      })
      .catch(error => {
        console.error('审核操作失败:', error);
        ElMessage.error(`审核失败: ${error.message || '网络错误'}`);
      })
      .finally(() => {
        loading.value = false;
      });
  }).catch(() => {
  });
};


const pollingTimers = ref({}); // 存储定时器ID，以便可以清除

const startPollingMergeRequestStatus = (projectPath, mrIid, row) => {
  const timerId = `${projectPath}-${mrIid}`;
  

  if (pollingTimers.value[timerId]) {      // 清除可能存在的旧定时器
    clearInterval(pollingTimers.value[timerId]);
  }
  
  // 显示初始状态通知
  ElMessage({
    message: `正在检查合并请求状态，请稍候...`,
    type: 'info',
    duration: 3000
  });
  
  // 创建新的定时器，每5秒轮询一次
  pollingTimers.value[timerId] = setInterval(() => {
    http.get('/softtrack/mr-status/', {
      params: {
        engineering_group: row.engineeringProject,
        project_path: projectPath,
        mr_iid: mrIid
      }
    })
    .then(response => {
      if (response.data) {
        const { merge_status, state,code } = response.data;
        
        // 根据状态更新UI或显示通知
        if (merge_status === 'can_be_merged' && state === 'opened') {
          ElMessage({
            message: '合并请求已准备好,将进行自动合并',
            type: 'success',
            duration: 5000
          });
          
          http.post('/softtrack/mr-status/', {             // 发送POST请求来执行合并
            project_path: projectPath,
            mr_iid: mrIid,
            id: row.id
          })
          .then(mergeResponse => {
            console.log('自动合并已触发:', mergeResponse.data);
            if (mergeResponse.data && mergeResponse.data.code == 200) {
              ElMessage({
                message: mergeResponse.data.message,
                type: 'success',
                duration: 5000
              });
            } else {
              ElMessage({
                message: mergeResponse.data.message,
                type: 'error',
                duration: 5000
              });
            }
          })
          .catch(mergeError => {
            console.error('执行合并失败:', mergeError);
            ElMessage.error(`执行合并失败: ${mergeError.message || '网络错误'}`);
          });
          
          clearInterval(pollingTimers.value[timerId]);  // 停止轮询
          delete pollingTimers.value[timerId];
        } 
        else if (state === 'closed') {
          ElMessage({
            message: '当前合并请求失败，请检查是否提交远端dev分支，或者是否存在冲突',
            type: 'warning',
            duration: 5000
          });
          clearInterval(pollingTimers.value[timerId]);
          delete pollingTimers.value[timerId];
          fetchData();
        }   
      }
    })
    .catch(error => {
      console.error('轮询合并请求状态失败:', error);
      clearInterval(pollingTimers.value[timerId]);
      delete pollingTimers.value[timerId];
      ElMessage.error( `当前合并失败,异常是 ${error.response.data.message}`);
      
    });
  }, 5000); // 每5秒轮询一次
};

// 在组件卸载时清除所有定时器
onUnmounted(() => {
  Object.values(pollingTimers.value).forEach(timerId => {
    clearInterval(timerId);
  });
  pollingTimers.value = {};
});
</script>

<style scoped>
.test-submission-container {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  font-size: 22px;
  color: #303133;
  margin: 0;
}

.project-info {
  display: flex;
  align-items: center;
  background-color: #e1f3d8;
  padding: 8px 16px;
  border-radius: 4px;
  border: 1px solid #b3d8a4;
}

.project-label {
  font-size: 14px;
  color: #67c23a;
  font-weight: 500;
  margin-right: 8px;
}

.project-name {
  font-size: 14px;
  color: #67c23a;
  font-weight: 600;
}

.filter-container {
  background-color: #fff;
  padding: 10px 20px 0;
  margin-bottom: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.table-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  overflow-x: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  padding-bottom: 40px;
}

.el-table {
  width: 100% !important;
  min-width: 100%;
  flex-grow: 1;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.version-tabs :deep(.el-tabs__item) {
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
}

.version-tabs :deep(.el-tabs__nav) {
  border: none;
}

.version-tabs :deep(.el-tabs__active-bar) {
  height: 3px;
}

/* 调整表格样式 */
:deep(.el-table th) {
  font-weight: 600;
  color: #606266;
  background-color: #f5f7fa !important;
}

:deep(.el-table .cell) {
  padding-left: 12px;
  padding-right: 12px;
}

:deep(.el-button--text) {
  padding: 4px 8px;
}

/* Ensure the single tab looks correct */
.filter-container .el-tabs__nav-wrap::after {
  /* Hide the bottom border of the tabs container if desired */
  background-color: transparent;
}

/* 操作按钮样式 */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-button {
  width: 100%;
  margin-right: 0;
}

/* 在大屏幕上使用水平布局 */
@media screen and (min-width: 768px) {
  .button-group {
    flex-direction: row;
    gap: 8px;
  }
  
  .action-button {
    width: 80px;
  }
}
</style> 