<template>
  <div class="push-platform-form">
    <div class="form-group">
      <label for="platformUrl">开发平台地址</label>
      <input 
        id="platformUrl" 
        v-model="config.platformUrl" 
        type="text" 
        placeholder="https://product-platform.example.com" 
      />
    </div>

    <div class="form-group">
      <label for="pushEnv">推送环境</label>
      <select 
        id="pushEnv" 
        v-model="config.environment"
      >
        <option value="test">测试环境</option>
        <option value="prod">生产环境</option>
      </select>
    </div>

    
    <div class="form-actions">
      <button class="cancel-btn" @click="$emit('cancel')">取消</button>
      <button class="save-btn" @click="save">保存</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  initialConfig: {
    type: Object,
    default: () => ({
      platformUrl: '',
      environment: 'dev',
    })
  }
});

const emit = defineEmits(['save', 'cancel']);
const config = ref({
  platformUrl: '',
  environment: 'dev',
  ...props.initialConfig
});

const save = () => {
  emit('save', config.value);  
};
</script>

<style scoped>
.push-platform-form {
  width: 100%;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

input, select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #1f2937;
  transition: border-color 0.2s;
}

input:focus, select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-item label {
  margin-bottom: 0;
  font-weight: normal;
}

.checkbox-item input[type="checkbox"] {
  width: auto;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 36px;
}

button {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.cancel-btn:hover {
  background-color: #e5e7eb;
}

.save-btn {
  background-color: #2563eb;
  color: white;
  border: 1px solid #2563eb;
}

.save-btn:hover {
  background-color: #1d4ed8;
}
</style> 