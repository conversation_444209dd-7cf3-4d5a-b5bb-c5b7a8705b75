<template>
    <div>
        <div style="padding: 20px 10px;">
            <el-form label-width="auto">
                <el-form-item label="分享链接:">
                    <el-input v-model="form.url" readonly></el-input>
                    <span v-if="form.url">链接有效期6小时</span>
                </el-form-item>

            </el-form>
        </div>
        <div style="display: flex; justify-content: end;">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleShare">生成链接</el-button>
            <el-button type="primary" @click="handleCopy">复制链接</el-button>
        </div>
    </div>
</template>

<script setup>
import { reactive } from 'vue';
import http from '@/utils/http/http.js';
import { ElMessageBox } from 'element-plus';

const props = defineProps({
    r_id: {
        type: Number,
        required: true
    }
});

const form = reactive({
    url: ''
});

const emit = defineEmits(['cancel']);

function handleCancel() {
    emit('cancel');
}

function handleShare() {
    http.post('/diff_tool/records/share_urls', { diff_record_id: props.r_id }).then(res => {
        let token = res.data.data.token;
        form.url = window.location.origin + '/diff_packages/' + token;
    }).catch(err => {
        ElMessageBox.alert(
            err.response.data.msg,
            '警告',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
    });
}

function handleCopy() {
    copyTextToClipboard(form.url);
}

const copyTextToClipboard = async (text) => {
    if (navigator.clipboard && navigator.clipboard.writeText) {
        try {
            await navigator.clipboard.writeText(text);
        } catch (err) {
            console.error('复制失败', err);
            fallbackCopyTextToClipboard(text);
        }
    } else {
        fallbackCopyTextToClipboard(text);
    }
};

const fallbackCopyTextToClipboard = (text) => {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.position = "fixed";  // 防止页面滚动
    textArea.style.opacity = "0";  // 隐藏文本区域
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
        const successful = document.execCommand('copy');
        const msg = successful ? '复制成功' : '复制失败';
        console.log(msg);
        if (!successful) {
            ElMessageBox.alert(
                '复制失败：无法使用 Clipboard API 或 execCommand',
                '错误',
                {
                    confirmButtonText: '确定',
                    type: 'error',
                }
            );
        }
    } catch (err) {
        console.error('复制失败', err);
        ElMessageBox.alert(
            '复制失败：无法使用 Clipboard API 或 execCommand',
            '错误',
            {
                confirmButtonText: '确定',
                type: 'error',
            }
        );
    }
    document.body.removeChild(textArea);
};


</script>

<style scoped></style>