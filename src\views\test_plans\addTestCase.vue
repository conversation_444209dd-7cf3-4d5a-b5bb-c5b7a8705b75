<template>
    <div>
        <div class="tool-bar-container">

            <el-button type="info" plain @click="onFilterStatusChange">
                筛选<el-icon class="el-icon--right">
                    <component :is="filterButtonIcon"></component>
                </el-icon>
            </el-button>

            <el-input placeholder="请输入搜索内容" class="search-input" style="margin-left: auto;" v-model="form.search_str" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter" class="search-button">搜索</el-button>
                </template>
            </el-input>
        </div>

        <div class="filter-container" v-if="showFilterContainer">

            <el-row :gutter="10">
                <el-col :span="8">
                    <el-input v-model="form.name" placeholder="请输入用例名称" @keyup.enter="onFilter" clearable>
                        <template #append>
                            <el-button icon="Search" @click="onFilter"></el-button>
                        </template>
                    </el-input>
                </el-col>
                <el-col :span="8">
                    <el-input v-model="form.number" placeholder="请输入用例ID" @keyup.enter="onFilter">
                        <template #append>
                            <el-button icon="Search" @click="onFilter"></el-button>
                        </template>
                    </el-input>
                </el-col>
                <el-col :span="8">
                    <el-select v-model="form.status_list" placeholder="请选择用例状态" @change="onFilter" style="width: 100%;"
                        multiple clearable>
                        <el-option label="待评审" value="PENDING" type="primary"></el-option>
                        <el-option label="评审中" value="REVIEWING" type="warning"></el-option>
                        <el-option label="评审通过" value="APPROVED" type="success"></el-option>
                        <el-option label="评审不通过" value="REJECTED" type="danger"></el-option>
                        <el-option label="废弃" value="DEPRECATED" type="info"></el-option>
                    </el-select>
                </el-col>
            </el-row>

            <el-row :gutter="10">
                <el-col :span="8">
                    <el-select v-model="form.priority_list" placeholder="请输入优先级" @change="onFilter" multiple clearable>
                        <el-option label="高" value="HIGH"></el-option>
                        <el-option label="中" value="MIDDLE"></el-option>
                        <el-option label="低" value="LOW"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="8">
                    <el-tree-select v-model="form.module" :data="modules" :props="{ label: 'name', value: 'm' }"
                        ref="moduleRef" placeholder="请选择所属模块" clearable multiple check-strictly
                        :render-after-expand="false" node-key="m" show-checkbox @check="onModuleCheckChange"
                        class="moduleSelect">
                    </el-tree-select>
                </el-col>
                <el-col :span="8">
                    <el-select v-model="form.action_type" @change="onFilter" placeholder="所有用例活动类型" multiple clearable>
                        <el-option v-for="item in action_types" :label="item.name" :value="item.number"></el-option>
                    </el-select>
                </el-col>
            </el-row>

            <el-row :gutter="10">
                <el-col :span="8">
                    <el-select v-model="form.execute_mode_list" placeholder="请选择执行方式" @change="onFilter" multiple
                        clearable>
                        <el-option label="自动化测试" value="AUTOMATED_EXECUTION"></el-option>
                        <el-option label="手动测试" value="MANUAL_EXECUTION"></el-option>
                        <el-option label="半自动化测试" value="SEMI_AUTOMATED_EXECUTION"></el-option>
                    </el-select>
                </el-col>
            </el-row>

        </div>

        <el-table ref="tableRef" :data="tableData" stripe height="600px" style="width: 100%" class="table-container">
            <el-table-column type="selection" width="50" fixed="left" />
            <el-table-column prop="name" label="用例名称" width="200">
                <template #default="{ row }">
                    <el-link type="primary" @click="onDetail(row)" :underline="false">{{ row.name }}</el-link>
                </template>
            </el-table-column>
            <el-table-column prop="number" label="用例ID" width="200"></el-table-column>
            <el-table-column prop="version" label="用例版本" width="100" align="left">
                <template #default="{ row }">
                    <el-tag type="promary">V{{ row.version }}.0</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="用例状态" align="left">
                <template #default="{ row }">
                    <el-tag v-if="row.status == 'PENDING'" type="primary">待评审</el-tag>
                    <el-tag v-else-if="row.status == 'REVIEWING'" type="warning">评审中</el-tag>
                    <el-tag v-else-if="row.status == 'APPROVED'" type="success">评审通过</el-tag>
                    <el-tag v-else-if="row.status == 'REJECTED'" type="danger">评审不通过</el-tag>
                    <el-tag v-else-if="row.status == 'DEPRECATED'" type="info">废弃</el-tag>
                    <el-tag v-else type="danger">未知</el-tag>
                </template>
            </el-table-column>

        </el-table>
    </div>

    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100, 1000]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
            @change="onFilter" :default-page-size="100" />
    </div>

    <div class="button-container">
        <el-button type="default" @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleAdd">添加</el-button>
    </div>

    <el-drawer v-model="drawerDetailVisible" :with-header="false" size="50%" :destroy-on-close="true">
        <TestCaseDetail :id="r_id" />
    </el-drawer>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import http from '@/utils/http/http.js';
import TestCaseDetail from '@/components/test_case.vue';

const props = defineProps({
    project_number: {
        type: String,
        default: ''
    },
    durability: {
        type: Boolean,
        default: false

    }
});

const tableData = ref(null);
const tableRef = ref(null);
let filterButtonIcon = ref("ArrowDown");
let showFilterContainer = ref(false);

const modules = ref([]);
const action_types = ref([]);
const total = ref(0);
const drawerDetailVisible = ref(false);
const r_id = ref(null);

const project_number = computed(() => props.project_number);
const durability = computed(() => props.durability);

const form = reactive({
    project_number: project_number.value ? project_number.value : "占位符",
    is_durable: durability.value,
    page: 1,
    pagesize: 100,
});

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
    if (showFilterContainer.value) {
        filterButtonIcon.value = "ArrowUp";
    } else {
        filterButtonIcon.value = "ArrowDown";
    }
};

function updateTableData() {
    http.get('/test_cases', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });
};

const emit = defineEmits(['confirm', 'cancel']);

function handleAdd() {
    const selectedRows = tableRef.value.getSelectionRows();
    emit('confirm', selectedRows);
}

function handleCancel() {
    emit('cancel');
}

function onFilter() {
    updateTableData();
}

function onModuleCheckChange(curData, checkedData) {
    let module1 = checkedData.checkedNodes.filter(node => node.level == 1).map(node => node.number);
    let module2 = checkedData.checkedNodes.filter(node => node.level == 2).map(node => node.number);
    let module3 = checkedData.checkedNodes.filter(node => node.level == 3).map(node => node.number);

    form.module_list = module1;
    form.module_2level_list = module2;
    form.module_3level_list = module3;
    updateTableData();
}

function onDetail(row) {
    r_id.value = row.id;
    drawerDetailVisible.value = true;
};


onMounted(() => {

    http.get('/functions', { params: { pagesize: 1000 } }).then(res => {
        let data = res.data.data.results;
        data.forEach(item => {
            item.m = item.number;
            if (item.children) {
                item.children.forEach(item2 => {
                    item2.m = item.number + '-' + item2.number;
                    if (item2.children) {
                        item2.children.forEach(item3 => {
                            item3.m = item.number + '-' + item2.number + '-' + item3.number;
                        });
                    }
                });
            }
        });
        modules.value = data;
    }).catch(err => {
        console.log(err);
    });

    http.get('/test_m/test_case_types', { params: { pagesize: 1000 } }).then(res => {
        let data = res.data.data.results;
        action_types.value = data;
    }).catch(err => {
        console.log(err);
    });

    updateTableData({ page: 1, pagesize: 100 });
});

</script>


<style lang="scss" scoped>
.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    margin-bottom: 10px;

    .el-select {
        width: 100%;
    }

    .el-row {
        margin-bottom: 10px;
    }
}

.button-container {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;

    margin-top: 10px;

}
</style>