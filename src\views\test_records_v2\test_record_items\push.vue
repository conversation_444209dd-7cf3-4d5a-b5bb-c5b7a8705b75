<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="问题名称" prop="title">
                <el-input v-model="form.title"></el-input>
            </el-form-item>

            <el-row>
                <el-col :span="12">
                    <el-form-item label="所属项目">
                        <el-input :value="`${recordItem.project_name}(${recordItem.project_number})`"
                            readonly></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="问题等级" prop="level">
                        <el-select v-model="form.level">
                            <el-option label="D-建议" value="SUGGEST"></el-option>
                            <el-option label="C-一般" value="NORMAL"></el-option>
                            <el-option label="B-严重" value="SERIOUS"></el-option>
                            <el-option label="A-致命" value="FATAL"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="12">
                    <el-form-item label="问题模块" prop="module">
                        <el-select v-model="form.module">
                            <el-option label="软件" value="SOFTWARE"></el-option>
                            <el-option label="结构" value="STRUCT"></el-option>
                            <el-option label="硬件" value="HARDWARE"></el-option>
                            <el-option label="光学" value="OPTICS"></el-option>
                            <el-option label="实验" value="EXPERIMENT"></el-option>
                            <el-option label="调试" value="DEBUG"></el-option>
                            <el-option label="对手件" value="OPPONENT"></el-option>
                            <el-option label="客户反馈" value="FEEDBACK"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="项目阶段" prop="occurProjectProgress">
                        <el-select v-model="form.occurProjectProgress" placeholder="请选择项目阶段">
                            <el-option label="KICK OFF" value="KICK OFF"></el-option>
                            <el-option label="TKO" value="TKO"></el-option>
                            <el-option label="DV" value="DV"></el-option>
                            <el-option label="PV" value="PV"></el-option>
                            <el-option label="PP" value="PP"></el-option>
                            <el-option label="MP" value="MP"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-form-item label="前置条件" prop="frontCondition">
                <el-input type="textarea" :rows="3" v-model="form.frontCondition"></el-input>
            </el-form-item>

            <el-form-item label="测试步骤" prop="testStep">
                <el-input type="textarea" :rows="3" v-model="form.testStep"></el-input>
            </el-form-item>

            <el-form-item label="预期结果" prop="expectResult">
                <el-input type="textarea" :rows="3" v-model="form.expectResult"></el-input>
            </el-form-item>

            <el-form-item label="实际结果" prop="actualResult">
                <el-input type="textarea" :rows="3" v-model="form.actualResult"></el-input>
            </el-form-item>

            <el-form-item label="备注">
                <el-input type="textarea" :rows="2" v-model="form.remark"></el-input>
            </el-form-item>

            <el-form-item label="主导人" prop="handlerIdList">
                <Organization :multiple="true" v-model="form.handlerIdList" />
            </el-form-item>

            <el-row>
                <el-col :span="12">
                    <el-form-item label="测试人">
                        <el-input :value="recordItem.tester_name" readonly></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="抄送人" prop="starIdList">
                        <Organization :multiple="true" v-model="form.starIdList" />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="12">
                    <el-form-item label="处理时间" prop="timeRange">
                        <el-date-picker v-model="form.timeRange" type="daterange" range-separator="-"
                            start-placeholder="开始时间" end-placeholder="结束时间" size="default" />
                        <el-input style="display: none"> </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="问题概率" :required="true">
                        <el-col :span="11">
                            <el-form-item prop="p1">
                                <el-input-number v-model="form.p1" :min="1" :controls="false" placeholder="出现次数"
                                    style="width: 100%"></el-input-number>
                            </el-form-item>
                        </el-col>
                        <el-col :span="2" style="text-align: center;">/</el-col>
                        <el-col :span="11">
                            <el-form-item prop="p2">
                                <el-input-number v-model="form.p2" :min="1" :controls="false" placeholder="出现次数"
                                    style="width: 100%"></el-input-number>
                            </el-form-item>
                        </el-col>
                        <el-input style="display: none"> </el-input>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-form-item label="产品产生版本" prop="occurVersionList">
                <el-tree-select ref="pVersionRef" v-model="form.occurVersionList" show-checkbox check-on-click-node lazy
                    multiple :load="loadVersions"
                    :props="{ label: 'label', value: 'value', children: 'children', isLeaf: 'isLeaf' }" />
            </el-form-item>

            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onAffirm">确认</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';

import http from '@/utils/http/http.js';

import dayjs from 'dayjs';

import Organization from '@/components/Organization/index.vue';

const props = defineProps({
    r_id: {
        type: Number,
        required: true,
    },
});

const formRef = ref(null);
const pVersionRef = ref(null);
const recordItem = ref({});
const testCase = ref({});
const today = new Date();
const daysLater = new Date();
daysLater.setDate(today.getDate() + 6);

const form = ref({
    title: '',
    projectCode: '',
    level: 'NORMAL',
    desc: '',
    occurProjectProgress: '',
    module: 'SOFTWARE',
    frontCondition: '',
    testStep: '',
    expectResult: '',
    actualResult: '',
    p1: 1,
    p2: 1,
    remark: '',
    timeRange: [today, daysLater],
    occurVersionList: [],
    testerOpenIds: '',
    handlerIdList: [],
    starIdList: [],
});

const rules = ref({
    title: [
        { required: true, message: '请输入问题名称', trigger: 'blur' },
    ],
    projectCode: [
        { required: true, message: '请输入项目编号', trigger: 'blur' },
    ],
    level: [
        { required: true, message: '请选择问题等级', trigger: 'blur' },
    ],
    occurProjectProgress: [
        { required: true, message: '请输入项目阶段', trigger: 'blur' },
    ],
    module: [
        { required: true, message: '请输入问题模块', trigger: 'blur' },
    ],
    frontCondition: [
        { required: true, message: '请输入前置条件', trigger: 'blur' },
    ],
    testStep: [
        { required: true, message: '请输入测试步骤', trigger: 'blur' },
    ],
    expectResult: [
        { required: true, message: '请输入预期结果', trigger: 'blur' },
    ],
    actualResult: [
        { required: true, message: '请输入实际结果', trigger: 'blur' },
    ],
    p1: [
        { required: true, message: '请输入出现次数', trigger: 'blur' },
    ],
    p2: [
        { required: true, message: '请输入出现次数', trigger: 'blur' },
    ],
    timeRange: [
        { required: true, message: '请选择处理时间', trigger: 'blur' },
    ],
    occurVersionList: [
        { required: true, message: '请选择产品产生版本', trigger: 'blur' },
    ],
    handlerIdList: [
        { required: true, message: '请选择主导人', trigger: 'blur' },
    ],
});

function loadVersions(node, resolve) {
    if (node.level === 0) {
        http.get('/issues/project_version_type').then(res => {
            let data = res.data.data.dictList;
            data = data.map(item => {
                item.value = item.code;
                item.isLeaf = false;
                return item;
            });
            resolve(data);
        });
    } else if (node.level === 1) {
        http.get('/issues/project_version_number',
            { params: { type: node.data.code, project_number: form.value.projectCode } }).then(res => {
                console.log(res);
                if (res.data.err_code != 0) {
                    resolve([{ label: '无数据', value: '无数据', isLeaf: true, disabled: true }]);
                    return;
                }
                let data = res.data.data.records;
                data = data.map(item => {
                    item.label = item.name;
                    item.value = item.name;
                    item.isLeaf = true;
                    return item;
                });
                resolve(data);
                if (data.length == 0) {
                    resolve([{ label: '无数据', value: '无数据', isLeaf: true, disabled: true }]);
                }
            });
    }
}

const emit = defineEmits(['confirm', 'cancel']);

const onAffirm = () => {
    let data = {
        id: props.r_id,
    };
    Object.assign(data, form.value);

    data.probability = data.p1 + '/' + data.p2;
    delete data.p1;
    delete data.p2;

    data.occurVersionList = pVersionRef.value.getCheckedNodes().filter(
        item => item.isLeaf
    ).map(item => {
        return {
            num: item.num,
            type: item.type,
        }
    });

    data.beginTime = dayjs(data.timeRange[0]).format("YYYY-MM-DD HH:mm:ss");
    data.endTime = dayjs(data.timeRange[1]).format("YYYY-MM-DD HH:mm:ss");
    delete data.timeRange;

    data.testerOpenIds = [data.testerOpenIds];

    data.test_case_number = testCase.value.number;
    data.test_case_version = Number(testCase.value.version);

    formRef.value.validate((valid) => {
        if (valid) {
            http.post("/v2/test_records/items/issue_push", data).then(res => {
                ElMessage({
                    message: '推送成功.',
                    type: 'success',
                });
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '推送失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onCancel = () => {
    emit('cancel');
};

onMounted(() => {
    http.get(`/v2/test_records/items/${props.r_id}`).then(res => {
        let data = res.data.data;
        recordItem.value = data;

        form.value.title = data.raw.test_case_name;
        form.value.projectCode = data.raw.project_number;
        form.value.testerOpenIds = data.tester_email;
        form.value.actualResult = data.raw.steps.map(item => item.step_actual).join('\n');

        http.get(`/test_cases/${data.test_case_id}`).then(res => {
            let data = res.data.data;
            testCase.value = data;

            form.value.frontCondition = data.preconditions;
            form.value.testStep = data.test_steps.map(item => item.desc).join('\n');
            form.value.expectResult = data.test_steps.map(item => item.expectation).join('\n');
            form.value.occurProjectProgress = data.project_phase;
            form.value.remark = data.remark;
        })
    });
});

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>