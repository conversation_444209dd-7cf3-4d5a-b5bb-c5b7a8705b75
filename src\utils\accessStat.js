import { onMounted, onActivated } from 'vue';
import http from '@/utils/http/http.js';
import { useUserStore } from '@/stores/user.js';


export function useAccessStat(pageUrl, pageTitle) {
    const userStore = useUserStore();

    const accessStat = () => {
        const open_id = userStore.user_info.open_id;
        http.post('/auto_test/access_stat', {
            user_id: open_id,
            page_url: pageUrl,
            page_title: pageTitle,
        })
    };

    let isFirstLoad = true;

    onMounted(() => {
        accessStat();
    });

    onActivated(() => {
        if (isFirstLoad) {
            isFirstLoad = false
            return;
        }
        accessStat();
    });
}