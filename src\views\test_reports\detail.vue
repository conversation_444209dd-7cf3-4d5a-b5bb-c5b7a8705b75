<template>

    <div>
        <div style="display: flex; align-items: center;">
            <h2> {{ testPlan.name }} </h2>
        <el-button @click="onBack" style="margin-left: auto;">返回</el-button>
        </div>

        <div class="tool-container">
            <el-button  text bg icon="Download" @click="handleDownload">下载报告</el-button>
           
        </div>

    </div>

    <el-collapse v-model="activeNames">

        <el-collapse-item title="测试总结" name="1">
            <div>
                <el-form style="padding: 20px" label-width="auto">
                    <el-form-item label="所属项目：">
                        <span>{{ testPlan.project_name }}({{ testPlan.project_number }})</span>
                    </el-form-item>
                    <el-form-item label="软件版本：">
                        <el-tag>{{ testPlan.software_version }}</el-tag>
                    </el-form-item>

                    <template v-for="pv in (testPlan?.product_version || [])">
                        <el-form-item v-if="pv.type_name" :label="pv.type_name + '：'">
                            <el-tag>{{ pv.name }}</el-tag>
                        </el-form-item>
                    </template>

                    <el-form-item label="测试结论：">
                        <el-tag type="success" v-if="testPlan.stats?.result">通过</el-tag>
                        <el-tag type="danger" v-else>不通过</el-tag>
                    </el-form-item>
                    <el-form-item label="测试人员：">
                        <span>{{ testPlan.creator_name }}(计划创建人员)</span>
                        <template v-for="sp in (testPlan.sub_plans || [])">
                            <el-divider direction="vertical" />
                            <span v-if="testPlan.stats?.sp_stats[sp.id].tester_name">{{
            testPlan.stats?.sp_stats[sp.id].tester_name }}({{ sp.name }}子计划执行人员)</span>
                        </template>
                    </el-form-item>
                    <el-form-item label="开始日期：">
                        <span>{{ testPlan.stats?.start_time }}</span>
                    </el-form-item>
                    <el-form-item label="完成日期：">
                        <span> {{ testPlan.stats?.end_time }} </span>
                    </el-form-item>
                </el-form>
            </div>
        </el-collapse-item>

        <el-collapse-item title="测试用例汇总分析" name="2">

            <el-table :data="testPlan.stats?.m_stats || []" stripe style="width: 100%" class="table-container"
                height="200">

                <el-table-column prop="module" label="功能模块" width="200" align="center">
                    <template #default="{ row }">
                        <span>{{ moduleMap[row.module] || row.module }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="total" label="测试用例总数" width="200" align="center"></el-table-column>
                <el-table-column prop="non_exec" label="未执行用例数" width="200" align="center"></el-table-column>
                <el-table-column prop="pass" label="通过" width="100" align="center"></el-table-column>
                <el-table-column prop="ng" label="不通过" width="100" align="center"></el-table-column>
                <el-table-column prop="pass_rate" label="通过率" width="100" align="center">
                    <template #default="{ row }">
                        <span>{{ (row.pass_rate * 100).toFixed(2) }}%</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="name" label="问题等级 -致命(A)" width="200" align="center"></el-table-column>
                <el-table-column prop="project_name" label="问题等级 - 严重(B)" width="200" align="center"></el-table-column>
                <el-table-column prop="software_version" label="问题等级 - 一般(C)" width="200" align="center"></el-table-column>
                <el-table-column prop="name" label="问题等级 - 建议(D)" width="200" align="center"></el-table-column> -->
                <el-table-column label="测试结果" width="200" align="center">
                    <template #default="{ row }">
                        <el-tag v-if="row.result" type="success">通过</el-tag>
                        <el-tag v-else type="danger">不通过</el-tag>
                    </template>
                </el-table-column>

            </el-table>

        </el-collapse-item>

        <el-collapse-item title="子计划" name="3">
            <el-tabs type="border-card">
                <el-tab-pane v-for="sp in (testPlan.sub_plans || [])" :label="sp.name">

                    <div style="padding: 20px;">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form label-width="auto">
                                    <el-form-item label="测试记录：">
                                        <el-button type="primary" size="small"
                                            @click="onTestRecordItems(testPlan.stats?.sp_stats[sp.id].test_record_id || 'null')"
                                            :underline="false">查看记录</el-button>
                                    </el-form-item>
                                    <el-form-item label="测试人员：">
                                        <span>{{ testPlan.stats?.sp_stats[sp.id].tester_name }}</span>
                                    </el-form-item>
                                    <el-form-item label="测试时间：">
                                        <template v-if="testPlan.stats?.sp_stats[sp.id].start_time">
                                            <span>{{ testPlan.stats?.sp_stats[sp.id].start_time }}</span>
                                            <el-divider direction="vertical" />
                                            <span>{{ testPlan.stats?.sp_stats[sp.id].end_time }}</span>
                                        </template>
                                    </el-form-item>
                                </el-form>
                            </el-col>
                            <el-col :span="12">
                                <el-form label-width="auto">
                                    <el-form-item label="测试状态：">
                                        <el-tag v-if="testPlan.stats?.sp_stats[sp.id].plan_status === null"
                                            type="danger">未开始</el-tag>
                                        <el-tag v-else-if="testPlan.stats?.sp_stats[sp.id].plan_status === 0"
                                            type="warning">进行中</el-tag>
                                        <el-tag v-else-if="testPlan.stats?.sp_stats[sp.id].plan_status === 1"
                                            type="success">已完成</el-tag>
                                    </el-form-item>
                                    <el-form-item label="PASS：">
                                        <el-tag type="success">{{ testPlan.stats?.sp_stats[sp.id].pass
                                            }}</el-tag>
                                    </el-form-item>
                                    <el-form-item label="NG：">
                                        <el-tag type="danger">{{ testPlan.stats?.sp_stats[sp.id].ng
                                            }}</el-tag>
                                    </el-form-item>
                                    <el-form-item label="待判定：">
                                        <el-tag type="warning">{{ testPlan.stats?.sp_stats[sp.id].unknown
                                            }}</el-tag>
                                    </el-form-item>
                                </el-form>
                            </el-col>
                        </el-row>
                    </div>

                    <h4>测试用例：</h4>
                    <el-table :data="sp.test_cases" stripe style="width: 100%;">
                        <el-table-column type="index" label="序号" width="100"></el-table-column>
                        <el-table-column prop="name" label="名称" width="300">
                            <template #default="{ row }">
                                <el-link type="primary" @click="onTestCaseDetail(row)" :underline="false">{{ row.name
                                    }}</el-link>
                            </template>
                        </el-table-column>
                        <el-table-column prop="cycle" label="计划执行次数" width="120"></el-table-column>
                        <el-table-column label="实际执行次数" width="120">
                            <template #default="{ row }">
                                <span>{{ testPlan.stats?.sp_stats[sp.id].tc_stats[row.id]?.exec || 0 }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="失败次数" width="120">
                            <template #default="{ row }">
                                <span>{{ testPlan.stats?.sp_stats[sp.id].tc_stats[row.id]?.ng || 0 }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="失败率" width="120">
                            <template #default="{ row }">
                                <span>{{ (testPlan.stats?.sp_stats[sp.id].tc_stats[row.id]?.ng_rate * 100 || 0).toFixed(2) }}%</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="执行结果" width="120">
                            <template #default="{ row }">
                                <el-tag v-if="!testPlan.stats?.sp_stats[sp.id].tc_stats[row.id]?.exec" type="warning">NA</el-tag>
                                <el-tag v-else-if="testPlan.stats?.sp_stats[sp.id].tc_stats[row.id]?.pass == row.cycle"
                                    type="success">PASS</el-tag>
                                <el-tag v-else type="danger">NG</el-tag>
                            </template>
                        </el-table-column>
                    </el-table>

                </el-tab-pane>

            </el-tabs>
        </el-collapse-item>

    </el-collapse>

    <el-drawer v-model="drawerDetailVisible" :with-header="false" size="60%" :destroy-on-close="true">
        <TestCaseDetail :testCaseData="test_case" />
    </el-drawer>

    <el-drawer v-model="drawerTestRecordItemsVisible" :with-header="false" size="60%" :destroy-on-close="true">
        <TestRecordItems :id="test_record_id" />
    </el-drawer>

</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import http from '@/utils/http/http.js';
import { useRouter, useRoute } from 'vue-router';
import TestCaseDetail from '@/components/test_case.vue';
import TestRecordItems from '@/views/test_records/test_record_items/index.vue';

const activeNames = ref(['1', '2', '3']);
const router = useRouter();
const route = useRoute();
const test_case = ref({});
const drawerDetailVisible = ref(false);
const drawerTestRecordItemsVisible = ref(false);
let moduleMap = ref({});
const testPlan = ref({});
const test_record_id = ref(null);

function onTestRecordItems(id) {
    test_record_id.value = id;
    drawerTestRecordItemsVisible.value = true;
};


function onTestCaseDetail(row) {
    test_case.value = row;
    drawerDetailVisible.value = true;
};


function handleDownload() {
    window.open(import.meta.env.VITE_BASE_URL + `/test_reports/${route.params.id}/download`);
};

function onBack() {
    router.push({ path: '/test_reports' });
}

onMounted(() => {
    http.get(`/test_reports/${route.params.id}`).then(res => {
        testPlan.value = res.data.data;

        console.log(testPlan.value);
    });

    http.get('/functions').then(res => {
        res.data.data.results.forEach(item => {
            moduleMap.value[item.number] = item.name;
            if (item.children) {
                item.children.forEach(item2 => {
                    moduleMap.value[item.number + '-' + item2.number] = item2.name;
                    if (item2.children) {
                        item2.children.forEach(item3 => {
                            moduleMap.value[item.number + '-' + item2.number + '-' + item3.number] = item3.name;
                        });
                    }
                });
            }
        });
    });
});



</script>

<style lang="scss" scoped>

.tool-container {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
}

:deep(.el-form-item) {
    margin: 0;
}

:deep(.el-collapse-item__arrow) {
    order: -1;
    margin: 0 8px 0 0;
}

:deep(.el-collapse-item__header) {
    font-size: 16px;
}
</style>