<template>
    <div class="el-empty">
        <el-image :src="emptyImage" style="width: 160px;" />
        <div style="margin-top: var(--el-empty-description-margin-top);">
            <p style="color: var(--el-text-color-secondary); font-size: var(--el-font-size-base);">功能开发中</p>
        </div>
    </div>
</template>

<script setup>
import emptyImage from '@/assets/images/empty.png';

</script>

<style lang="css" scoped>
.el-empty {
    height: 100%;
    align-items: center;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: var(--el-empty-padding);
    text-align: center;
}
</style>