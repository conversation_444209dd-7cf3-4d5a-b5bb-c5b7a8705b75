<template>
    <!-- <div class="search-container">
        <el-input placeholder="请输入搜索内容" class="search-input">
            <template #append>
                <el-button icon="Search" @click="onSearch" class="search-button">搜索</el-button>
            </template>
        </el-input>
    </div> -->

    <div class="tool-bar-container">
        <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button>
        <el-button type="info" plain @click="onFilterStatusChange" >
            筛选<el-icon class="el-icon--right" ><component :is="filterButtonIcon"></component></el-icon>
        </el-button>
    </div>

    <div class="filter-container" v-if="showFilterContainer">
        <el-input v-model="form.name" size="large" placeholder="请输入名称" suffix-icon="Search" @keyup.enter="onFilter"></el-input>
        <el-input v-model="form.number" size="large" placeholder="请输入编号" suffix-icon="Search" @keyup.enter="onFilter"></el-input>
    </div>

    <div class="table-container">
        <el-table :data="tableData" stripe border style="width: 100%">

            <el-table-column prop="name" label="名称" width="200" align="center"></el-table-column>
            <el-table-column prop="number" label="项目编号" width="200" align="center"></el-table-column>
            <el-table-column label="消息推送人" width="300" align="center">
                <template #default="{ row }">
                    <el-tag v-if="row.related_people" v-for="item in JSON.parse(row.related_people)" :key="item.id" type="success">{{ item.name }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="msg_effective_time_start" label="消息推送-开始时间" width="200" align="center"></el-table-column>
            <el-table-column prop="msg_effective_time_end" label="消息推送-结束时间" width="200" align="center"></el-table-column>

            <el-table-column label="操作" min-width="150" fixed="right" align="left">
                <template #default="{ row }">
                    <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                    <el-popconfirm confirmButtonText="确定" cancelButtonText="取消" title="确定删除吗？" placement="top"
                        @confirm="handleDelete(row)">
                        <template #reference>
                            <el-button type="danger" size="small">删除</el-button>
                        </template>
                    </el-popconfirm>
                </template>
            </el-table-column>

        </el-table>
    </div>

     <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            :total="total" background @change="onPaginate" />
     </div>

    <el-dialog v-if="dialogAddVisible" v-model="dialogAddVisible" title="添加项目" width="800"
        :close-on-click-modal="false">
        <ProjectAdd @affirm="onAddAffirm" @cancel="onAddCancel" />
    </el-dialog>

    <el-dialog v-if="dialogEditVisible" v-model="dialogEditVisible" title="编辑项目" width="800"
        :close-on-click-modal="false">
        <ProjectEdit @affirm="onEditAffirm" @cancel="onEditCancel" :r_id="r_id" />
    </el-dialog>

</template>


<script setup>

import { ref, reactive, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import ProjectAdd from './add.vue';
import ProjectEdit from './edit.vue';

const tableData = ref([]);

let r_id = ref(0);

const dialogAddVisible = ref(false);

const dialogEditVisible = ref(false);

let form = reactive({
    name: '',
    number: '',
});

let total = ref(0);

let searchParams = {};

let showFilterContainer = ref(false);
let filterButtonIcon = ref("ArrowDown");

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
    if (showFilterContainer.value) {
        filterButtonIcon.value = "ArrowUp";
    } else {
        filterButtonIcon.value = "ArrowDown";
    }
};

function update_table(params) {
    Object.assign(searchParams, params)
    http.get('/projects', { params: searchParams }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });
};

function onSearch() {
    update_table({});
};

function onFilter() {
    update_table(form);
};

function onPaginate(currentPage, pageSize) {
    update_table({ page: currentPage, pagesize: pageSize });
};

function handleAdd() {
    dialogAddVisible.value = true;
};

function handleEdit(row) {
    r_id.value = row.id;
    dialogEditVisible.value = true;
};

function handleDelete(row) {
    http.delete(`/projects/${row.id}`).then(res => {
        ElMessage({
            message: '删除成功.',
            type: 'success',
        });
        update_table();
    }).catch(err => {
        console.log(err);
    });
};

function onAddAffirm() {
    dialogAddVisible.value = false;
    update_table();
};

function onAddCancel() {
    dialogAddVisible.value = false;
};

function onEditAffirm() {
    dialogEditVisible.value = false;
    update_table();
};

function onEditCancel() {
    dialogEditVisible.value = false;
};

onMounted(() => {
    update_table();
});

</script>


<style scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
  border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}
</style>