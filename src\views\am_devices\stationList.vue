<template>
    <div style="display: flex; flex-direction: column; height: calc(100vh - 250px);">
        <div class="tool-bar-container" style="display: flex;">
            <div style="margin-left: auto; display: flex; gap: 10px;">
                <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                    <el-button text bg @click="handleReset">重置</el-button>
                </el-tooltip>
                <filterButton @click="onFilterStatusChange" :count="filterCount" />
                <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
            </div>
        </div>

        <div class="filter-container" v-if="showFilterContainer">
            <el-input v-model="form.name" placeholder="请输入名称" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
            <el-input v-model="form.code" placeholder="请输入编号" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
        </div>

        <el-table :data="tableData" stripe style="width: 100%;flex: 1;" class="table-container">
            <el-table-column type="expand">
                <template #default="{ row }">
                    <div style="padding: 20px; background-color: #f5f5f5;">
                        <!-- <h3>在线设备：</h3> -->
                        <el-table :data="deviceData[row.id] || []" stripe>
                            <el-table-column label="设备名称" prop="name" width="200" align="center" />
                            <el-table-column label="设备描述" prop="desc" min-width="300" align="center" />
                            <el-table-column label="设备ID" prop="device_id" width="200" align="center" />
                            <el-table-column label="PSN" prop="psn" width="200" align="center" />
                            <el-table-column label="操作" min-width="130" fixed="right" align="center">
                                <template #default="{ row }">
                                    <div style="display: flex; justify-content: center; gap: 10px;">
                                        <el-button type="primary" size="small" @click="handleDetail(row)">详情</el-button>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="name" label="工位名称" min-width="200" align="center"></el-table-column>
            <el-table-column prop="code" label="工位编号" min-width="150" align="center"></el-table-column>
            <el-table-column prop="desc" label="工位描述" min-width="300" align="center"></el-table-column>

            <el-table-column label="状态" min-width="150" fixed="right" align="center">
                <template #default="{ row }">
                    <el-tag v-if="deviceData[row.id]" type="success">使用中</el-tag>
                    <el-tag v-else type="info">空闲</el-tag>
                </template>
            </el-table-column>

        </el-table>
    </div>

    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
            @change="onPageChange" />
    </div>

</template>


<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import http from '@/utils/http/http2.js';
import filterButton from '@/components/filterButton.vue';
import { useRouter } from 'vue-router';

const tableData = ref([]);
const filterCount = ref(0);
const router = useRouter();

let form = reactive({
    page: 1,
    pagesize: 15,
});

let total = ref(0);

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/monitor/stations', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleRefresh() {
    update_table();
};

const wsConnected = ref(false);
let ws = null;
let wsStop = false;
const deviceData = ref({});

function handleDetail(row) {
    let key = row.key;
    router.push({ path: '/am_devices/' + key });
}

const initWebSocket = () => {
    const wsUrl = import.meta.env.VITE_AM_BASE_WS_URL + '/monitor/devices';
    ws = new WebSocket(wsUrl);

    ws.onopen = () => {
        wsConnected.value = true;
    };

    ws.onmessage = (event) => {
        try {
            const data = JSON.parse(event.data);

            deviceData.value = {};
            data.forEach(item => {
                if (!item.station_id) {
                    return;
                }
                const id = item.station_id;
                if (!deviceData.value[id]) {
                    deviceData.value[id] = [];
                }
                deviceData.value[id].push(item);
            });

        } catch (error) {
            console.error('处理 WebSocket 消息时出错:', error);
        }
    };

    ws.onclose = () => {
        if (wsStop) {
            return;
        }
        wsConnected.value = false;
        ElMessage.warning('设备监控连接已断开，正在尝试重新连接...');
        setTimeout(initWebSocket, 1000);
    };

    ws.onerror = (error) => {
        wsConnected.value = false;
        ElMessage.error('设备监控连接错误');
        console.error('WebSocket 错误:', error);
    };
};

onMounted(() => {
    update_table();
    initWebSocket();
});

onBeforeUnmount(() => {
    wsStop = true;
    if (ws) {
        ws.close();
        ws = null;
    }
});

</script>


<style scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}
</style>