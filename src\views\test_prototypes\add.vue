<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="名称" prop="name">
                <el-input v-model="form.name"></el-input>
            </el-form-item>

            <el-form-item label="样件类型" prop="type">
                 <el-select v-model="form.type">
                     <el-option label="总成" value="0"></el-option>
                     <el-option label="VDS" value="1"></el-option>
                     <el-option label="主机" value="2"></el-option>
                     <el-option label="PCBA" value="3"></el-option>
                     <el-option label="屏模组" value="4"></el-option>
                 </el-select>
            </el-form-item>

            <el-form-item label="样件状态" prop="status">
                <el-select v-model="form.status">
                    <el-option label="正常" value="0"></el-option>
                    <el-option label="故障" value="1"></el-option>
                    <el-option label="拆解" value="2"></el-option>
                    <el-option label="报废" value="3"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="关联项目" prop="project">
                <ProjectsComponent ref="projectsRef" v-model="form.project" :includePrefix="false"
                :includeAll="false" />
            </el-form-item>

            <el-form-item label="维护人员" prop="user_email">
                <Orga v-model="form.user_email" ref="prototypesRef" />
            </el-form-item>

            <el-form-item label="描述">
                <el-input type="textarea" :rows="4" v-model="form.desc"></el-input>
            </el-form-item>

            <div class="submit-button-container">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import http from '@/utils/http/http.js';
import Orga from '@/components/Organization/index.vue';
import ProjectsComponent from '@/components/projects.vue';

const formRef = ref(null);
const prototypesRef = ref(null);

const form = ref({
    name:'',
    type: '',
    status:'',
    project:'',
    user_email:'',
    desc: '',
});

const rules = ref({
    name:[
        { required: true, message:'请输入样件名称', trigger:'blur'}
    ],
    type: [
        { required: true, message: '请输入样件类型', trigger: 'blur' },
    ],
    status:[
        { required: true, message: '请输入样件状态', trigger:'blur'}
    ],
    project:[
        { required: true, message: '请输入关联项目', trigger: 'blur'}
    ],
    user_email:[
        { required: true, message:'请输入维护人员', trigger: 'blur'}
    ],

});

const emit = defineEmits(['submit', 'cancel'])

const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            let prototypes = prototypesRef.value.getNode(form.value.user_email);
            form.value.user_name = prototypes.label;
            http.post('/test_prototypes', form.value).then(res => {
                emit('submit');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onCancel = () => {
    emit('cancel');
};

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>

