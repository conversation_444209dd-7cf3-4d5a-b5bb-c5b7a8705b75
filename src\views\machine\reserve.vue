<template>
    <el-divider />

    <el-row>

        <el-col :span="15">
            <div class="canlendar-container">
                <FullCalendar :options="calendarOptions" ref="canlendarRef" />
            </div>
        </el-col>
        <el-col :span="9">
            <div class="form-container">
                <el-form :model="form" label-width="85px" :rules="rules" status-icon ref="formRef">

                    <el-form-item label="开始时间" prop="start_time">
                        <el-date-picker v-model="form.start_time" type="datetime" placeholder="选择日期时间">
                        </el-date-picker>
                    </el-form-item>

                    <el-form-item label="结束时间" prop="end_time">
                        <el-date-picker v-model="form.end_time" type="datetime" placeholder="选择日期时间">
                        </el-date-picker>
                    </el-form-item>

                    <el-form-item label="项目" prop="project">
                        <el-select v-model="form.project" placeholder="请选择项目" filterable>
                            <el-option v-for="item in projects" :label="`${item.name}(${item.code})`"
                                :value="item.code"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="测试内容" prop="content">
                        <el-input type="textarea" :rows="3" v-model="form.content"></el-input>
                    </el-form-item>

                    <el-form-item label="备注">
                        <el-input type="textarea" v-model="form.comment"></el-input>
                    </el-form-item>

                    <el-form-item label="多项目共用">
                        <el-switch v-model="form.is_share" size="large" inline-prompt active-text="是"
                            inactive-text="否" />
                    </el-form-item>

                    <el-form-item label="使用模式" prop="t_mode">
                        <el-select v-model="form.t_mode" placeholder="请选择使用模式" filterable>
                            <el-option label="测试模式" value="TEST"></el-option>
                            <el-option label="调试模式" value="DEBUG"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="执行人员" prop="executive_personnel">
                        <Organizaiton v-model="form.executive_personnel" ref="executive_personnel" :cache-data="cacheData" />
                    </el-form-item>

                    <div class="submit-button-container">
                        <el-button type="default" @click="onCancel">取消</el-button>
                        <el-button :loading="loading" type="primary" @click="onConfirm">提交</el-button>
                    </div>
                </el-form>
            </div>
        </el-col>
    </el-row>
</template>

<script setup>
import { ref, onMounted, h } from 'vue';
import FullCalendar from '@fullcalendar/vue3'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import listPlugin from '@fullcalendar/list'
import interactionPlugin from '@fullcalendar/interaction'
import dayjs from 'dayjs'

import http from '@/utils/http/http.js';

import Organizaiton from '@/components/Organization/index.vue';

const cacheData = ref([]);
const canlendarRef = ref(null);
const formRef = ref(null);

const loading = ref(false);

const projects = ref([]);

const form = ref({
    start_time: '',
    end_time: '',
    project: '',
    content: '',
    comment: '',
    executive_personnel: '',
    is_share: false,
    t_mode: 'TEST',
});

const validateStartTime = (rule, value, callback) => {
    if (!value) {
        return callback(new Error('请选择开始时间'));
    }
    if (dayjs(value).isBefore(dayjs().add(15, 'minute'))) {
        return callback(new Error('开始时间需要提前15分钟'));
    }
    callback();
};

const validateEndTime = (rule, value, callback) => {
    if (!value) {
        return callback(new Error('请选择结束时间'));
    }
    if (dayjs(value).isBefore(form.value.start_time)) {
        return callback(new Error('结束时间不能早于开始时间'));
    }
    callback();
};

const rules = ref({
    start_time: [
        { required: true, message: '请选择开始时间', trigger: 'blur' },
        { validator: validateStartTime, trigger: 'blur' },
    ],
    end_time: [
        { required: true, message: '请选择结束时间', trigger: 'blur' },
        { validator: validateEndTime, trigger: 'blur' },
    ],
    project: [
        { required: true, message: '请输入项目名称', trigger: 'blur' },
    ],
    content: [
        { required: true, message: '请输入测试内容', trigger: 'blur' },
    ],
    t_mode: [
        { required: true, message: '请选择使用模式', trigger: 'blur' },
    ],
    executive_personnel: [
        { required: true, message: '请选择执行人员', trigger: 'blur' },
    ],
});

const props = defineProps({
    r_id: {
        type: Number,
        required: true,
    },
});

const calendarOptions = ref({
    plugins: [dayGridPlugin, interactionPlugin, timeGridPlugin, listPlugin],
    initialView: 'timeGridWeek',
    headerToolbar: {
        left: 'prev,next today',
        center: 'title',
        right: 'dayGridMonth,timeGridWeek,timeGridDay',
    },
    buttonText: {
        today: '今天',
        month: '月',
        week: '周',
        day: '日',
        list: '列表'
    },
    firstDay: 1,
    allDayText: '全天',
    locale: 'zh-cn',
    height: '100%',
    events: (fetchInfo, successCallback, failureCallback) => {
        let start = fetchInfo.startStr;
        let end = fetchInfo.endStr;

        http.get("/machines/reservations", {
            params: {
                machine_id: props.r_id,
                start_time: start,
                end_time: end,
                status_list: [1, 3],
            }
        }).then(res => {
            let events = res.data.data.results.map(item => {
                return {
                    title: item.project + " " + item.user,
                    start: item.start_time,
                    end: item.end_time,
                }
            });
            successCallback(events);
        });
    },
})

const emit = defineEmits(['confirm', 'cancel']);

const onConfirm = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            let data = {
                ...form.value,
            };

            data.machine_id = props.r_id;
            data.start_time = dayjs(data.start_time).format('YYYY-MM-DD HH:mm:ss');
            data.end_time = dayjs(data.end_time).format('YYYY-MM-DD HH:mm:ss');

            loading.value = true;

            http.post("/machines/reservations", data).then(res => {
                ElMessage({
                    message: '添加成功.',
                    type: 'success',
                });
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            }).finally(() => {
                loading.value = false;
            });

        };
    });
};

const onCancel = () => {
    emit('cancel');
};

onMounted(() => {
    http.get('/projects/p', { params: { pagesize: 10000 } }).then(res => {
        let data = res.data.data.results;
        projects.value = data;
    }).catch(err => {
        console.log(err);
    });
});

</script>

<style lang="scss" scoped>
.canlendar-container {
    width: 100%;
    height: 600px;
    padding: 20px;
}

.form-container {
    display: flex;
    justify-content: center;
    height: 100%;
    padding-top: 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}


</style>