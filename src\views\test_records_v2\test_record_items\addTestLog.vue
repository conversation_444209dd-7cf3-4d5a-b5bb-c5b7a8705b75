<template>
    <div>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="auto" status-icons>
            <el-form-item label="测试版本" prop="task_version">
                <el-input type="textarea" v-model="form.task_version" placeholder="请输入备注"></el-input>
            </el-form-item>
            <el-form-item label="测试方案" prop="task_scheme">
                <el-input type="textarea" v-model="form.task_scheme" placeholder="请输入备注"></el-input>
            </el-form-item>
            <el-form-item label="测试结论" prop="task_conclusion">
                <el-input type="textarea" v-model="form.task_conclusion" placeholder="请输入备注"></el-input>
            </el-form-item>
        </el-form>
        
        <div class="submit-button-container">
            <el-button type="default" @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onAffirm" :loading="loading">确认</el-button>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import http from '@/utils/http/http.js';

const props = defineProps({
    bug_id: {
        type: Number,
        required: true
    }
})
const formRef = ref(null)

const form = ref({
    bug_id: props.bug_id,
    task_version: '',
    task_scheme: '',
    task_conclusion: '',
})

const rules = ref({
    task_version: [
        { required: true, message: '请输入测试版本', trigger: 'blur' }
    ],
    task_scheme: [
        { required: true, message: '请输入测试方案', trigger: 'blur' }
    ],
    task_conclusion: [
        { required: true, message: '请输入测试结论', trigger: 'blur' }
    ]
})

const loading = ref(false)

const emit = defineEmits(
    'cancel',
    'confirm'
)

const onCancel = () => {
    emit('cancel')
}

const onAffirm = () => {
    formRef.value.validate((valid) => {
        if (valid) {
            loading.value = true
            http.post("/bugs/add_test_record", form.value).then(res => {
                ElMessage({
                    message: '添加成功.',
                    type: 'success',
                });
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '添加失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            }).finally(() => {
                loading.value = false
            });
        }
    })
}
</script>


<style lang="scss" scoped>
.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>