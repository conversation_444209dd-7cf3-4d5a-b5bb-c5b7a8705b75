<template>
    <div class="device-detail-container">
        <div class="status-bar">
            <div class="connection-status">
                <el-tag :type="wsConnected ? 'success' : 'danger'" effect="dark">
                    {{ wsConnected ? '已连接' : '连接断开' }}
                </el-tag>
                <span class="timestamp">更新时间: {{ lastUpdateTime }}</span>
            </div>

            <div class="actions">
                <el-button size="small" type="primary" @click="handleBack">返回</el-button>
            </div>
        </div>

        <el-card class="device-info-card">
            <template #header>
                <div class="card-header">
                    <div class="device-title">
                        <h2>{{ deviceName || '设备状态' }}</h2>
                        <el-tag size="large" :type="deviceStatus === '正常' ? 'success' : 'danger'">
                            {{ deviceStatus }}
                        </el-tag>
                    </div>
                    <!-- <div class="device-meta">
                        <span><strong>PSN:</strong> {{ deviceId }}</span>
                    </div> -->
                </div>
            </template>

            <div class="card-content">
                <div class="view-options">
                    <el-radio-group v-model="currentViewMode">
                        <el-radio-button label="dashboard">卡片</el-radio-button>
                        <el-radio-button label="table">列表</el-radio-button>
                    </el-radio-group>
                </div>

                <!-- 仪表板视图 -->
                <div v-if="currentViewMode === 'dashboard'" class="dashboard-view view-container">
                    <el-scrollbar>
                        <div class="metrics-grid">
                            <el-card v-for="(metric, index) in metrics" :key="index" class="metric-tile"
                                :class="{ 'metric-alert': metric.status !== 0 }" :body-style="{ padding: '0' }"
                                shadow="hover">
                                <div class="metric-content">
                                    <div class="metric-tile-header">
                                        <div class="metric-name-container">
                                            <h3 class="metric-name">{{ metric.name }}</h3>
                                            <span class="metric-code">{{ metric.code }}</span>
                                        </div>
                                        <el-tag :type="metric.status === 0 ? 'success' : 'danger'" size="small"
                                            effect="dark" class="status-tag">
                                            {{ metric.status === 0 ? '正常' : '异常' }}
                                        </el-tag>
                                    </div>

                                    <div class="metric-tile-content">
                                        <div class="value-section">
                                            <div class="metric-value">{{ metric.value }}</div>
                                            <div class="metric-timestamp">{{ metric.cur_time }}</div>
                                        </div>

                                        <!-- 状态指示器 -->
                                        <div class="status-indicator"
                                            :class="{ 'status-normal': metric.status === 0, 'status-alert': metric.status !== 0 }">
                                            <el-icon :size="28" v-if="metric.status === 0">
                                                <Check />
                                            </el-icon>
                                            <el-icon :size="28" v-else>
                                                <Warning />
                                            </el-icon>
                                        </div>
                                    </div>

                                    <!-- <div v-if="metric.extra_info" class="metric-extra-info">
                                        <span>{{ metric.extra_info }}</span>
                                    </div> -->

                                    <div class="metric-actions">
                                        <el-button size="small" type="primary" text @click="viewMetricHistory(metric)">
                                            <el-icon>
                                                <Histogram />
                                            </el-icon> 历史记录
                                        </el-button>
                                        <el-button size="small" type="info" text @click="viewMetricDetails(metric)">
                                            <el-icon>
                                                <InfoFilled />
                                            </el-icon> 详情
                                        </el-button>
                                    </div>
                                </div>
                            </el-card>
                        </div>
                    </el-scrollbar>
                </div>

                <!-- 表格视图 -->
                <div v-else-if="currentViewMode === 'table'" class="table-view view-container">
                    <el-table :data="metrics" stripe style="width: 100%" border height="calc(100vh - 460px)">
                        <el-table-column prop="name" label="名称" width="250" align="center" />
                        <el-table-column prop="code" label="编码" width="250" align="center" />
                        <el-table-column prop="value" label="值" width="150" align="center" />
                        <el-table-column label="状态" width="120" align="center">
                            <template #default="{ row }">
                                <el-tag :type="row.status === 0 ? 'success' : 'danger'">
                                    {{ row.status === 0 ? '正常' : '异常' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="extra_info" label="额外信息" min-width="200" align="center" />
                        <el-table-column label="操作" width="150" fixed="right" align="center">
                            <template #default="scope">
                                <el-button size="small" type="primary" @click="viewMetricHistory(scope.row)">
                                    <el-icon>
                                        <Histogram />
                                    </el-icon>
                                </el-button>
                                <el-button size="small" type="info" @click="viewMetricDetails(scope.row)">
                                    <el-icon>
                                        <InfoFilled />
                                    </el-icon>
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="metrics-summary">
                        <span class="metrics-total">总计: <strong>{{ metrics_total }}</strong> 项</span>
                        <span class="metrics-normal">正常: <strong>{{ metrics_total - metrics_exp }}</strong> 项</span>
                        <span class="metrics-error">异常: <strong>{{ metrics_exp }}</strong> 项</span>
                    </div>
                </div>
            </div>
        </el-card>

        <el-dialog v-if="historyVisible" v-model="historyVisible" :title="`${currentMetric.name}(${currentMetric.code}) 历史记录`" width="1000"
            :close-on-click-modal="false">
            <MetricHistory :meta="currentMeta" :metric="currentMetric" />
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import {
    Refresh, Connection, InfoFilled, Histogram,
    Check, Warning
} from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import MetricHistory from './metricHistory.vue';

const route = useRoute();
const router = useRouter();
const historyVisible = ref(false);
const currentMetric = ref({});
const currentMeta = ref({});

const wsConnected = ref(false);
let ws = null;
let wsStop = false;

const deviceData = ref({});
const lastUpdateTime = ref('--');
const currentViewMode = ref('table');
const metrics_total = computed(() => {
    return metrics.value.length;
});
const metrics_exp = computed(() => {
    return metrics.value.filter(metric => metric.status !== 0).length;
});

const deviceName = ref("")
const deviceId = ref("");
const metrics = ref([]);
const deviceStatus = ref("");

const initWebSocket = () => {
    const wsUrl = import.meta.env.VITE_AM_BASE_WS_URL + '/monitor/devices/' + route.params.id;
    ws = new WebSocket(wsUrl);

    ws.onopen = () => {
        wsConnected.value = true;
        ElMessage.success('Monitor connection established');
    };

    ws.onmessage = (event) => {
        try {
            const data = JSON.parse(event.data);
            // console.log('WebSocket message:', data);
            if (data === null || data === undefined) {
                deviceData.value = {};
                deviceName.value = '';
                deviceId.value = '';
                deviceStatus.value = '设备离线';
                metrics.value = [];
                return;
            }
            deviceData.value = data;
            lastUpdateTime.value = formatTime(new Date());

            currentMeta.value = data.meta;
            deviceName.value = data.meta.name;
            deviceId.value = data.meta.psn;
            metrics.value = data.status.metrics || [];

            deviceStatus.value = '正常';
            for (const metric of metrics.value) {
                if (metric.status !== 0) {
                    deviceStatus.value = '异常';
                    break;
                }
            }


        } catch (error) {
            console.error('处理 WebSocket 消息时出错:', error);
        }
    };

    ws.onclose = () => {
        if (wsStop) {
            return;
        }
        wsConnected.value = false;
        ElMessage.warning('设备监控连接已断开，正在尝试重新连接...');
        setTimeout(initWebSocket, 1000);
    };

    ws.onerror = (error) => {
        wsConnected.value = false;
        ElMessage.error('设备监控连接错误');
        console.error('WebSocket 错误:', error);
    };
};

const handleBack = () => {
    router.push({ path: '/am_devices' });
};

const viewMetricHistory = (metric) => {
    currentMetric.value = metric;
    historyVisible.value = true;
};

const viewMetricDetails = (metric) => {

};

const formatTime = (time) => {
    if (!time) return '--';
    const date = new Date(time);
    return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

onMounted(() => {
    initWebSocket();
});

onBeforeUnmount(() => {
    wsStop = true;
    if (ws) {
        ws.close();
        ws = null;
    }
});
</script>

<style lang="scss" scoped>
.device-detail-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 120px);
    /* 减去页面上下边距 */
    padding: 20px;
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 15px;

    .connection-status {
        display: flex;
        align-items: center;
        gap: 15px;

        .timestamp {
            color: #666;
            font-size: 13px;
        }
    }

    .actions {
        display: flex;
        gap: 10px;
    }
}

.device-info-card {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    /* 防止内容溢出 */

    :deep(.el-card__body) {
        height: 100%;
        padding: 0;
        display: flex;
        flex-direction: column;
    }

    .card-header {
        padding: 15px 20px;

        .device-title {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;

            h2 {
                margin: 0;
                font-weight: 600;
                color: #303133;
            }
        }

        .device-meta {
            display: flex;
            gap: 20px;
            color: #606266;
            font-size: 14px;
        }
    }

    .card-content {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
    }

    .view-options {
        padding: 15px 20px;
        text-align: center;
        border-bottom: 1px solid #ebeef5;
    }

    .view-container {
        flex: 1;
        overflow: hidden;
        padding: 20px;
    }
}

.dashboard-view {
    .el-scrollbar {
        height: 90%;
    }

    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 20px;
        padding: 10px;
    }

    .metric-tile {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        padding: 20px;
        position: relative;
        display: flex;
        flex-direction: column;
        height: 200px;
        overflow: hidden;

        &:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        &.metric-alert {
            background-color: rgba(245, 108, 108, 0.05);
            border-left: 4px solid #F56C6C;
        }

        &:not(.metric-alert) {
            border-left: 4px solid #67C23A;
        }

        .metric-tile-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;

            .metric-name-container {
                .metric-name {
                    margin: 0;
                    font-size: 18px;
                    font-weight: 600;
                    color: #303133;
                    line-height: 1.2;
                }

                .metric-code {
                    display: block;
                    font-size: 12px;
                    color: #909399;
                    margin-top: 5px;
                }
            }

            .status-tag {
                margin-top: 5px;
            }
        }

        .metric-tile-content {
            display: flex;
            justify-content: space-between;
            flex: 1;
            padding: 10px 0;

            .value-section {
                display: flex;
                flex-direction: column;
                justify-content: center;
            }

            .metric-value {
                font-size: 36px;
                font-weight: 700;
                color: #303133;
                line-height: 1.2;
            }

            .metric-timestamp {
                font-size: 12px;
                color: #909399;
                margin-top: 5px;
            }

            .status-indicator {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 60px;
                height: 60px;
                border-radius: 50%;

                &.status-normal {
                    background-color: rgba(103, 194, 58, 0.1);
                    color: #67C23A;
                }

                &.status-alert {
                    background-color: rgba(245, 108, 108, 0.1);
                    color: #F56C6C;
                    animation: pulse 2s infinite;
                }
            }
        }

        .metric-extra-info {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 8px 12px;
            margin-bottom: 15px;
            font-size: 13px;
            color: #606266;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            .metric-alert & {
                background-color: rgba(245, 108, 108, 0.1);
                color: #F56C6C;
            }
        }

        .metric-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: auto;
        }
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.4);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(245, 108, 108, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(245, 108, 108, 0);
    }
}

.table-view {
    display: flex;
    flex-direction: column;
    height: 90%;
}

.metrics-summary {
    margin-top: 15px;
    display: flex;
    gap: 20px;
    font-size: 14px;

    .metrics-total strong {
        color: #409EFF;
    }

    .metrics-normal strong {
        color: #67C23A;
    }

    .metrics-error strong {
        color: #F56C6C;
    }
}
</style>