@import './variables.module.scss';
@import './layout.scss';
@import './element-plus.scss';


body {
  height: 100vh;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  font-size: 14px;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.full-height {
  height: 100%;
}

.no-padding {
  padding: 0 !important;
}

.tool-bar-container {
  padding-bottom: 5px;
}

.pagination-container {
  display: flex;
  justify-content: flex-start;
  padding: 10px 0;
}

.el-select-dropdown .el-select-dropdown__footer {
  padding: 0;
}

.custom-html a {
  color: #3370ff;
  background-color: #f1f1f1;
  padding: 4px;
}

/* nprogress-custom.css */
#nprogress .bar {
  background: #409EFF;
  ;
  /* 进度条颜色 */
}

#nprogress .peg {
  box-shadow: 0 0 10px #409EFF, 0 0 5px #409EFF;
  ;
  /* 进度条阴影颜色 */
}

#nprogress .spinner-icon {
  border-top-color: #409EFF;
  /* Spinner 顶部颜色 */
  border-left-color: #409EFF;
  /* Spinner 左侧颜色 */
}

:root {
  --fc-button-bg-color: #409eff;
  --fc-button-border-color: #409eff;
  --fc-button-hover-bg-color: #79bbff;
  --fc-button-hover-border-color: #79bbff;
  --fc-button-active-bg-color: #337ecc;
  --fc-button-active-border-color: #337ecc;
}

.el-form-item .el-form-item__label-wrap .el-form-item__label {
  font-weight: 700;
}


.el-menu-vertical:not(.el-menu--collapse) {
  width: 180px;
}

.el-menu {
  border: none;
}

.el-menu-item {
  margin: 0 10px;
  border-radius: 5px;
}

.el-sub-menu__title {
  margin: 0 10px;
}

.el-menu-item .el-icon {
  color: #c6c6c6;
}

.el-menu-item.is-active {
  background-color: #ecf5ff;
}

.el-menu-item:hover:not(.is-active) {
  background-color: #ffffff;
}

.el-sub-menu .el-icon {
  color: #c6c6c6;
}

.el-sub-menu.is-active .el-sub-menu__title,
.el-sub-menu.is-active .el-icon {
  color: #409eff;
}

.el-sub-menu .el-sub-menu__title:hover {
  background-color: #ffffff;
}

.el-sub-menu:hover:not(.is-active) {
  background-color: #ffffff;
}

.el-menu--collapse .el-tooltip__trigger {
  display: flex;
  justify-content: center;
}

.el-menu-vertical {
  transition: width 0.3s ease;
}

.el-menu-item {
  transition: all 0.3s ease;
}

.el-menu-item .el-icon,
.el-sub-menu .el-icon {
  transition: transform 0.3s ease;
}

.el-menu--collapse .el-menu-item .el-icon,
.el-menu--collapse .el-sub-menu .el-icon {
  transform: scale(1.2);
}

@keyframes icon-shrink-grow {
  0% {
    transform: scale(1.2);
  }

  50% {
    transform: scale(0.5);
  }

  100% {
    transform: scale(1);
  }
}

.el-menu-vertical:not(.el-menu--collapse) .el-menu-item .el-icon,
.el-menu-vertical:not(.el-menu--collapse) .el-sub-menu__title .el-icon {
  animation: icon-shrink-grow 0.3s ease;
}