// 芯片配置数据
export const PinValue = {
  "Config_Onboard": {
    "Chip_function": [
      {
        "GPIO": {
          "code_define": {
            "define_startPostion": "/*GPIO define start*/",
            "define_endPostion": "/*GPIO define end*/",
            "define_rule": {
              "pin_id_name": "MCU_(name)_PIN",
              "output": [
                "#define MCU_(name)_PIN\t           (pin_id) //(des)",
                "#define MCU_(name)_ON()            Hal_Gpio_SetMode(MCU_(name)_PIN, GPIO_OUPUT_MODE, High)",
                "#define MCU_(name)_OFF()           Hal_Gpio_SetMode(MCU_(name)_PIN, GPIO_OUPUT_MODE, Low)"
              ],
              "reversal": [
                "#define MCU_(name)_NOT()\t         Hal_Gpio_ToggleOutput(MCU_(name)_PIN)"
              ],
              "reversal_rule": [
                "MCU_(name)_NOT()"
              ],
              "input": [
                "#define MCU_(name)_PIN\t           (pin_id)//(des)",
                "#define MCU_(name)_INPUT()            Hal_Gpio_SetMode(MCU_(name)_PIN, GPIO_INPUT_MODE, High)",
                "#define MCU_(name)_Read()             Hal_Gpio_GetLevel(MCU_(name)_PIN)"
              ]
            },
            "functions": {
              "name": "IO_DefaultInit",
              "start": [
                "GPIO_InitTypeDef GPIO_InitStruct;"
              ],
              "GPIO_OType": [
                "GPIO_MODE_OUTPUT_OD",
                "GPIO_MODE_OUTPUT_PP"
              ],
              "GPIO_PuPd": [
                "HAL_GPIO_PULL_UP",
                "HAL_GPIO_PULL_DOWN",
                "HAL_GPIO_PULL_NO_PULL"
              ],
              "GPIO_OUTPUT_LEVEL": [
                "LOW",
                "HIGH"
              ],
              "rule": {
                "output": [
                  "//使能(name)引脚",
                  "GPIO_InitStruct.GPIO_Mode  = GPIO_MODE_OUTPUT;",
                  "GPIO_InitStruct.GPIO_OType = (GPIO_OType);",
                  "GPIO_InitStruct.GPIO_OUTPUT_LEVEL = (GPIO_OUTPUT_LEVEL);",
                  "GPIO_InitStruct.GPIO_Pin   = MCU_(name)_PIN;",
                  "Hal_Gpio_Init(&GPIO_InitStruct);"
                ],
                "input": [
                  "//(name)检测",
                  "GPIO_InitStruct.GPIO_Mode  = GPIO_MODE_INPUT;",
                  "GPIO_InitStruct.GPIO_PuPd  = (GPIO_PuPd);",
                  "GPIO_InitStruct.GPIO_Pin   = MCU_(name)_PIN;",
                  "Hal_Gpio_Init(&GPIO_InitStruct);"
                ]
              }
            }
          },
          "attribute_define": {
            "name": {
              "display": "通道名称",
              "type": "string"
            },
            "PinInfo": [
              {
                "IO": {
                  "pin_id": {
                    "display": "引脚号",
                    "type": "enum",
                    "list": "GPIO"
                  }
                }
              }
            ],
            "des": {
              "display": "引脚注释",
              "type": "string"
            },
            "config": {
              "work_mode": {
                "display": "工作模式",
                "type": "enum",
                "list": [
                  "输入",
                  "输出",
                  "复用",
                  "模拟"
                ]
              },
              "speed": {
                "display": "速度",
                "type": "enum",
                "list": [
                  "高速",
                  "中速",
                  "低速"
                ]
              },
              "output_type": {
                "display": "输出类型",
                "type": "enum",
                "list": [
                  "开漏",
                  "推挽"
                ]
              },
              "output_level": {
                "display": "默认电平",
                "type": "enum",
                "list": [
                  "低",
                  "高"
                ]
              },
              "up_down_pullType": {
                "display": "上下拉选择",
                "type": "enum",
                "list": [
                  "上拉",
                  "下拉",
                  "无上下拉"
                ]
              },
              "reversal": {
                "display": "是否翻转",
                "type": "enum",
                "list": [
                  "是",
                  "否"
                ]
              }
            }
          },
          "attribute_config": [
            {
              "name": "HEART",
              "pin_id": 1,
              "des": "心跳引脚功能",
              "config": {
                "work_mode": "输出",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "HEART",
              "pin_id": 6,
              "des": "MCU心跳灯",
              "config": {
                "work_mode": "输出",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "是"
              },
              "enable_config": true
            },
            {
              "name": "BL_FAULT",
              "pin_id": 14,
              "des": "背光错误引脚",
              "config": {
                "work_mode": "输入",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "HOLD",
              "pin_id": 22,
              "des": "MCU保持引脚",
              "config": {
                "work_mode": "输出",
                "speed": "中速",
                "output_level": "高",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "ENB_IN",
              "pin_id": 23,
              "des": "外部使能检测引脚",
              "config": {
                "work_mode": "输入",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "上拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "RESET_TP",
              "pin_id": 24,
              "des": "触摸芯片RESET引脚",
              "config": {
                "work_mode": "输出",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "ERR_TP",
              "pin_id": 25,
              "des": "触摸芯片报错引脚",
              "config": {
                "work_mode": "输入",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "ATREN_LCD",
              "pin_id": 29,
              "des": "屏端每60帧启用自动重新加载OTP（板端上拉启动功能）",
              "config": {
                "work_mode": "输出",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "FAILT_LCD",
              "pin_id": 30,
              "des": "屏端错误引脚",
              "config": {
                "work_mode": "输入",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "RESET_LCD",
              "pin_id": 31,
              "des": "屏端RESET引脚",
              "config": {
                "work_mode": "输出",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "STBYB_LCD",
              "pin_id": 32,
              "des": "屏端休眠引脚",
              "config": {
                "work_mode": "输出",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "BL_EN",
              "pin_id": 33,
              "des": "背光使能引脚",
              "config": {
                "work_mode": "输出",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "INT_DSL",
              "pin_id": 61,
              "des": "MCU中断引脚",
              "config": {
                "work_mode": "输出",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": true
            },
            {
              "name": "948_BISTEN",
              "pin_id": 35,
              "des": "解串器BIST模式控制引脚",
              "config": {
                "work_mode": "输出",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "948_LOCK",
              "pin_id": 37,
              "des": "解串器LOCK引脚",
              "config": {
                "work_mode": "输入",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "948_PDB",
              "pin_id": 38,
              "des": "解串器休眠引脚",
              "config": {
                "work_mode": "输出",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "TP_3V3_EN",
              "pin_id": 39,
              "des": "触摸芯片电源使能",
              "config": {
                "work_mode": "输出",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "LCD_3V3_EN",
              "pin_id": 44,
              "des": "屏端3.3V使能",
              "config": {
                "work_mode": "输出",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "LDO_DSL1V2_EN",
              "pin_id": 45,
              "des": "1.2V电源芯片使能",
              "config": {
                "work_mode": "输出",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            },
            {
              "name": "DCDC_EN",
              "pin_id": 46,
              "des": "DCDC电源芯片使能",
              "config": {
                "work_mode": "输出",
                "speed": "中速",
                "output_level": "低",
                "output_type": "推挽",
                "up_down_pullType": "无上下拉",
                "reversal": "否"
              },
              "enable_config": false
            }
          ]
        }
      },
      {
        "ADC": {
          "code_define": {
            "define_startPostion": "/*ADC define start*/",
            "define_endPostion": "/*ADC define end*/",
            "define_rule": {
              "general": [
                "#define ADC_(channel)_PIN             (pin_id)      // (des)",
                "#define ADC_(channel)_PIN_ALT         (alt_values)       // (des)复用值"
              ]
            },
            "enums_rule": {
              "name": "Enum_ADC_Index",
              "rule": [
                "ADC_INDEX_(channel), //(name)"
              ]
            },
            "array_rule": {
              "name": "Adc_ConfigInfo",
              "rule": [
                "{",
                "    {ADC_(channel)_PIN, ADC_(channel)_PIN_ALT},",
                "}"
              ]
            }
          },
          "attribute_define": {
            "name": {
              "display": "通道名称",
              "type": "string"
            },
            "PinInfo": [
              {
                "IO": {
                  "pin_id": {
                    "display": "引脚号",
                    "type": "enum",
                    "list": "ADC"
                  }
                }
              }
            ],
            "des": {
              "display": "引脚注释",
              "type": "string"
            },
            "config": {
              "work_mode": {
                "display": "工作模式",
                "type": "enum",
                "list": [
                  "复用"
                ]
              },
              "alt_values": {
                "display": "复用值",
                "type": "label"
              },
              "channel": {
                "display": "通道号",
                "type": "label"
              },
              "sampling_period": {
                "display": "采样周期[ms]",
                "type": "uint32"
              }
            }
          },
          "attribute_config": [
            {
              "name": "Temperature",
              "pin_id": 1,
              "des": "温度采集",
              "config": {
                "work_mode": "复用",
                "alt_values": 1,
                "channel": 0,
                "sampling_period": 10
              },
              "enable_config": false
            },
            {
              "name": "PCBA_ADC",
              "pin_id": 13,
              "des": "PCBA版本识别引脚",
              "config": {
                "work_mode": "复用",
                "alt_values": "14",
                "channel": 0,
                "sampling_period": 10
              },
              "enable_config": false
            },
            {
              "name": "LCDNTC_ADC_MCU",
              "pin_id": 53,
              "des": "屏端NTC电阻电压检测",
              "config": {
                "work_mode": "复用",
                "alt_values": "14",
                "channel": 1,
                "sampling_period": 10
              },
              "enable_config": true
            },
            {
              "name": "AT_ADC",
              "pin_id": 54,
              "des": "PCBA板端NTC电阻电压检测",
              "config": {
                "work_mode": "复用",
                "alt_values": "14",
                "channel": 2,
                "sampling_period": 10
              },
              "enable_config": false
            },
            {
              "name": "BATT_ADC_MCU",
              "pin_id": 55,
              "des": "输入电压检测",
              "config": {
                "work_mode": "复用",
                "alt_values": "14",
                "channel": 3,
                "sampling_period": 10
              },
              "enable_config": true
            }
          ]
        }
      },
      {
        "PWM": {
          "code_define": {
            "define_startPostion": "/*PWM define start*/",
            "define_endPostion": "/*PWM define end*/",
            "define_rule": {
              "general": [
                "#define PWM_(channel)_PIN             (pin_id)      // (des)",
                "#define PWM_(channel)_PIN_ALT         (alt_values)       // (des)复用值",
                "#define PWM_(channel)_CLKDIV          (clock_division)       // (des)时钟分频",
                "#define PWM_(channel)_FREQ            (output_frequency)    // (des)频率",
                "#define PWM_(channel)_DUTY            (duty_cycle)      // (des)占空比"
              ]
            },
            "enums_rule": {
              "name": "Enum_PWM_Index",
              "rule": [
                "PWM_INDEX_(channel), //(name)"
              ]
            },
            "array_rule": {
              "name": "Pwm_ConfigInfo",
              "rule": [
                "{",
                "    {PWM_(channel)_PIN, PWM_(channel)_PIN_ALT},",
                "    PWM_(channel)_CLKDIV,",
                "    PWM_(channel)_FREQ,",
                "    PWM_(channel)_DUTY,",
                "    1,",
                "}"
              ]
            }
          },
          "attribute_define": {
            "name": {
              "display": "通道名称",
              "type": "string"
            },
            "PinInfo": [
              {
                "IO": {
                  "pin_id": {
                    "display": "引脚号",
                    "type": "enum",
                    "list": "PWM"
                  }
                }
              }
            ],
            "des": {
              "display": "引脚注释",
              "type": "string"
            },
            "config": {
              "work_mode": {
                "display": "工作模式",
                "type": "enum",
                "list": [
                  "复用"
                ]
              },
              "alt_values": {
                "display": "复用值",
                "type": "label"
              },
              "channel": {
                "display": "通道号",
                "type": "uint32,auto_add"
              },
              "clock_division": {
                "display": "时钟分频",
                "type": "enum",
                "list": [
                  1,
                  2,
                  3,
                  4,
                  5,
                  6,
                  7,
                  8,
                  9
                ]
              },
              "output_frequency": {
                "display": "输出频率",
                "type": "uint32"
              },
              "duty_cycle": {
                "display": "占空比[%]",
                "type": "uint8",
                "min": 0,
                "max": 100
              }
            }
          },
          "attribute_config": [
            {
              "name": "brightness",
              "pin_id": 1,
              "des": "亮度控制",
              "config": {
                "work_mode": "复用",
                "alt_values": 1,
                "channel": 1,
                "clock_division": 5,
                "output_frequency": 100,
                "duty_cycle": 50
              },
              "enable_config": false
            },
            {
              "name": "BL_PWM",
              "pin_id": 5,
              "des": "PWM亮度控制",
              "config": {
                "work_mode": "复用",
                "alt_values": "2",
                "channel": 0,
                "clock_division": 5,
                "output_frequency": 20000,
                "duty_cycle": 0
              },
              "enable_config": true
            }
          ]
        }
      },
      {
        "IIC": {
          "code_define": {
            "define_startPostion": "/*IIC define start*/",
            "define_endPostion": "/*IIC define end*/",
            "define_rule": {
              "general": [
                "#define IIC_(channel)_PIN_SCL         (pin_id_SCL)       // (des)时钟线引脚号",
                "#define IIC_(channel)_PIN_SDA         (pin_id_SDA)      // (des)数据线引脚号",
                "#define IIC_(channel)_PIN_SCL_ALT     (alt_values_SCL)       // (des)时钟线引脚复用值",
                "#define IIC_(channel)_PIN_SDA_ALT     (alt_values_SDA)       // (des)数据线引脚复用值",
                "#define IIC_(channel)_MODE            (iic_mode)       // (des)模式",
                "#define IIC_(channel)_TYPE            (iic_type)       // (des)类型",
                "#define IIC_(channel)_ADDR            (device_address)    // (des)地址"
              ]
            },
            "iic_mode": [
              "EnumI2CModeMaster",
              "EnumI2CModeSlaver"
            ],
            "iic_type": [
              "EnumI2CTypeHard",
              "EnumI2CTypeSoft"
            ],
            "enums_rule": {
              "name": "Enum_IIC_Index",
              "rule": [
                "IIC_INDEX_(channel), //(name)"
              ]
            },
            "array_rule": {
              "name": "I2C_ConfigInfo",
              "rule": [
                "{",
                "        {IIC_(channel)_PIN_SCL, IIC_(channel)_PIN_SCL_ALT},",
                "        {IIC_(channel)_PIN_SDA, IIC_(channel)_PIN_SDA_ALT},",
                "        IIC_(channel)_MODE,",
                "        IIC_(channel)_TYPE,",
                "        2,",
                "        IIC_(channel)_ADDR,",
                "        (sub_addr_byte),",
                "        (sub_addr_type),",
                "}"
              ]
            }
          },
          "attribute_define": {
            "name": {
              "display": "通道名称",
              "type": "string"
            },
            "PinInfo": [
              {
                "SCL": {
                  "pin_id": {
                    "display": "SCL",
                    "type": "enum",
                    "list": "SCL"
                  },
                  "work_mode": {
                    "display": "工作模式",
                    "type": "enum",
                    "list": [
                      "复用"
                    ]
                  },
                  "alt_values": {
                    "display": "复用值",
                    "type": "label"
                  }
                }
              },
              {
                "SDA": {
                  "pin_id": {
                    "display": "SDA",
                    "type": "enum",
                    "list": "SDA"
                  },
                  "work_mode": {
                    "display": "工作模式",
                    "type": "enum",
                    "list": [
                      "复用"
                    ]
                  },
                  "alt_values": {
                    "display": "复用值",
                    "type": "label"
                  }
                }
              }
            ],
            "des": {
              "display": "引脚注释",
              "type": "string"
            },
            "iic_type": {
              "display": "IIC类型",
              "type": "enum",
              "list": [
                "硬件IIC",
                "软件IIC"
              ]
            },
            "config": {
              "channel": {
                "display": "通道号",
                "type": "uint32,auto_add"
              },
              "communication_speed": {
                "display": "通讯速率[khz]",
                "type": "uint32",
                "default": 400
              },
              "device_address": {
                "display": "设备地址(8bit)",
                "type": "uint8_hex",
                "min": 1,
                "max": 255
              },
              "work_mode": {
                "display": "工作模式",
                "type": "enum",
                "list": [
                  "主",
                  "从"
                ]
              },
              "sub_addr_type": {
                "display": "设置子地址类型",
                "type": "enum",
                "list": [
                  "I2CSubAddrTypeNoBye",
                  "I2CSubAddrTypeSingleBye",
                  "I2CSubAddrTypeDoubleByte",
                  "I2CSubAddrTypeThreeByte",
                  "I2CSubAddrTypeFourByte"
                ]
              },
              "sub_addr_byte": {
                "device_address": null,
                "display": "设备子地址字节数",
                "type": "uint8",
                "min": 0,
                "max": 4
              }
            }
          },
          "attribute_config": [
            {
              "name": "touch",
              "PinInfo": [
                [
                  1,
                  "复用",
                  2
                ],
                [
                  2,
                  "复用",
                  3
                ]
              ],
              "des": "触摸",
              "config": {
                "channel": 0,
                "communication_speed": 400,
                "device_address": 2,
                "work_mode": "主",
                "sub_addr_type": "I2CSubAddrTypeSingleBye",
                "sub_addr_byte": 0
              },
              "iic_type": "硬件IIC",
              "enable_config": false
            },
            {
              "name": "TP",
              "PinInfo": [
                [
                  42,
                  "GPIO",
                  "5"
                ],
                [
                  43,
                  "GPIO",
                  "5"
                ]
              ],
              "des": "触摸芯片I2C",
              "config": {
                "channel": 0,
                "communication_speed": 400,
                "device_address": "0x2",
                "work_mode": "主",
                "sub_addr_type": "I2CSubAddrTypeSingleBye",
                "sub_addr_byte": 0
              },
              "iic_type": "软件IIC",
              "enable_config": true
            },
            {
              "name": "MCU",
              "PinInfo": [
                [
                  59,
                  "复用",
                  "5"
                ],
                [
                  60,
                  "复用",
                  "5"
                ]
              ],
              "des": "触摸",
              "config": {
                "channel": 1,
                "communication_speed": 400,
                "device_address": "0x1e",
                "work_mode": "从",
                "sub_addr_type": "I2CSubAddrTypeSingleBye",
                "sub_addr_byte": 0
              },
              "iic_type": "硬件IIC",
              "enable_config": true
            },
            {
              "name": "DEBUG",
              "PinInfo": [
                [
                  50,
                  "复用",
                  "5"
                ],
                [
                  49,
                  "复用",
                  "5"
                ]
              ],
              "des": "I2C调试接口（硬件I2C接口）",
              "config": {
                "channel": 2,
                "communication_speed": 400,
                "device_address": 2,
                "work_mode": "主",
                "sub_addr_type": "I2CSubAddrTypeSingleBye",
                "sub_addr_byte": 0
              },
              "iic_type": "硬件IIC",
              "enable_config": false
            }
          ]
        }
      },
      {
        "SPI": {
          "code_define": {
            "define_startPostion": "/*SPI define start*/",
            "define_endPostion": "/*SPI define end*/",
            "define_rule": {
              "general": [
                "#define SPI_(channel)_PIN_CS          (pin_id_CS)      // (des)片选引脚号",
                "#define SPI_(channel)_PIN_SCK         (pin_id_SCK)       // (des)时钟引脚号",
                "#define SPI_(channel)_PIN_MISO        (pin_id_MISO)       // (des)主输入从输出引脚号",
                "#define SPI_(channel)_PIN_MOSI        (pin_id_MOSI)       // (des)主输出从输入引脚号",
                "#define SPI_(channel)_PIN_CS_ALT      (alt_values_CS)       // (des)片选引脚复用值",
                "#define SPI_(channel)_PIN_SCK_ALT     (alt_values_SCK)       // (des)时钟引脚复用值",
                "#define SPI_(channel)_PIN_MISO_ALT    (alt_values_MISO)       // (des)主输入从输出引脚复用值",
                "#define SPI_(channel)_PIN_MOSI_ALT    (alt_values_MOSI)       // (des)主输出从输入引脚复用值",
                "#define SPI_(channel)_SPI_TYPE        (spi_type)       // (des)类型",
                "#define SPI_(channel)_SPI_MODE        (spi_work_mode)       // (des)模式",
                "#define SPI_(channel)_SPI_SPEED       (communication_speed)000 // (des)速度",
                "#define SPI_(channel)_SPI_BIT_WIDTH   (transfor_bits)       // (des)位宽"
              ]
            },
            "spi_work_mode": [
              "EnumSPIModeMaster",
              "EnumSPIModeSlaver"
            ],
            "spi_type": [
              "EnumHardSpiType",
              "EnumNormalSoftSpiType",
              "EnumThreeSoftSpiType"
            ],
            "enums_rule": {
              "name": "Enum_SPI_Index",
              "rule": [
                "SPI_INDEX_(channel), //(name)"
              ]
            },
            "array_rule": {
              "name": "SPI_ConfigInfo",
              "rule": [
                "{",
                "    {SPI_(channel)_PIN_CS, SPI_(channel)_PIN_CS_ALT},",
                "    {SPI_(channel)_PIN_SCK, SPI_(channel)_PIN_SCK_ALT},",
                "    {SPI_(channel)_PIN_MISO, SPI_(channel)_PIN_MISO_ALT},",
                "    {SPI_(channel)_PIN_MOSI, SPI_(channel)_PIN_MOSI_ALT},",
                "    SPI_(channel)_SPI_TYPE,",
                "    SPI_(channel)_SPI_MODE,",
                "    2,",
                "    SPI_(channel)_SPI_SPEED,",
                "    SPI_(channel)_SPI_BIT_WIDTH,",
                "}"
              ]
            }
          },
          "attribute_define": {
            "name": {
              "display": "通道名称",
              "type": "string"
            },
            "PinInfo": [
              {
                "CS": {
                  "pin_id": {
                    "display": "CS",
                    "type": "enum",
                    "list": "CS"
                  },
                  "work_mode": {
                    "display": "工作模式",
                    "type": "enum",
                    "list": [
                      "复用"
                    ]
                  },
                  "alt_values": {
                    "display": "复用值",
                    "type": "label"
                  }
                }
              },
              {
                "CLK": {
                  "pin_id": {
                    "display": "CLK",
                    "type": "enum",
                    "list": "CLK"
                  },
                  "work_mode": {
                    "display": "工作模式",
                    "type": "enum",
                    "list": [
                      "复用"
                    ]
                  },
                  "alt_values": {
                    "display": "复用值",
                    "type": "label"
                  }
                }
              },
              {
                "MOSI": {
                  "pin_id": {
                    "display": "MOSI",
                    "type": "enum",
                    "list": "MOSI"
                  },
                  "work_mode": {
                    "display": "工作模式",
                    "type": "enum",
                    "list": [
                      "复用"
                    ]
                  },
                  "alt_values": {
                    "display": "复用值",
                    "type": "label"
                  }
                }
              },
              {
                "MISO": {
                  "pin_id": {
                    "display": "MISO",
                    "type": "enum",
                    "list": "MISO"
                  },
                  "work_mode": {
                    "display": "工作模式",
                    "type": "enum",
                    "list": [
                      "复用"
                    ]
                  },
                  "alt_values": {
                    "display": "复用值",
                    "type": "label"
                  }
                }
              }
            ],
            "des": {
              "display": "引脚注释",
              "type": "string"
            },
            "spi_type": {
              "display": "SPI类型",
              "type": "enum",
              "list": [
                "硬件SPI",
                "软件SPI"
              ]
            },
            "soft_spi_type": {
              "display": "软件SPI类型",
              "type": "enum",
              "list": [
                "三线SPI",
                "四线SPI"
              ]
            },
            "config": {
              "channel": {
                "display": "通道号",
                "type": "uint32,auto_add"
              },
              "communication_speed": {
                "display": "通讯速率[khz]",
                "type": "uint32",
                "default": 500
              },
              "transfor_bits": {
                "display": "传输位数[bits]",
                "type": "enum",
                "list": [
                  8,
                  16,
                  17
                ]
              },
              "work_mode": {
                "display": "工作模式",
                "type": "enum",
                "list": [
                  "主",
                  "从"
                ]
              }
            }
          },
          "attribute_config": [
            {
              "name": "OSD",
              "PinInfo": [
                [
                  1,
                  "复用",
                  3
                ],
                [
                  2,
                  "复用",
                  3
                ],
                [
                  3,
                  "复用",
                  3
                ],
                [
                  4,
                  "复用",
                  3
                ]
              ],
              "des": "这是OSD",
              "config": {
                "channel": 0,
                "communication_speed": 500,
                "transfor_bits": 8,
                "work_mode": "主"
              },
              "spi_type": "硬件SPI",
              "soft_spi_type": "四线SPI",
              "enable_config": false
            },
            {
              "name": "LCD",
              "PinInfo": [
                [
                  18,
                  "GPIO",
                  "15"
                ],
                [
                  15,
                  "GPIO",
                  "15"
                ],
                [
                  19,
                  "GPIO",
                  "15"
                ],
                [
                  1,
                  "GPIO",
                  "15"
                ]
              ],
              "des": "屏端SPI",
              "config": {
                "channel": 0,
                "communication_speed": 500,
                "transfor_bits": 8,
                "work_mode": "主"
              },
              "spi_type": "软件SPI",
              "soft_spi_type": "三线SPI",
              "enable_config": true
            }
          ]
        }
      },
      {
        "UART": {
          "code_define": {
            "define_startPostion": "/*UART define start*/",
            "define_endPostion": "/*UART define end*/",
            "define_rule": {
              "general": [
                "#define UART_(channel)_PIN_TX         (pin_id_TX)      // (des)发送引脚号",
                "#define UART_(channel)_PIN_RX         (pin_id_RX)      // (des)接收引脚号",
                "#define UART_(channel)_PIN_TX_ALT     (alt_values_TX)       // (des)发送引脚复用值",
                "#define UART_(channel)_PIN_RX_ALT     (alt_values_RX)       // (des)接收引脚复用值",
                "#define UART_(channel)_BAUDRATE       (communication_speed)  // (des)波特率"
              ]
            },
            "enums_rule": {
              "name": "Enum_Uart_Index",
              "rule": [
                "UART_INDEX_(channel), //(name)"
              ]
            },
            "array_rule": {
              "name": "Uart_ConfigInfo",
              "rule": [
                "{",
                "    UART_INDEX_(channel),",
                "    {UART_(channel)_PIN_RX, UART_(channel)_PIN_RX_ALT},",
                "    {UART_(channel)_PIN_TX, UART_(channel)_PIN_TX_ALT},",
                "    UART_(channel)_BAUDRATE,",
                "}"
              ]
            }
          },
          "attribute_define": {
            "name": {
              "display": "通道名称",
              "type": "string"
            },
            "PinInfo": [
              {
                "RX": {
                  "pin_id": {
                    "display": "RX",
                    "type": "enum",
                    "list": "RX"
                  },
                  "work_mode": {
                    "display": "工作模式",
                    "type": "enum",
                    "list": [
                      "复用"
                    ]
                  },
                  "alt_values": {
                    "display": "复用值",
                    "type": "label"
                  }
                }
              },
              {
                "TX": {
                  "pin_id": {
                    "display": "TX",
                    "type": "enum",
                    "list": "TX"
                  },
                  "work_mode": {
                    "display": "工作模式",
                    "type": "enum",
                    "list": [
                      "复用"
                    ]
                  },
                  "alt_values": {
                    "display": "复用值",
                    "type": "label"
                  }
                }
              }
            ],
            "des": {
              "display": "引脚注释",
              "type": "string"
            },
            "config": {
              "channel": {
                "display": "通道号",
                "type": "uint32,auto_add"
              },
              "communication_speed": {
                "display": "通讯速率",
                "type": "enum",
                "list": [
                  9600,
                  57600,
                  115200,
                  921600
                ]
              },
              "interupt_flag": {
                "display": "中断使用",
                "type": "enum",
                "list": [
                  "是",
                  "否"
                ]
              }
            }
          },
          "attribute_config": [
            {
              "name": "Debug",
              "PinInfo": [
                [
                  1,
                  "复用",
                  2
                ],
                [
                  2,
                  "复用",
                  3
                ]
              ],
              "des": "调试",
              "config": {
                "channel": 0,
                "communication_speed": 9600,
                "interupt_flag": "是"
              },
              "enable_config": false
            },
            {
              "name": "Debug_1",
              "PinInfo": [
                [
                  48,
                  "复用",
                  "3"
                ],
                [
                  47,
                  "复用",
                  "3"
                ]
              ],
              "des": "调试",
              "config": {
                "channel": 0,
                "communication_speed": 115200,
                "interupt_flag": "否"
              },
              "enable_config": true
            }
          ]
        }
      },
      {
        "EXIT": {
          "code_define": {
            "define_startPostion": "/*EXIT define start*/",
            "define_endPostion": "/*EXIT define end*/",
            "define_rule": {
              "general": [
                "#define EXTI_(channel)_PIN             (pin_id)      // (des)",
                "#define EXTI_(channel)_PIN_ALT         (alt_values)       // (des)复用值"
              ]
            },
            "enums_rule": {
              "name": "Enum_EXTI_Index",
              "rule": [
                "EXTI_INDEX_(channel), //(name)"
              ]
            },
            "array_rule": {
              "name": "Gpioeint_ConfigInfo",
              "rule": [
                "{",
                "    {EXTI_(channel)_PIN, EXTI_(channel)_PIN_ALT},",
                "    {",
                "        INTERRUPT_TRIGGER_RISING,",
                "        {INTERRUPT_PRIORITY_(preemptPriority),INTERRUPT_PRIORITY_(subPriority)},",
                "        NULL",
                "    },",
                "}"
              ]
            }
          },
          "attribute_define": {
            "name": {
              "display": "通道名称",
              "type": "string"
            },
            "PinInfo": [
              {
                "IO": {
                  "pin_id": {
                    "display": "引脚号",
                    "type": "enum",
                    "list": "EXIT"
                  }
                }
              }
            ],
            "des": {
              "display": "引脚注释",
              "type": "string"
            },
            "config": {
              "alt_values": {
                "display": "复用值",
                "type": "label"
              },
              "work_mode": {
                "display": "工作模式",
                "type": "enum",
                "list": [
                  "输入"
                ]
              },
              "speed": {
                "display": "速度",
                "type": "enum",
                "list": [
                  "高速",
                  "中速",
                  "低速"
                ]
              },
              "up_down_pullType": {
                "display": "上下拉选择",
                "type": "enum",
                "list": [
                  "上拉",
                  "下拉",
                  "无上下拉"
                ]
              },
              "channel": {
                "display": "通道号",
                "type": "enum",
                "list": [
                  0,
                  1
                ]
              },
              "preemptPriority": {
                "display": "抢占优先级",
                "type": "uint8",
                "min": 0,
                "max": "exit"
              },
              "subPriority": {
                "display": "响应优先级",
                "type": "uint8",
                "min": 0,
                "max": "exit"
              }
            }
          },
          "attribute_config_max": 2,
          "attribute_config": [
            {
              "name": "TOUCH_EXIT",
              "pin_id": 1,
              "des": "触摸退出",
              "config": {
                "alt_values": 1,
                "work_mode": "输入",
                "speed": "中速",
                "up_down_pullType": "无上下拉",
                "channel": 0,
                "preemptPriority": 0,
                "subPriority": 15
              },
              "enable_config": false
            },
            {
              "name": "INT_TP",
              "pin_id": 16,
              "des": "触摸芯片中断引脚",
              "config": {
                "alt_values": "16",
                "work_mode": "输入",
                "speed": "中速",
                "up_down_pullType": "上拉",
                "channel": 0,
                "preemptPriority": 0,
                "subPriority": 15
              },
              "enable_config": true
            }
          ]
        }
      }
    ]
  }
}