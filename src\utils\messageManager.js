import { ElMessage } from 'element-plus';

/**
 * ElMessage管理器 - 确保界面上只显示一条消息
 * 如果存在历史消息，则先清除历史消息再显示新消息
 */
class MessageManager {
  constructor() {
    this.currentMessage = null;
  }

  /**
   * 清除当前显示的消息
   */
  clear() {
    if (this.currentMessage) {
      this.currentMessage.close();
      this.currentMessage = null;
    }
  }

  /**
   * 显示成功消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长，默认3000ms
   * @returns {Object} 消息实例
   */
  success(message, duration = 3000) {
    this.clear();
    this.currentMessage = ElMessage.success({
      message,
      duration,
      onClose: () => {
        this.currentMessage = null;
      }
    });
    return this.currentMessage;
  }

  /**
   * 显示错误消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长，默认3000ms
   * @returns {Object} 消息实例
   */
  error(message, duration = 3000) {
    this.clear();
    this.currentMessage = ElMessage.error({
      message,
      duration,
      onClose: () => {
        this.currentMessage = null;
      }
    });
    return this.currentMessage;
  }

  /**
   * 显示警告消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长，默认3000ms
   * @returns {Object} 消息实例
   */
  warning(message, duration = 3000) {
    this.clear();
    this.currentMessage = ElMessage.warning({
      message,
      duration,
      onClose: () => {
        this.currentMessage = null;
      }
    });
    return this.currentMessage;
  }

  /**
   * 显示信息消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长，默认3000ms
   * @returns {Object} 消息实例
   */
  info(message, duration = 3000) {
    this.clear();
    this.currentMessage = ElMessage.info({
      message,
      duration,
      onClose: () => {
        this.currentMessage = null;
      }
    });
    return this.currentMessage;
  }
}

// 创建全局单例实例
const messageManager = new MessageManager();

export default messageManager;
