 <template>
  <!--  <div v-if="gt != undefined && show">
        <div style="display: flex;justify-content: center;width: 100%;">
            <h3>{{ gt }}</h3>
        </div>
        <el-row :gutter="10">
            <el-col :span="9">
                <el-table :data="data" stripe style="height: 500px;">

                    <el-table-column v-for="k in header" :prop="k" :label="k" align="center"></el-table-column>

                </el-table>
            </el-col>
            <el-col :span="15">

                <EchartsComponent :options="options" height="500px" />

            </el-col>
        </el-row>

    </div> -->

    <!-- <div v-if="gt != undefined && show">
        <div style="display: flex;justify-content: center;width: 100%;">
            <h3>{{ gt }}</h3>
        </div>
        <el-row :gutter="10">
            <el-col :span="12">
                <EchartsComponent :options="options" height="500px" />
            </el-col>
            <el-col :span="12">
                <EchartsComponent :options="options" height="500px" />
            </el-col>
   </el-row>  -->

  <!--add 左右两栏表格格式 -->
   <el-row :gutter="10">

        <el-col :span="12">
            <div >
                <div style="display: flex;justify-content: center;width: 100%;">
                        <h3>{{ gt }}</h3>
                </div>
                <EchartsComponent :options="options" height="500px" />   
            </div>
        </el-col>

        <el-col :span="12">
          
            <div style="display: flex;justify-content: center;width: 100%;">
                    <h3>温差曲线</h3>
            </div>
            <EchartsComponent :options="options2" height="500px" />
                      
        </el-col>
     
   </el-row>

   <el-table :data="data" stripe style="height: 500px;">

    <el-table-column v-for="k in header" :prop="k" :label="k" align="center"></el-table-column>

    </el-table>

  

</template>

<script setup>
import { ref, computed } from 'vue';
import EchartsComponent from '@/components/echartsComponent.vue';

const props = defineProps({
    graph_data: {
        type: Object,
        required: true,
    }
})

const show = ref(true);

const gt = computed(() => {
    if(props.graph_data.test_function == "EnvTemperatureTest"){
        return "原始温度曲线"
    }
    return props.graph_data.test_function;
});

const header = computed(() => {
    return props.graph_data.header || props.graph_data.head;
});

const data = computed(() => {
    if (props.graph_data.test_function == undefined) {
        return [];
    }

    let header = props.graph_data.header || props.graph_data.head;
    let content = props.graph_data.content;

    return content.map(item => {
        let obj = {};
        header.forEach((key, index) => {
            obj[key] = item[index];
        });
        return obj;
    })

})

const options = computed(() => {

    if (props.graph_data.test_function == "Gamma曲线测试") {
        let content = props.graph_data.content;
        let data = content.map(item => item[4])
        let indices = data.map((_, index) => index);

        return {
            grid: {
                left: '5%',
                right: '5%',
                top: '5%',
                bottom: '5%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: indices,
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    data,
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        color: 'green'
                    },
                    showSymbol: false,
                    markLine: {
                        symbol: 'none',
                        data: [
                            {
                                yAxis: 2.0,
                                lineStyle: {
                                    color: 'yellow',
                                    type: 'solid'
                                }
                            },
                            {
                                yAxis: 2.4,
                                lineStyle: {
                                    color: 'red',
                                    type: 'solid'
                                }
                            }
                        ]
                    }
                }
            ]
        };
    }
    else if (props.graph_data.test_function == "亮度曲线测试") {
        let content = props.graph_data.content;
        let data = content.map(item => item[3]);
        let indices = content.map(item => item[0]);

        return {
            grid: {
                left: '5%',
                right: '5%',
                top: '5%',
                bottom: '5%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: indices,
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    data,
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        color: 'green'
                    },
                    showSymbol: false,
                }
            ]
        }
    } 
    else if (props.graph_data.test_function == "EnvTemperatureTest") {
        let header = props.graph_data.header || props.graph_data.head;
        let content = props.graph_data.content;
        let indices = content.map(item => new Date(item[0]).getTime());
        //add
        var maxV = 0;
        var maxI;

        content.forEach(item => {
            var ta = item.slice(2).map(Number);
            var temp = Math.max(...ta);
            if (temp > maxV) {
                maxV = temp;
                maxI = ta.indexOf(maxV);
            }
        });
        const series = header.slice(1).map((key, idx) => ({
            name: key,
            type: 'line',
            data: content.map(item => ({
                value: [new Date(item[0]).getTime(), item[idx + 1]]
            })),
            smooth: true,
            showSymbol: false,
            markPoint: {
                data:idx == maxI? [
                    {
                        type: 'max',
                        name: '最大值',
                        itemStyle: {
                        color: 'red' 
                    }

                    },
                   
                ]:[]
            },
        }));

        let allYValues = [];
        series.forEach(serie => {
            serie.data.forEach(point => {
                allYValues.push(point.value[1]);
            });
        });
        const minY = Math.min(...allYValues);
        const maxY = Math.max(...allYValues);

        return {
            legend: {
                data: header.slice(1),
                top: '30' // 调整图例的位置
            },
            grid: {
                left: '5%',
                right: '5%',
                top: '20%',
                bottom: '5%',
                containLabel: true
            },
            xAxis: {
                type: 'time',
                data: indices,
            },
            yAxis: {
                type: 'value',
                min: minY - 1,
                max: maxY + 1,
                axisLabel: {
                    formatter: '{value} ℃' // 在这里设置单位，如 '{value} ℃' 或 '{value} kg'
                }
            },
          
            series
        }
    } 
    else if (props.graph_data.test_function == "TemperatureDifference") {
        let header = props.graph_data.header || props.graph_data.head;
        let content = props.graph_data.content;
        let indices = content.map(item => new Date(item[0]).getTime());

        const series = header.slice(1).map((key, idx) => ({
            name: key,
            type: 'line',
            data: content.map(item => ({
                value: [new Date(item[0]).getTime(), item[idx + 1]]
            })),
            smooth: true,
            showSymbol: false,
        }));

        let allYValues = [];
        series.forEach(serie => {
            serie.data.forEach(point => {
                allYValues.push(point.value[1]);
            });
        });
        const minY = Math.min(...allYValues);
        const maxY = Math.max(...allYValues);

        return {
            legend: {
                data: header.slice(1),
                top: '30' // 调整图例的位置
            },
            grid: {
                left: '5%',
                right: '5%',
                top: '20%',
                bottom: '5%',
                containLabel: true
            },
            xAxis: {
                type: 'time',
                data: indices,
            },
            yAxis: {
                type: 'value',
                min: minY -1 ,
                max: maxY +1
            },
            series
        }
    } 
    else if (props.graph_data.test_function == "LightSensorAutoTest") {
        let header = props.graph_data.header;
        let content = props.graph_data.content;
        let indices = content.map(item => item[0]);

        const series = header.slice(1).map((key, idx) => ({
            name: key,
            type: 'line',
            data: content.map(item => item[idx + 1]),
            smooth: true,
            showSymbol: false,
        }));

        return {
            legend: {
                data: header.slice(1),
                top: '30' // 调整图例的位置
            },
            grid: {
                left: '5%',
                right: '5%',
                top: '20%',
                bottom: '5%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: indices,
            },
            yAxis: {
                type: 'value'
            },
            series
        }
    } else {
        show.value = false;
        return {
            xAxis: {
                data: ['A', 'B', 'C', 'D', 'E']
            },
            yAxis: {},
            series: [
                {
                    data: [10, 22, 28, 23, 19],
                    type: 'line',
                    smooth: true
                }
            ]
        };
    }
});

const options2 = computed(() => {

if (props.graph_data.test_function == "Gamma曲线测试") {
    let content = props.graph_data.content;
    let data = content.map(item => item[4])
    let indices = data.map((_, index) => index);

    return {
        grid: {
            left: '5%',
            right: '5%',
            top: '5%',
            bottom: '5%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: indices,
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                data,
                type: 'line',
                smooth: true,
                lineStyle: {
                    color: 'green'
                },
                showSymbol: false,
                markLine: {
                    symbol: 'none',
                    data: [
                        {
                            yAxis: 2.0,
                            lineStyle: {
                                color: 'yellow',
                                type: 'solid'
                            }
                        },
                        {
                            yAxis: 2.4,
                            lineStyle: {
                                color: 'red',
                                type: 'solid'
                            }
                        }
                    ]
                }
            }
        ]
    };
}
else if (props.graph_data.test_function == "亮度曲线测试") {
    let content = props.graph_data.content;
    let data = content.map(item => item[3]);
    let indices = content.map(item => item[0]);

    return {
        grid: {
            left: '5%',
            right: '5%',
            top: '5%',
            bottom: '5%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: indices,
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                data,
                type: 'line',
                smooth: true,
                lineStyle: {
                    color: 'green'
                },
                showSymbol: false,
            }
        ]
    }
} 
else if (props.graph_data.test_function == "EnvTemperatureTest") {
    let header = props.graph_data.header || props.graph_data.head;
    let content = props.graph_data.content;
    let indices = content.map(item => new Date(item[0]).getTime());

    var maxV = 0;
    var maxI;

    content.forEach(item => {
        var ta = item.slice(2).map(Number);
        var temp = Math.max(...ta);
        if (temp > maxV) {
            maxV = temp;
            maxI = ta.indexOf(maxV);
        }
    });

    const series = header.slice(2, header.length-1).map((key, idx) => ({
        name: key,
        type: 'line',
        data: content.map(item => ({
            value: [new Date(item[0]).getTime(), (Number(item[idx + 2])-Number(item[item.length-1])).toFixed(1) ],
        })),
        
        smooth: true,
        showSymbol: false,
        markPoint: {
            data: idx == maxI ? [
                {
                    type: 'max',
                    name: '最大值',
                    itemStyle: {
                        color: 'red' 
                    }
                    },
                ] : []  
        },
        markLine: {
                        symbol: 'none',
                        data: [
                            {
                                yAxis: 15,
                                lineStyle: {
                                    color: 'red',
                                    type: 'dashed',
                                    width: 2
                                }
                            },
                        ]
        }
        
    }));
    
    let allYValues = [];
    series.forEach(serie => {
        serie.data.forEach(point => {
            allYValues.push(point.value[1]);
        });
    });
    const tempMinY = Math.min(...allYValues);
    const tempMaxY = Math.max(...allYValues);

    const multiplier =  1000 * 1000;
    const offset = Math.round(0.5 * multiplier);
    const min = Math.round(tempMinY * multiplier);
    const minY =  (min - offset) / multiplier;
    const max = Math.round(tempMaxY * multiplier);
    const maxY =  (max + offset) / multiplier;

    return {
        legend: {
            data: header.slice(1),
            top: '30' // 调整图例的位置
        },
        grid: {
            left: '5%',
            right: '5%',
            top: '20%',
            bottom: '5%',
            containLabel: true
        },
        xAxis: {
            type: 'time',
            data: indices,
        },
        yAxis: {
            type: 'value',
            min: minY,
            max: maxY,
            axisLabel: {
                formatter: '{value} ℃' // 在这里设置单位，如 '{value} ℃' 或 '{value} kg'
            }
        },
        series
    }
} 
else if (props.graph_data.test_function == "TemperatureDifference") {
    let header = props.graph_data.header || props.graph_data.head;
    let content = props.graph_data.content;
    let indices = content.map(item => new Date(item[0]).getTime());

    const series = header.slice(1).map((key, idx) => ({
        name: key,
        type: 'line',
        data: content.map(item => ({
            value: [new Date(item[0]).getTime(), item[idx + 1]]
        })),
        smooth: true,
        showSymbol: false,
    }));

    let allYValues = [];
    series.forEach(serie => {
        serie.data.forEach(point => {
            allYValues.push(point.value[1]);
        });
    });
    const minY = Math.min(...allYValues);
    const maxY = Math.max(...allYValues);

    return {
        legend: {
            data: header.slice(1),
            top: '30' // 调整图例的位置
        },
        grid: {
            left: '5%',
            right: '5%',
            top: '20%',
            bottom: '5%',
            containLabel: true
        },
        xAxis: {
            type: 'time',
            data: indices,
        },
        yAxis: {
            type: 'value',
            min: minY,
            max: maxY,
            axisLabel: {
                formatter: '{value} ℃' // 在这里设置单位，如 '{value} ℃' 或 '{value} kg'
            }
        },
        series
    }
} 
else if (props.graph_data.test_function == "LightSensorAutoTest") {
    let header = props.graph_data.header;
    let content = props.graph_data.content;
    let indices = content.map(item => item[0]);

    const series = header.slice(1).map((key, idx) => ({
        name: key,
        type: 'line',
        data: content.map(item => item[idx + 1]),
        smooth: true,
        showSymbol: false,
    }));

    return {
        legend: {
            data: header.slice(1),
            top: '30' // 调整图例的位置
        },
        grid: {
            left: '5%',
            right: '5%',
            top: '20%',
            bottom: '5%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: indices,
        },
        yAxis: {
            type: 'value'
        },
        series
    }
} else {
    show.value = false;
    return {
        xAxis: {
            data: ['A', 'B', 'C', 'D', 'E']
        },
        yAxis: {},
        series: [
            {
                data: [10, 22, 28, 23, 19],
                type: 'line',
                smooth: true
            }
        ]
    };
}
});

</script>

<style lang="scss" scoped></style>