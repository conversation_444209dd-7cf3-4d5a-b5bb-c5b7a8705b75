<template>
    <div class="node-config-drawer">
      <!-- 抽屉标题 -->
      
      <!-- 表单内容 -->
      <div class="drawer-body">
        <!-- 节点唯一标识 -->
        <div class="form-item required">
          <label>节点唯一标识</label>
          <div class="input-wrapper">
            <input 
              type="text" 
              v-model="formData.nodeId" 
              placeholder="节点唯一标识"
            />
            <div class="char-count">{{ formData.nodeId.length }}/30</div>
          </div>
        </div>
        
        <!-- 节点名称 -->
        <div class="form-item">
          <label>节点名称</label>
          <div class="input-wrapper">
            <input 
              type="text" 
              v-model="formData.nodeName" 
              placeholder="节点名称"
            />
            <div class="char-count">{{ formData.nodeName.length }}/36</div>
          </div>
        </div>
        
        <!-- 节点版本 -->
        <div class="form-item required">
          <label>节点版本</label>
          <div class="input-wrapper">
            <select v-model="formData.nodeVersion">
              <option value="1.0.0">1.0.0</option>
            </select>
          </div>
        </div>
        
        <!-- 参数Tab切换 -->
        
        <!-- 输入参数内容 -->
        <div class="tab-content">
          <!-- 目标文件类型 -->
          <div class="form-item required">
            <label>目标文件类型</label>
            <div class="reference-button">参数</div>
            <div class="code-input">
              <div class="code-header">语法: javascript</div>
              <input 
                type="text" 
                v-model="formData.fileExtensions" 
                placeholder="输入文件后缀，用逗号分隔（如：c,h,cpp）"
              />
            </div>
          </div>
          
          <!-- 包含子目录 -->
          <div class="form-item">
            <label>包含子目录</label>
            <div class="checkbox-wrapper">
              <input 
                type="checkbox" 
                id="includeSubdirs" 
                v-model="formData.includeSubdirs"
              />
              <label for="includeSubdirs">是</label>
            </div>
          </div>
          
          <!-- 排除目录 -->
          <div class="form-item">
            <label>排除目录</label>
            <div class="reference-button">参数</div>
            <div class="code-input">
              <div class="code-header">语法: javascript</div>
              <input 
                type="text" 
                v-model="formData.excludeDirs" 
                placeholder="输入要排除的目录，用逗号分隔"
              />
            </div>
          </div>
          
          <!-- 分析类型 -->
          <div class="form-item required">
            <label>分析类型</label>
            <div class="input-wrapper">
                <input type="text" v-model="formData.analysisType" placeholder="输入分析类型"/>
            </div>
          </div>
          
          <!-- 扫描模式配置 - 仅在选择扫描特定模式时显示 -->
          <div class="form-item" v-if="formData.analysisType === 'patterns'">
            <label>扫描模式</label>
            <div class="reference-button">参数</div>
            <div class="code-input">
              <div class="code-header">语法: javascript</div>
              <textarea 
                v-model="formData.scanPattern" 
                placeholder="输入要扫描的模式，每行一个"
              ></textarea>
            </div>
          </div>
        </div>
        
        <!-- 输出参数内容 -->
        <!-- <div v-if="activeTab === 'output'" class="tab-content">
          <div class="form-item">
            <label>输出格式</label>
            <div class="input-wrapper">
              <select v-model="formData.outputFormat">
                <option value="json">JSON</option>
                <option value="csv">CSV</option>
                <option value="report">报告</option>
              </select>
            </div>
          </div>
          
          <div class="form-item">
            <label>输出详细文件列表</label>
            <div class="checkbox-wrapper">
              <input 
                type="checkbox" 
                id="outputFileList" 
                v-model="formData.outputFileList"
              />
              <label for="outputFileList">是</label>
            </div>
          </div>
        </div> -->
      </div>
      
      <!-- 底部按钮 -->
      <div class="drawer-footer">
        <button class="cancel-button" @click="closeDrawer">取消</button>
        <button class="save-button" @click="saveConfig">保存</button>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive } from 'vue';
  
  // 定义props
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    nodeData: {
      type: Object,
      default: () => ({})
    }
  });
  
  // 定义事件
  const emit = defineEmits(['update:visible', 'save']);
  
  // 表单数据
  const formData = reactive({
    nodeId: 'analyzer',
    nodeName: '分析',
    nodeVersion: '1.0',
    fileExtensions: 'c,h',
    includeSubdirs: true,
    excludeDirs: '',
    analysisType: '.c',
    scanPattern: '',
    outputFormat: 'json',
    outputFileList: false
  });
  
  // 当前激活的选项卡
  const activeTab = ref('input');
  
  // 关闭抽屉
  const closeDrawer = () => {
    emit('update:visible', false);
  };
  
  // 保存配置
  const saveConfig = () => {
    // 表单验证
    if (!formData.nodeId || !formData.fileExtensions) {
      // 显示错误信息或者处理验证失败
      return;
    }
    
    emit('save', { ...formData });
    closeDrawer();
  };
  </script>
  
  <style scoped>
  .node-config-drawer {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-left: 1px solid #eaeaea;
  }
  
  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #eaeaea;
  }
  
  .drawer-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
  
  .close-button {
    cursor: pointer;
    font-size: 20px;
  }
  
  .drawer-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }
  
  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    padding: 12px 20px;
    border-top: 1px solid #eaeaea;
  }
  
  .form-item {
    margin-bottom: 16px;
    position: relative;
  }
  
  .form-item.required label::before {
    content: "* ";
    color: #f56c6c;
  }
  
  .input-wrapper {
    position: relative;
  }
  
  .char-count {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    color: #999;
  }
  
  input, select, textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    outline: none;
  }
  
  input:focus, select:focus, textarea:focus {
    border-color: #409eff;
  }
  
  .checkbox-wrapper {
    display: flex;
    align-items: center;
  }
  
  .checkbox-wrapper input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
  }
  
  .tabs {
    display: flex;
    border-bottom: 1px solid #eaeaea;
    margin-bottom: 16px;
  }
  
  .tab {
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    color: #606266;
  }
  
  .tab.active {
    color: #409eff;
    border-bottom: 2px solid #409eff;
  }
  
  .code-input {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .code-header {
    background-color: #f5f7fa;
    padding: 4px 12px;
    font-size: 12px;
    color: #909399;
  }
  
  .code-input textarea {
    border: none;
    border-radius: 0;
    min-height: 80px;
    resize: vertical;
  }
  
  .code-input input {
    border: none;
    border-radius: 0;
  }
  
  .reference-button {
    position: absolute;
    right: 10px;
    top: 4px;
    font-size: 12px;
    color: #409eff;
    cursor: pointer;
  }
  
  .save-button, .cancel-button {
    padding: 8px 20px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
  }
  
  .save-button {
    background-color: #409eff;
    color: white;
    border: none;
    margin-left: 10px;
  }
  
  .cancel-button {
    background-color: #fff;
    color: #606266;
    border: 1px solid #dcdfe6;
  }
  </style>