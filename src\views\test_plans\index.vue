<template>

    <div class="tool-bar-container">
        <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button>

        <div style="margin-left: auto; display: flex; gap: 10px;">
            <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                <el-button text bg @click="handleReset">重置</el-button>
            </el-tooltip>
            <filterButton @click="onFilterStatusChange" :count="filterCount" />
            <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
        </div>
    </div>

    <div class="filter-container" v-if="showFilterContainer">
        <el-input v-model="form.name_re" placeholder="请输入计划名称" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
        <el-input v-model="form.creator_name_re" placeholder="请输入创建人" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
        <el-select v-model="form.plan_type_list" placeholder="请选择计划类型" @change="onFilter" style="width: 400px;" multiple
            clearable>
            <el-option label="全功能测试" value="FULL_FUNCTIONALITY_TEST"></el-option>
            <el-option label="版本回归测试" value="VERSION_REGRESSION_TEST"></el-option>
            <el-option label="专项验证测试" value="SPECIFIC_VALIDATION_TEST"></el-option>
            <el-option label="问题验证测试" value="PROBLEM_VALIDATION_TEST"></el-option>
            <el-option label="耐久测试" value="DURABILITY_TEST"></el-option>
        </el-select>
        <el-input v-model="form.software_version" placeholder="请输入产品软件版本" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
    </div>

    <el-table :data="tableData" stripe border style="width: 100%" class="table-container">
        <el-table-column prop="name" label="计划名称" width="200" align="center"></el-table-column>
        <el-table-column prop="project_name" label="所属项目" width="300" align="center"></el-table-column>
        <el-table-column prop="durability" label="计划类型" width="150" align="center">
            <template #default="{ row }">
                <el-tag v-if="row.plan_type == 'FULL_FUNCTIONALITY_TEST'" type="success">全功能测试</el-tag>
                <el-tag v-else-if="row.plan_type == 'VERSION_REGRESSION_TEST'" type="success">版本回归测试</el-tag>
                <el-tag v-else-if="row.plan_type == 'SPECIFIC_VALIDATION_TEST'" type="success">专项验证测试</el-tag>
                <el-tag v-else-if="row.plan_type == 'PROBLEM_VALIDATION_TEST'" type="success">问题验证测试</el-tag>
                <el-tag v-else-if="row.plan_type == 'DURABILITY_TEST'" type="primary">耐久测试</el-tag>
                <el-tag v-else type="danger">未知</el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="creator_name" label="创建人" width="100" align="center"></el-table-column>
        <el-table-column label="测试记录" width="100" align="center">
                <template #default="{ row }">
                    <el-link :underline="false" type="primary" @click="handleTestRecord(row)">查看</el-link>
                </template>
            </el-table-column>
        <el-table-column prop="software_version" label="产品软件版本" width="150" align="center"></el-table-column>
        <el-table-column label="关联版本" width="300" align="center">
            <template #default="{ row }">
                <el-tag v-for="item in row.product_version" type="info">{{ item?.name }}</el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="desc" label="计划描述" width="500" align="center"></el-table-column>
        <el-table-column label="操作" min-width="150" fixed="right" align="left">
            <template #default="{ row }">
                <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button @click="handleDelete(row)" type="danger" size="small">删除</el-button>
            </template>
        </el-table-column>

    </el-table>

    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
            @change="onPageChange" />
    </div>

</template>


<script setup>
import { ref, reactive, onMounted, onActivated, watch } from 'vue';
import http from '@/utils/http/http.js';
import { useRouter, useRoute } from 'vue-router';
import { useProjectStore } from '@/stores/project.js';
import filterButton from '@/components/filterButton.vue';
import { useTestPlanStore } from '@/stores/testPlan.js';
import { useTestRecordStore } from '@/stores/testRecord.js';

const router = useRouter();
const route = useRoute();
let projectStore = useProjectStore();
const filterCount = ref(0);
const tableData = ref([]);
let testPlanStore = useTestPlanStore();
let testRecordStore = useTestRecordStore();

let form = reactive({
    page: 1,
    pagesize: 10,
    project_number: '',
});

let total = ref(0);

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/test_plans', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'project_number'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleAdd() {
    router.push({ path: '/test_plans/add', query: { project_number: form.project_number } });
};

function handleEdit(row) {
    router.push({ path: '/test_plans/add', query: { type: 'edit', id: row.id } });
};

function handleDelete(row) {
    ElMessageBox.confirm(
        '确定删除吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.delete(`/test_plans/${row.id}`).then(res => {
            ElMessage({
                message: '删除成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessageBox.alert(
                err.response.data.msg,
                '警告',
                {
                    confirmButtonText: '确定',
                    type: 'warning',
                }
            )
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};

function handleRefresh() {
    update_table();
};

function handleTestRecord(row) {
    testRecordStore.setTestPlanId(row.id);
    router.push({ path: "/test_records/list"});
};

watch(() => projectStore.project_info, () => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

onActivated(() => {
    if (testPlanStore.softwareVersion) {
        form.software_version = testPlanStore.softwareVersion;
        testPlanStore.clearSoftwareVersion();
    }
    form.project_number = projectStore.project_info.projectCode; 
    update_table();
});

</script>


<style lang="scss" scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input,
    .el-select {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;

    .el-select {
        width: 500px;
        margin-left: 10px;
    }

}

.el-aside {
    width: 200px;
    height: 100% !important;

    margin-top: 20px;
}

.el-main {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);

    margin-top: 0px;

    .table-container {
        flex: 1;
        overflow: auto;
    }
}
</style>