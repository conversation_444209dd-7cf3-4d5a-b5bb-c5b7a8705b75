<template>
    <el-divider style="margin-top: 0;margin-bottom: 20px;" />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="项目" prop="project_number">
                <ProjectsComponent ref="projectsRef" v-model="form.project_number" :includePrefix="false"
                    :includeAll="false" />
            </el-form-item>

            <el-form-item label="产品类型"  prop="product_type">
                <el-select v-model="form.product_type" placeholder="请选择产品类型" clearable>
                            <el-option v-for="item in product_types" :key="item.id" :label="item.name"
                                :value="item.number"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="升级包类型"  prop="upgrade_type">
               <el-select placeholder="请选择升级包类型" v-model="form.upgrade_type">
                    <el-option label="MCU" :value="'MCU'"></el-option>
                    <el-option label="Touch" :value="'Touch'"></el-option>
                    
               </el-select>
            </el-form-item>

            <el-form-item label="零件号" prop="component_code">
                <el-input v-model="form.component_code" placeholder="请输入零件号"></el-input>
            </el-form-item>

            <el-form-item label="软件版本" prop="software_version">
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-input-number v-model="form.software_version[0]" :min="0" :max="255"
                            :controls="false"></el-input-number>
                    </el-col>
                    <el-col :span="12">
                        <el-input-number v-model="form.software_version[1]" :min="0" :max="255"
                            :controls="false"></el-input-number>
                    </el-col>
                </el-row>
            </el-form-item>

            <el-form-item label="显示与触摸屏版本" prop="screen_touchpad_version">
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-input-number v-model="form.screen_touchpad_version[0]" :min="0" :max="255"
                            :controls="false"></el-input-number>
                    </el-col>
                    <el-col :span="12">
                        <el-input-number v-model="form.screen_touchpad_version[1]" :min="0" :max="255"
                            :controls="false"></el-input-number>
                    </el-col>
                </el-row>
            </el-form-item >

            

            <el-form-item prop="input_files" width="100%">
                <InputFiles v-model="form.input_files" />
            </el-form-item>

            <el-form-item label="输出文件类型" prop="output_type">
                <el-radio-group v-model="form.output_type">
                    <el-radio-button value="bin">bin</el-radio-button>
                    <el-radio-button value="hex">hex</el-radio-button>
                    <el-radio-button value="s19">s19</el-radio-button>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="s19格式" prop="srecord_type" v-if="form.output_type === 's19'">
                <el-radio-group v-model="form.srecord_type">
                    <el-radio-button :value="1">s1</el-radio-button>
                    <el-radio-button :value="2">s2</el-radio-button>
                    <el-radio-button :value="3">s3</el-radio-button>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="输出文件名" prop="output_path">
                <el-input v-model="form.output_path" placeholder="输出文件名" disabled >
                </el-input>
            </el-form-item>

            <el-form-item label="PMIC升级">
                <el-radio-group v-model="form.pmic_flag">
                    <el-radio-button label="是" :value="1" />
                    <el-radio-button label="否" :value="0" />
                    <el-radio-button label="DISABLE" :value="2" />
                </el-radio-group>
            </el-form-item>

            <el-form-item label="加密" prop="encrypt">
                <el-radio-group v-model="form.encrypt">
                    <el-radio-button label="是" :value="0" />
                    <el-radio-button label="否" :value="1" />
                    <el-radio-button label="DISABLE" :value="2" />
                </el-radio-group>
            </el-form-item>

            <el-form-item label="加密密钥文件" prop="encrypt_file" v-if="form.encrypt === 0">
                <el-upload action="" :auto-upload="false" :limit="1" :on-exceed="handleEncryptFileExceed" :on-change="val => form.encrypt_file = val"
                    ref="encryptFileRef">
                    <el-button slot="trigger" type="primary" plain>选择加密密钥文件</el-button>
                </el-upload>
                <el-input style="display: none;"></el-input>
            </el-form-item>

            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit" :loading="loading">打包</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted,reactive, watch } from 'vue';
import { genFileId } from 'element-plus'
import ProjectsComponent from '@/components/projects.vue';
import http from '@/utils/http/http.js';
import InputFiles from './input_files.vue';
import { useProjectStore } from '@/stores/project.js';


const projectsRef = ref(null);
const formRef = ref(null);
const loading = ref(false);
const encryptFileRef = ref(null)
let projectStore = useProjectStore();
const pre_product_types = ref([]);
const product_types = ref([]);
const form = ref({
    project_number: projectStore.project_info.projectCode,
    component_code: '0000',
    software_version: [0, 0],
    screen_touchpad_version: [0, 0],
    output_type: 'bin',
    srecord_type: 1,
    output_path: '',
    pmic_flag: 1,
    encrypt: 1,
    encrypt_file: null,
    input_files: [{ identification: "", file: null, crc_vaild: "00" }],
    product_type: '',
    upgrade_type: '',
});

const rules = ref({
    project_number: [{ required: true, message: '请选择项目', trigger: 'change' }],
    component_code: [{ required: true, message: '请输入零件号', trigger: 'blur' }],
    input_files: [
        { validator: validateInputFiles, trigger: 'blur' }
    ],
    software_version: [{ required: true, message: '请输入软件版本', trigger: 'change' }],
    screen_touchpad_version: [{ required: true, message: '请输入显示与触摸屏版本', trigger: 'change' }],
    output_type: [{ required: true, message: '请选择输出文件类型', trigger: 'change' }],
    // output_path: [{ required: true, message: '请输入输出文件名', trigger: 'blur' }],
    encrypt_file: [{ required: true, message: '请选择加密密钥文件', trigger: 'change' }],
    product_type: [{ required: true, message: '请选择产品类型', trigger: 'change' }],
    upgrade_type: [{ required: true, message: '请选择升级包类型', trigger: 'change' }],
});
function validateInputFiles(rule, value, callback) {
    if (!value || value.length === 0) {
        return callback(new Error('请添加源文件'));
    }
    for (let i = 0; i < value.length; i++) {
        const file = value[i];
        if (!file.identification) {
            return callback(new Error(`源文件${i + 1}标识不能为空`));
        }
        if (!file.file) {
            return callback(new Error(`源文件${i + 1}不能为空`));
        }
        if (file.crc_vaild === undefined) {
            return callback(new Error(`源文件${i + 1}CRC校验方式不能为空`));
        }
    }
    callback();
}

const handleEncryptFileExceed = (files) => {
    encryptFileRef.value.clearFiles()
    const file = files[0]
    file.uid = genFileId()
    encryptFileRef.value.handleStart(file)
}

const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            const formData = new FormData();
            let projectInfo = projectsRef.value.getProjectInfo(form.value.project_number);

            formData.append('project_name', projectInfo.name);
            formData.append('project_number', form.value.project_number);
            formData.append('component_code', form.value.component_code);
            form.value.software_version.forEach(v => {
                formData.append('software_version', v);
            });
            form.value.screen_touchpad_version.forEach(v => {
                formData.append('screen_touchpad_version', v);
            });

            form.value.input_files.forEach((v, index) => {
                if (v.file) {
                    formData.append(`input_files`, v.file.raw);
                    formData.append('input_file_identifs', v.identification)
                    formData.append('input_file_crc_vailds', v.crc_vaild);
                } 
            });
            formData.append('output_type', form.value.output_type);
            formData.append('srecord_type', form.value.srecord_type);
            formData.append('output_path', form.value.output_path);
            formData.append('pmic_flag', form.value.pmic_flag);
            formData.append('encrypt', form.value.encrypt);
            if (form.value.encrypt === 0) {
                formData.append('encrypt_file', form.value.encrypt_file.raw);
            }
            
            loading.value = true;
            http.post('/pack_tool/records', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                },
                timeout: 20000,
            }).then(res => {
                ElMessage({
                    message: '添加成功.',
                    type: 'success',
                });
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    type: 'error',
                })
            }).finally(() => {
                loading.value = false;
            });
        };
    });
};

const emit = defineEmits(['confirm', 'cancel']);

function onCancel() {
    emit('cancel');
};

watch([() => form.value.product_type, () => form.value.upgrade_type, () => form.value.output_type, () => form.value.software_version[0], () => form.value.software_version[1]], () => {
    if (form.value.product_type && form.value.upgrade_type && form.value.output_type) {
        var temp_version_0 = form.value.software_version[0].toString().padStart(2, "0")
        var temp_version_1 = form.value.software_version[1].toString().padStart(2, "0")
        form.value.output_path = `${form.value.project_number}_${form.value.product_type}_${form.value.upgrade_type}_V${temp_version_0}.${temp_version_1}.${form.value.output_type}`
    }
})


onMounted(() => {
    http.get('/product_types', { params: { pagesize: 100000 } }).then(res => {
        let data = res.data.data.results;
        pre_product_types.value = data;
        form.product_type = '';
        product_types.value = pre_product_types.value;

    }).catch(err => {
        console.log(err);
    });


});



</script>

<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>