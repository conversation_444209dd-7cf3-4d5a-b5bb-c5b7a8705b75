<template>
    <el-container class="app-container">
        <el-aside class="side-container">
            <Sider />
        </el-aside>
        <el-container class="main-container">
                <el-header class="nav-container no-padding">
                    <NavBar />
                </el-header>
            <el-main class="content-container">
                <Content />
            </el-main>
        </el-container>
    </el-container>
</template>


<script setup>
import Sider from './Sider/index.vue';
import NavBar from './NavBar/index.vue';
import Content from './Content/index.vue';
</script>

<style scoped lang="scss">
.side-container {
    width: auto;
    overflow: hidden;
    border-right: 1px solid #ebeef5;
}

.main-container {
    .content-container {
        border-width: 11px 22px 29px;
        border-style: solid;
        border-color: #eaedf0;
    }
}

.nav-container {
    height: 50px;
    box-sizing: border-box;
}

</style>
