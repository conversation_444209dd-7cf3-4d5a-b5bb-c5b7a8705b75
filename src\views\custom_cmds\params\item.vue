<template>
    <div style="width: 100%;">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">
            <el-form-item label="参数类型" prop="param_type">
                <el-select v-model="form.param_type">
                    <el-option label="字符串" value="el-input"></el-option>
                    <el-option label="数字" value="el-input-number"></el-option>
                    <el-option label="选项" value="el-select"></el-option>
                    <el-option label="颜色" value="el-color-picker"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="名称" prop="param_name">
                <el-input  v-model="form.param_name"></el-input>
            </el-form-item>
            <el-form-item label="编码">
                <el-input  v-model="form.param_code"></el-input>
            </el-form-item>
            <el-form-item label="默认值">
                <el-input v-model="form.param_value"></el-input>
            </el-form-item>

            <template v-if="form.param_type === 'el-input-number'">
                <el-form-item label="最小值">
                    <el-input v-model="form.param_min"></el-input>
                </el-form-item>
                <el-form-item label="最大值">
                    <el-input v-model="form.param_max"></el-input>
                </el-form-item>
            </template>

            <template v-else-if="form.param_type === 'el-select'">
                <el-form-item label="选项">
                    <Options v-model="form.param_options" />
                </el-form-item>
            </template>

            <template v-else-if="form.param_type === 'el-color-picker'">    
            </template>


        </el-form>
        <div class="submit-button-container">
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onSubmit">提交</el-button>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import Options from './options.vue';

const props = defineProps({
    param: {
        type: Object,
        default: () => ({})
    }
})

const formRef = ref(null);

const form = ref({
    param_type: props.param.param_type || '',
    param_name: props.param.param_name || '',
    param_code: props.param.param_code || '',
    param_value: props.param.param_value == undefined ? '' : props.param.param_value,
    param_min: props.param.param_min == undefined ? '' : props.param.param_min,
    param_max: props.param.param_max == undefined ? '' : props.param.param_max,
    param_options: props.param.param_options || [{ label: '', value: '' }],
});

const rules = ref({
    param_type: [
        { required: true, message: '请选择参数类型', trigger: 'blur' },
    ],
    param_name: [
        { required: true, message: '请输入参数名称', trigger: 'blur' },
    ],
});


const emit = defineEmits(['submit', 'cancel'])

const onSubmit = () => {
    formRef.value.validate((valid) => {
        if (valid) {
            emit('submit', form.value);
        };
    });
};

const onCancel = () => {
    emit('cancel');
};


</script>

<style lang="scss" scoped>

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}

</style>