<template>

    <el-badge :value="count" :hidden="count <= 0" :max="99" class="item">
        <el-button text bg @click="onStatusChange" :class="{ inuse: count > 0  }">
            筛选
            <SvgIcon name="icons8:double-up" source="iconify" size="1em" :class="{ rotated: isRotated}"
                class="svg-icon" />
        </el-button>
    </el-badge>

</template>

<script setup>
import { ref, computed } from 'vue';
import SvgIcon from '@/components/SvgIcon/index.vue';

const props = defineProps({
    count: {
        type: Number,
        default: 0
    },
    expand: {
        type: Boolean,
        default: false,
    }
});
const count = computed(() => props.count);
const isRotated = ref(props.expand);

const emit = defineEmits(['click']);

const onStatusChange = () => {
    isRotated.value = !isRotated.value;
    emit('click');
};

</script>


<style lang="scss" scoped>
.svg-icon {
    transition: transform 0.5s ease;
}

.rotated {
    transform: rotate(180deg);
}

.inuse {
    color: #409eff;
}

</style>