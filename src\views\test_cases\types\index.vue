<template>
    <div style="display: flex; flex-direction: column; height: calc(109.35vh - 250px);">
    <div class="c-container">

        <div class="tool-bar-container" style="display: flex;">
            <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button>
            <div style="margin-left: auto;">
                <el-button type="info" plain @click="onFilterStatusChange">
                筛选<el-icon class="el-icon--right">
                    <component :is="filterButtonIcon"></component>
                </el-icon>
            </el-button>
            </div>
        </div>

        <div class="filter-container" v-if="showFilterContainer">
            <el-input v-model="form.name_re" placeholder="请输入名称" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
            <el-input v-model="form.number_re" placeholder="请输入编号" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
        </div>

        <el-table :data="tableData" stripe border style="width: 100%;flex: 1;" class="table-container">
    <el-table-column prop="name" label="名称" min-width="200" align="center"></el-table-column>
    <el-table-column prop="number" label="编号" min-width="300" align="center"></el-table-column>
    <el-table-column prop="desc" label="描述" min-width="500" align="center"></el-table-column>
    <el-table-column label="操作" min-width="120" fixed="right" align="center">
        <template #default="{ row }">
            <div style="display: flex; justify-content: center; gap: 10px;">
                <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
            </div>
        </template>
    </el-table-column>
</el-table>

        <div class="pagination-container">
            <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
                v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
                @change="onFilter" />
        </div>

        <el-dialog v-if="dialogAddVisible" v-model="dialogAddVisible" title="添加用例类型" width="800"
            :close-on-click-modal="false">
            <Add @confirm="onAddConfirm" @cancel="onAddCancel" />
        </el-dialog>

        <el-dialog v-if="dialogEditVisible" v-model="dialogEditVisible" title="编辑用例类型" width="800"
            :close-on-click-modal="false">
            <Edit @confirm="onEditAffirm" @cancel="onEditCancel" :r_id="r_id" />
        </el-dialog>
    </div>
</div>
</template>


<script setup>

import { ref, reactive, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import Add from './add.vue';
import Edit from './edit.vue';

const tableData = ref([]);

let r_id = ref(0);

const dialogAddVisible = ref(false);

const dialogEditVisible = ref(false);

let form = reactive({
    page: 1,
    pagesize: 10,
});

let total = ref(0);

let showFilterContainer = ref(false);
let filterButtonIcon = ref("ArrowDown");

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
    if (showFilterContainer.value) {
        filterButtonIcon.value = "ArrowUp";
    } else {
        filterButtonIcon.value = "ArrowDown";
    }
};

function update_table() {
    http.get('/test_m/test_case_types', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });
};

function onFilter() {
    update_table();
};

function handleAdd() {
    dialogAddVisible.value = true;
};

function handleEdit(row) {
    r_id.value = row.id;
    dialogEditVisible.value = true;
};

function handleDelete(row) {
    ElMessageBox.confirm(
        '确定删除吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.delete(`/test_m/test_case_types/${row.id}`).then(res => {
            ElMessage({
                message: '删除成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessage({
                type: 'error',
                message: err.response.data.msg
            });
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};

function onAddConfirm() {
    dialogAddVisible.value = false;
    update_table();
};

function onAddCancel() {
    dialogAddVisible.value = false;
};

function onEditAffirm() {
    dialogEditVisible.value = false;
    update_table();
};

function onEditCancel() {
    dialogEditVisible.value = false;
};

onMounted(() => {
    update_table();
});

</script>


<style scoped>
.c-container {
    width: 100%;
    height: calc(100vh - 150px);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.table-container {
    flex: 1 1 auto;
}

.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}
</style>