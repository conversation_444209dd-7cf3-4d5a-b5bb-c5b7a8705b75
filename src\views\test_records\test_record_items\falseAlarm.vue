<template>

    <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

        <el-form-item label="误判原因" prop="reason">
            <el-input type="textarea" :rows="4" v-model="form.reason" placeholder="请输入误判原因"></el-input>
        </el-form-item>

        <div class="button-container">
            <el-button type="info" @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onConfirm">确认</el-button>
        </div>

    </el-form>



</template>

<script setup>
import { ref } from 'vue'
import http from '@/utils/http/http.js';

const props = defineProps({
    r_id: {
        type: Number,
        required: true,
    },
})

const formRef = ref(null)

const form = ref({
    reason: ''
})

const rules = ref({
    reason: [
        { required: true, message: '请输入误判原因', trigger: 'blur' }
    ]
})

const emit = defineEmits(['cancel', 'confirm'])

const onCancel = () => {
    emit('cancel')
}

const onConfirm = () => {
    formRef.value.validate((valid) => {
        if (valid) {
            http.post('/test_records/items/false_alarm', {
                id: props.r_id,
                reason: form.value.reason,
            }).then(res => {
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(
                    err.response.data.msg,
                    '警告',
                    {
                        confirmButtonText: '确定',
                        type: 'warning',
                    }
                )
            });
        }
    })
}


</script>

<style scoped>
.button-container {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    margin-top: 20px;
}
</style>