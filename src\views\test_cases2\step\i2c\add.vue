<template>
    <el-form :model="form" :rules="rules" ref="formRef">
        <el-form-item label="指令" prop="cmd">
            <el-input v-model="form.cmd"></el-input>
        </el-form-item>
        <el-form-item label="接收消息">
            <el-input v-model="form.recv_msg"></el-input>
        </el-form-item>
        <el-form-item label="判断条件" prop="option">
            <el-select v-model="form.option">
                <el-option label="完全匹配" value="完全匹配"></el-option>
                <el-option label="不匹配" value="不匹配"></el-option>
                <el-option label="等于" value="等于"></el-option>
                <el-option label="范围" value="范围"></el-option>
            </el-select>
        </el-form-item>

        <el-form-item v-if="form.option == '等于'" label="精准值" prop="equal">
            <el-input-number v-model="form.equal" :precision="2" :step="0.01" class="half-width"></el-input-number>
        </el-form-item>

        <el-row :gutter="20" v-if="form.option == '范围'">
            <el-col :span="12">
                <el-form-item label="最小值" prop="min">
                    <el-input-number v-model="form.min" :precision="2" :step="0.01"
                        class="full-width"></el-input-number>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="最大值" prop="max">
                    <el-input-number v-model="form.max" :precision="2" :step="0.01"
                        class="full-width"></el-input-number>
                </el-form-item>
            </el-col>
        </el-row>

        <el-form-item label="循环周期(ms)" prop="period">
            <el-input-number v-model="form.period" :min="0" class="half-width"></el-input-number>
        </el-form-item>
    </el-form>
</template>


<script setup>

import { ref, watch } from 'vue';

const props = defineProps({
    initData: {
        type: Object,
        default: () => ({}),
    },
});

const model = defineModel();

const formRef = ref(null);

const form = ref({
    cmd: '',
    recv_msg: '',
    option: '完全匹配',
    equal: 0,
    min: 0,
    max: 0,
    period: 0,
});

const rules = ref({
    cmd: [
        { required: true, message: '请输入指令', trigger: 'blur' },
    ],
    option: [
        { required: true, message: '请输入判断条件', trigger: 'blur' },
    ],
    equal: [
        { required: true, message: '请输入值', trigger: 'blur' },
    ],
    min: [
        { required: true, message: '请输入最小值', trigger: 'blur' },
    ],
    max: [
        { required: true, message: '请输入最大值', trigger: 'blur' },
    ],
    period: [
        { required: true, message: '请输入循环周期', trigger: 'blur' },
    ],
});

watch(form, () => {
    model.value = form.value;
}, { immediate: true });

watch(() => props.initData, (val) => {
    Object.assign(form.value, val);
}, { immediate: true });


const validate = (callback) => {
    formRef.value.validate(callback);
};

defineExpose({
    validate,
});

</script>

<style scoped>
.full-width {
    width: 100%;
}

.half-width {
    width: 50%;
}
</style>