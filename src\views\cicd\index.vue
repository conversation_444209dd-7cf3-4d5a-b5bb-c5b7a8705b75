    <template>
        <div class="cicd-container">
            <el-card class="box-card">
                <template #header>
                    <div class="header-container">
                        <div class="title-container">
                            <span class="main-title">Git代码分析工具</span>
                        </div>
                        <div class="logo-container">
                            <el-icon class="logo-icon"><StarFilled /></el-icon>
                        </div>
                    </div>
                </template>
                
                <div class="card-content">
                    <div class="project-section">
                        <h2 class="section-title">项目选择</h2>
                        
                        <div class="search-container">
                            <el-input 
                                v-model="searchQuery" 
                                placeholder="输入项目名称或项目描述..." 
                                class="search-input">
                            </el-input>
                            <el-button type="primary" class="search-button" @click="searchProjects">搜索</el-button>
                        </div>
                        
                        <div class="project-list-header">
                            <span>可用项目</span>
                            <el-button type="danger" @click="confirmSelection">确定</el-button>
                        </div>
                        
                        <div class="project-list">
                            <div v-for="(project, index) in projects" :key="index" class="project-item">
                                <el-checkbox v-model="project.selected" :disabled="isProjectSelectionConfirmed"></el-checkbox>
                                <div class="project-info">
                                    <span class="project-name">{{ project.name }}</span>
                                    <span class="project-description">项目描述: {{ project.description||'无' }}</span>
                                    <span class="project-date">最后更新: {{ project.last_activity }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="operation-section">
                        <h2 class="section-title">操作</h2>
                        <!-- 添加工作流进度指示器 -->
                        <div class="workflow-indicator">
                            <div class="step" :class="{ active: workflowStep >= 1, completed: workflowStep > 1 }">确定</div>
                            <div class="arrow">→</div>
                            <div class="step" :class="{ active: workflowStep >= 2, completed: workflowStep > 2 }">克隆</div>
                            <div class="arrow">→</div>
                            <div class="step" :class="{ active: workflowStep >= 3, completed: workflowStep > 3 }">扫描</div>
                            <div class="arrow">→</div>
                            <div class="step" :class="{ active: workflowStep >= 4, completed: workflowStep > 4 }">分析</div>
                            <div class="arrow">→</div>
                            <div class="step" :class="{ active: workflowStep >= 5, completed: workflowStep > 5 }">推送至产品开发平台</div>
                        </div>
                        <div class="operation-buttons">
                            <el-button 
                                type="primary" 
                                @click="completeAction" 
                                :loading="isCloning"
                                :disabled="!isProjectSelectionConfirmed || workflowStep > 2 || isCloning"
                                :class="{ 'highlighted-button': isProjectSelectionConfirmed && workflowStep === 1 }">
                                {{ isCloning ? '克隆中...' : '克隆' }}
                            </el-button>
                            <el-button 
                                @click="scanAction" 
                                :loading="isScanning"
                                :disabled="workflowStep !== 2 || isScanning"
                                :class="{ 'highlighted-button': workflowStep === 2 }">
                                {{ isScanning ? '扫描中...' : '扫描' }}
                            </el-button>
                            <el-button 
                                @click="analyzeAction" 
                                :loading="isAnalyzing"
                                :disabled="workflowStep !== 3 || isAnalyzing"
                                :class="{ 'highlighted-button': workflowStep === 3 }">
                                {{ isAnalyzing ? '分析中...' : '分析' }}
                            </el-button>
                            <el-button 
                                @click="exportAction" 
                                :loading="isPushing"
                                :disabled="workflowStep !== 4 || isPushing"
                                :class="{ 'highlighted-button': workflowStep === 4 }">
                                {{ isPushing ? '推送中...' : '推送至产品开发平台' }}
                            </el-button>
                            <el-button 
                                type="success" 
                                @click="goToWorkflowEditor">
                                可视化流程编辑器
                            </el-button>
                        </div>
                    </div>
                    
                    <div class="progress-section">
                        <h2 class="section-title">进度</h2>
                        <el-progress :percentage="progressPercentage" :format="format" />
                    </div>
                    <div v-if="analysisResultUrl" class="analysis-url-section">
                        <h2 class="section-title">分析结果</h2>
                        <div class="url-container">
                            <span class="url-label">项目分析结果</span>
                            <el-link type="primary" :href="analysisResultUrl" target="_blank">
                                <el-icon><Document /></el-icon>
                                点击查看详细分析报告
                            </el-link>
                        </div>
                    </div>
                    
                    <div class="log-section">
                        <h2 class="section-title">操作日志</h2>
                        <div class="log-container">
                            <div v-for="(log, index) in operationLogs" :key="index" class="log-item">
                                {{ log }}
                            </div>
                        </div>
                    </div>
                </div>
            </el-card>
        </div>
    </template>

    <script setup>
    import { ref, computed, onMounted } from 'vue';
    import { ElMessage, ElMessageBox } from 'element-plus'; // 引入 ElMessage 和 ElMessageBox 组件
    import http from '@/utils/http/http';
    import { Document } from '@element-plus/icons-vue';
    import { useRouter } from 'vue-router';

    // 搜索
    const searchQuery = ref('');
    const analysisResultUrl = ref(''); 
    const searchProjects = () => {
        // 实现搜索功能
        console.log('搜索:', searchQuery.value);
    };
    const allProjects = ref([]);


    const workflowStep = ref(0);   // 工作流步骤：0=初始状态, 1=已确认选择, 2=已克隆, 3=已扫描, 4=已分析, 5=已导出
    const isProjectSelectionConfirmed = ref(false);
    

    const isCloning = ref(false);   //克隆
    const isScanning = ref(false);  //扫描
    const isAnalyzing = ref(false); //分析
    const isPushing = ref(false);   //推送

    // 项目列表
    const projects = computed(() => {
        if (!searchQuery.value.trim()) {
            return allProjects.value;
        }
         
        const keyword = searchQuery.value.toLowerCase();
        return allProjects.value.filter(project => 
            project.name.toLowerCase().includes(keyword) || 
            (project.description && project.description.toLowerCase().includes(keyword))
        );
    });

    // 确认选择项目
    const confirmSelection = () => {
        let total = 0;
        projects.value.forEach(project => {
            if (project.selected) {
                total++;
            }
        });
        
        if (total === 0) {
            ElMessage({
                message: '请至少选择一个项目',
                type: 'info',
                duration: 2000
            });
            return;
        } else if (total > 1) {
            ElMessage({
                message: '此版本只能选择一个项目',
                type: 'warning',
                duration: 3000
            });
            
            for (let i = 0; i < projects.value.length; i++) {
                projects.value[i].selected = false;
            }
            return;
        }
        
        ElMessageBox.confirm('确定选择该项目进行操作吗？', '确认选择', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            // 用户点击确定
            const selectedProject = projects.value.find(project => project.selected);
            isProjectSelectionConfirmed.value = true;
            workflowStep.value = 1; // 更新工作流状态
            
            ElMessage({
                message: `已确认选择项目: ${selectedProject.name}`,
                type: 'success',
                duration: 2000
            });
            
            addLog(`已确认选择项目: ${selectedProject.name}`);
            addLog('请点击"克隆"按钮开始操作');
            
            // 高亮克隆按钮
            highlightNextButton();
        }).catch(() => {
            // 用户点击取消
            ElMessage({
                type: 'info',
                message: '已取消选择'
            });
        });
    };

    const selectedProjects = computed(() => {
        return projects.value.filter(project => project.selected);
    });


    const highlightNextButton = () => {
        console.log('高亮下一个按钮');
    };


    const progressPercentage = ref(50);
    const format = (percentage) => `${percentage}%`;


    const updateProgress = (step) => {
        const progressMap = {
            0: 0,
            1: 20,
            2: 40,
            3: 60,
            4: 80,
            5: 100
        };
        progressPercentage.value = progressMap[step];
    };

    const completeAction = async () => {
        if (!isProjectSelectionConfirmed.value) {
            ElMessage({
                message: '请先确认选择项目',
                type: 'warning',
                duration: 2000
            });
            return;
        }
        
        const selectedProject = projects.value.find(project => project.selected);  
        addLog(`开始克隆项目: ${selectedProject.name}...`);
        
        const status = await cloneProject();
        console.log(status);
        if (status) {
            workflowStep.value = 2; // 更新状态为已克隆
            updateProgress(workflowStep.value);
            addLog(`成功克隆项目: ${selectedProject.name}`);
            addLog('请点击"扫描"按钮继续');
            
            // 高亮下一个按钮
            highlightNextButton();
            
            ElMessage({
                message: '克隆完成，可以进行扫描了',
                type: 'success',
                duration: 2000
            });
        } else {
            ElMessage({
                message: '克隆失败，请检查网络连接',
                type: 'error',
                duration: 2000
            });
        }
    };

    const cloneProject = async () => { 
        const project = projects.value.find(project => project.selected);
        try {
            isCloning.value = true;  // 开始克隆，设置加载状态
            const response = await http.get('/testqueuegit_project_clone', {
                timeout: 50000,
                params: {
                    project_name: project.name
                }
            });
            addLog(response.data.message);
            return response.data.result;
        } catch (error) {
            console.error('克隆项目失败:', error);
            addLog('克隆项目失败，请检查网络连接');
            return false;
        } finally {
            isCloning.value = false;  // 无论成功失败，都要关闭加载状态
        }
    }

    const scanProject = async () => { 
        const project = projects.value.find(project => project.selected);
        try {
            isScanning.value = true;   
            const response = await http.get('/testqueueproject_scanner', {
                timeout: 30000,
                params: {
                    project_name: project.name
                }
            });
            addLog(response.data.message);
            return response.data.code;
        } catch (error) {
            console.error('扫描项目失败:', error);
            addLog('扫描项目失败，请检查网络连接');
            return false;
        } finally {
            isScanning.value = false;  // 无论成功失败，都要关闭加载状态
        }
    }

    const scanAction = async () => {
        const selectedProject = projects.value.find(project => project.selected);
        addLog(`开始扫描项目: ${selectedProject.name}...`);
        const result = await scanProject();
        if (result === 1004) {
            workflowStep.value = 3; // 更新状态为已扫描
            updateProgress(workflowStep.value);
            // 高亮下一个按钮
            highlightNextButton();
            
            ElMessage({
                message: '扫描完成，可以进行分析了',
                type: 'success',
                duration: 2000
            });
        } else if (result === 1002){
            ElMessage({
                message: '项目未克隆',
                type: 'error',
                duration: 2000
            });
        }
        else if (result === 1003) {
            ElMessage({
                message: '项目中没有.c文件 ，无法进行分析',
                type: 'error',
                duration: 2000
            });

        }
        else {
            ElMessage({
                message: '扫描失败，请检查网络连接',
                type: 'error',
                duration: 2000
            });
        }
    };

    const analyzeProject = async () => { 
        const project = projects.value.find(project => project.selected);
        try {
            isAnalyzing.value = true;   
            const response = await http.get('/testqueueproject_analysis', {
                timeout: 30000,
                params: {
                    project_name: project.name
                }
            });
            return [response.data.result, response.data.analysis_result,response.data.parent_url];
        } catch (error) {
            console.error('分析项目失败:', error);
            addLog('分析项目失败，请检查网络连接');
            return false;
        } finally {
            isAnalyzing.value = false;  // 无论成功失败，都要关闭加载状态
        }
    }

    const pushProject = async () => { 
        const project = projects.value.find(project => project.selected);
        try {
            isPushing.value = true;   
            const response = await http.get('/testqueueproject_export', {
                timeout: 30000,
                params: {
                    project_name: project.name
                }
            });
            addLog(response.data.message || '推送完成');
            return response.data.code || response.data.result;
        } catch (error) {
            console.error('推送项目失败:', error);
            addLog('推送项目失败，请检查网络连接');
            return false;
        } finally {
            isPushing.value = false;  // 无论成功失败，都要关闭加载状态
        }
    }

    const analyzeAction = async () => {
        const selectedProject = projects.value.find(project => project.selected);
        addLog(`开始分析项目代码: ${selectedProject.name}...`);
        
        const [result, analysis_result, parent_url] = await analyzeProject();
        console.log(analysis_result)
        if (result === true) {
            workflowStep.value = 4; // 更新状态为已分析
            updateProgress(workflowStep.value);
            addLog(`代码分析完成`);
                if (Array.isArray(analysis_result) && analysis_result.length > 0) {
                    const firstFile = analysis_result[0];
                    addLog(`示例文件: ${firstFile.文件名}`);
                    addLog(`  路径: ${firstFile.文件路径}`);
                    addLog(`  头文件: ${firstFile.头文件}`);
                    
                    if (firstFile.函数列表.length > 0) {
                        const function_list = firstFile.函数列表.length>3?3:firstFile.函数列表.length;
                        addLog(`  包含函数 (显示前${function_list}个):`);
                        firstFile.函数列表.slice(0, function_list).forEach(func => {
                            addLog(`    - ${func.函数名}`);
                        });
                        if (firstFile.函数列表.length > function_list) {
                            addLog(`    ...以及其他 ${firstFile.函数列表.length - function_list} 个函数`);
                        }
                    }
                    addLog(`其余 ${analysis_result.length - 1} 个文件的信息已省略`);
                    if (parent_url) {
                        analysisResultUrl.value = parent_url;
                        addLog(`分析结果已生成，可以查看完整报告`);
                    }
                }
            addLog('请点击"推送至产品开发平台"按钮继续');
            highlightNextButton();  //高亮下一个按钮 
            ElMessage({
                message: '分析完成，可以推送到产品开发平台了',
                type: 'success',
                duration: 2000
            });
        } else {
            ElMessage({
                message: '分析失败，请检查网络连接',
                type: 'error',
                duration: 2000
            });
        }
    };

    const exportAction = async () => {
        const selectedProject = projects.value.find(project => project.selected);
        addLog(`功能待开发: ${selectedProject.name}...`);
        ElMessage({
            message: '功能待开发',
            type: 'warning',
            duration: 2000
        })
    };
        
    const operationLogs = ref([]);

    const addLog = (log) => {
        const now = new Date();
        const hours = now.getHours().toString().padStart(2, '0');
        const minutes = now.getMinutes().toString().padStart(2, '0');
        const seconds = now.getSeconds().toString().padStart(2, '0');
        const timeString = `${hours}:${minutes}:${seconds}`;
        operationLogs.value.unshift(`${timeString} - ${log}`);
    };

    const fetchProjects = () => {
        http.get('/testqueueall_project', {
            timeout: 10000
        })
            .then(response => {
                // 更新原始数据
                allProjects.value = response.data.data.map(project => ({
                    ...project,
                    selected: false // 确保每个项目都有 selected 属性
                }));
                if (allProjects.value.length === 1) {
                    allProjects.value[0].selected = true;  //  只有一个元素默认选择
                }
            
            })
            .catch(error => {
                console.error('获取项目列表失败:', error);
                if (error.response && error.response.status === 503) {
                    addLog('获取项目列表失败：后端服务 AutoTestMicroservices 未响应，请检查服务状态。');
                    return;
                }
                addLog('获取项目列表失败，请检查网络连接');
            });
    };

    // 生命周期钩子 - 在组件挂载后自动获取项目列表
    onMounted(() => {
        console.log('组件已挂载，开始获取项目列表');
        fetchProjects();
        addLog('欢迎使用Git代码分析工具');
        addLog('请按照操作步骤执行')
        addLog('请选择想分析的项目并点击"确定"按钮开始');
        updateProgress(workflowStep.value); // 初始化进度条
    }); 

    // 获取路由器实例
    const router = useRouter();

    // 跳转到工作流编辑器
    const goToWorkflowEditor = () => {
        router.push('/workflow/editor');
    };
    </script>

    <style scoped>
    .cicd-container {
        padding: 20px;
        width: 100%;
    }

    .box-card {
        width: 100%;
    }

    .header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #3080dd;
        margin: -20px;
        padding: 15px 20px;
        color: white;
    }

    .main-title {
        font-size: 24px;
        font-weight: bold;
    }

    .logo-icon {
        font-size: 24px;
        color: white;
    }

    .card-content {
        padding: 20px 0;
    }

    .section-title {
        font-size: 18px;
        margin-bottom: 15px;
        font-weight: normal;
    }

    .search-container {
        display: flex;
        margin-bottom: 20px;
    }

    .search-input {
        flex: 1;
        margin-right: 10px;
    }

    .project-list-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        font-weight: bold;
    }

    .project-list {
        border: 1px solid #ebeef5;
        border-radius: 4px;
        padding: 0;
        margin-bottom: 20px;
        max-height: 300px; /* 增加高度，从200px改为300px */
        overflow-y: auto;
    }

    .project-item {
        padding: 10px 15px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: flex-start; /* 改为flex-start，让内容从顶部对齐 */
    }

    .project-item:last-child {
        border-bottom: none;
    }

    .project-info {
        flex: 1;
        display: flex;
        flex-direction: column; /* 改为纵向排列 */
        gap: 5px; /* 添加间距 */
        margin-left: 10px;
    }

    .project-name {
        font-size: 14px;
        font-weight: bold; /* 加粗项目名称 */
    }

    .project-date {
        color: #909399;
        font-size: 14px;
    }

    /* 添加工作流指示器样式 */
    .workflow-indicator {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding: 10px;
        background-color: #f5f7fa;
        border-radius: 4px;
    }

    .step {
        padding: 4px 12px;
        border-radius: 4px;
        background-color: #ebeef5;
        color: #909399;
        font-size: 14px;
    }

    .step.active {
        background-color: #409eff;
        color: white;
    }

    .step.completed {
        background-color: #67c23a;
        color: white;
    }

    .arrow {
        margin: 0 8px;
        color: #909399;
    }

    .operation-buttons {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }

    /* 添加按钮高亮效果 */
    .highlighted-button {
        box-shadow: 0 0 10px rgba(64, 158, 255, 0.8);
        animation: pulse 1.5s infinite;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 5px rgba(64, 158, 255, 0.8);
        }
        50% {
            box-shadow: 0 0 15px rgba(64, 158, 255, 0.8);
        }
        100% {
            box-shadow: 0 0 5px rgba(64, 158, 255, 0.8);
        }
    }

    .progress-section {
        margin-bottom: 20px;
    }

    .log-container {
        border: 1px solid #ebeef5;
        border-radius: 4px;
        padding: 10px;
        max-height: 400px;
        overflow-y: auto;
        background-color: #f9f9f9;
    }
    .log-section {
        margin-top: 25px; /* 添加外边距 */
    }
    .log-item {
        padding: 5px 0;
        font-family: monospace;
        font-size: 14px;
    }

    .project-description, .project-date {
        color: #909399;
        font-size: 14px;
    }
    .analysis-url-section {
    margin-bottom: 20px;
    padding: 15px;
        background-color: #f0f9eb;
        border-radius: 4px;
        border-left: 4px solid #67c23a;
    }

    .url-container {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .url-label {
        font-weight: bold;
    }
    </style>