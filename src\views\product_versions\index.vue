<template>
    <div style="display: flex; flex-direction: column; height: calc(104vh - 250px);">

        <div class="tool-bar-container">

            <div style="margin-left: auto; display: flex; gap: 10px;">
                <div>
                    <el-button icon="Setting" text bg @click="isSettingVisible = true">设置</el-button>
                    <!-- 设置弹窗 -->
                    <el-popover v-model:visible="isSettingVisible" width="180" trigger="manual" placement="bottom">
                        <template #reference>
                            <!-- 设置按钮作为触发器 -->
                            <div style="display: inline-block;"></div>
                        </template>
                        <!-- 操作按钮 -->
                        <div class="column-popper-title">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <el-checkbox :model-value="tableColumns.every(item => item.show)"
                                    :indeterminate="tableColumns.some(item => item.show) && tableColumns.some(item => !item.show)"
                                    label="列展示" @change="selectAllColumn" />
                                <el-button text @click="resetColumns" style="margin-right: -10px;">重置</el-button>
                            </div>
                        </div>
                        <!-- 列设置内容 -->
                        <div class="column-content" style="max-height: 200px; overflow-y: auto;">
                            <div class="column-item" v-for="column in tableColumns" :key="column.key">
                                <el-checkbox v-model="column.show" :label="column.name"
                                    :disabled="column.disabled"></el-checkbox>
                            </div>
                        </div>
                    </el-popover>

                </div>
                <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                    <el-button text bg @click="handleReset">重置</el-button>
                </el-tooltip>
                <filterButton @click="onFilterStatusChange" :count="filterCount" />
                <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
            </div>
        </div>

        <div class="filter-container" v-if="showFilterContainer">
            <el-select v-model="form.status_list" placeholder="请选择版本状态" @change="onFilter" style="width: 400px;"
                multiple clearable>
                <el-option label="已提测" value="SUBMITTED"></el-option>
                <el-option label="测试中" value="TESTING"></el-option>
                <el-option label="测试不通过" value="FAILED"></el-option>
                <el-option label="测试通过" value="PASSED"></el-option>
                <el-option label="已发布" value="RELEASED"></el-option>
                <el-option label="已量产" value="PRODUCTION"></el-option>
                <el-option label="已废弃" value="DEPRECATED"></el-option>
            </el-select>
            <el-select v-model="form.type_list" placeholder="请选择版本类型" @change="onFilter" style="width: 400px;" multiple
                clearable>
                <el-option label="产品软件版本" value="SOFTWARE"></el-option>
                <el-option label="产品硬件版本" value="HARDWARE"></el-option>
                <el-option label="产品TP版本" value="TP"></el-option>
                <el-option label="产品TCON版本" value="TCON"></el-option>
                <el-option label="主机版本" value="HOST"></el-option>
                <el-option label="上位机版本" value="UPPRER_COMPUTER"></el-option>
                <el-option label="VideoSource版本" value="VIDEOSOURCE"></el-option>
                <el-option label="SimBox版本" value="SIMBOX"></el-option>
                <el-option label="PMIC版本" value="PMIC"></el-option>
            </el-select>
            <el-input v-model="form.version_number" placeholder="请输入实际版本号" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>

        </div>

        <el-table :data="tableData" stripe border style="width: 100%;flex: 1;" class="table-container">
            <!-- 所属项目列 -->
            <!-- <el-table-column prop="name" label="所属项目" min-width="300" align="center"></el-table-column> -->
            <el-table-column v-if="tableColumns[0].show" label="所属项目" width="300" align="center">
                <template #default="{ row }">
                    <span>{{ row.project_name }}({{ row.project_number }})</span>
                </template>
            </el-table-column>

            <!-- 版本类型列 -->
            <el-table-column v-if="tableColumns[1].show" prop="type_name" label="版本类型" min-width="150"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[2].show" prop="use_name" label="版本用途" min-width="100"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[3].show" prop="plan_version" label="计划版本号" min-width="150"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[4].show" prop="customer_version" label="客户版本号" min-width="150"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[5].show" prop="version_name" label="版本名称" min-width="200"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[6].show" label="状态" min-width="150" align="center">
                <template #default="{ row }">
                    <el-tag v-if="row.status == 'SUBMITTED'" type="success">已提测</el-tag>
                    <el-tag v-else-if="row.status == 'TESTING'" type="success">测试中</el-tag>
                    <el-tag v-else-if="row.status == 'SMOKE_TEST_PASSED'" type="success">冒烟测试通过</el-tag>
                    <el-tag v-else-if="row.status == 'SPECIAL_VERSION_RELEASED'" type="success">特殊版本已发布</el-tag>
                    <el-tag v-else-if="row.status == 'FAILED'" type="warning">测试不通过</el-tag>
                    <el-tag v-else-if="row.status == 'PASSED'" type="success">测试通过</el-tag>
                    <el-tag v-else-if="row.status == 'RELEASED'" type="primary">已发布</el-tag>
                    <el-tag v-else-if="row.status == 'PRODUCTION'" type="primary">已量产</el-tag>
                    <el-tag v-else-if="row.status == 'DEPRECATED'" type="danger">已废弃</el-tag>
                    <el-tag v-else type="danger">未知</el-tag>
                </template>
            </el-table-column>
            <el-table-column v-if="tableColumns[7].show" prop="publisher_name" label="发布人" min-width="100"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[8].show" prop="version_number" label="实际版本号" min-width="200"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[9].show" prop="publish_time" label="发布日期" min-width="200"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[10].show" prop="finish_time" label="完成日期" min-width="200"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[11].show" prop="duration" label="完成天数" min-width="100"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[12].show" label="测试计划" min-width="100" align="center">
                <template #default="{ row }">
                    <el-link :underline="false" type="primary" @click="handleTestPlan(row)">查看</el-link>
                </template>
            </el-table-column>

            <el-table-column v-if="tableColumns[13].show" label="测试报告" min-width="300" align="center">
                <template #default="{ row }">
                    <div class="tp">
                        <el-form label-width="170px">
                            <el-form-item label="QAC报告：">
                                <el-link :underline="false" type="primary" @click="openReport(row.qac_url)">查看</el-link>
                            </el-form-item>
                            <el-form-item label="Tessy报告：">
                                <el-link :underline="false" type="primary"
                                    @click="openReport(row.tessy_url)">查看</el-link>
                            </el-form-item>
                            <el-form-item label="测试计划报告：">
                                <el-link :underline="false" type="primary" @click="toTestReport(row)">查看</el-link>
                            </el-form-item>
                        </el-form>
                    </div>
                </template>
            </el-table-column>

            <el-table-column v-if="tableColumns[14].show" label="版本描述" min-width="500" align="center">
                <template #default="{ row }">
                    <el-scrollbar height="150px" style="text-align: left;">
                        <div v-html="row.desc" class="custom-html"></div>
                    </el-scrollbar>
                </template>
            </el-table-column>
            <el-table-column v-if="tableColumns[15].show" label="版本包地址" min-width="250" align="center">
                <template #default="{ row }">
                    <el-scrollbar height="60px" style="text-align: center;">
                        <a :href="row.version_package_address" target="_blank" style="color: #3370ff;">
                            {{ row.version_package_address }}
                        </a>
                    </el-scrollbar>
                </template>
            </el-table-column>
            <!-- 新增备注列 -->
            <el-table-column v-if="tableColumns[16].show" label="备注" min-width="200" align="center">
                <template #default="{ row }">
                    <el-scrollbar height="150px" style="text-align: left;">
                        <div class="custom-html" style="padding: 10px;">{{ row.remark }}</div>
                    </el-scrollbar>
                </template>
            </el-table-column>
            <el-table-column v-if="tableColumns[17].show" label="操作" min-width="200" fixed="right" align="center">
                <template #default="{ row }">
                    <div style="display: flex; justify-content: center; gap: 10px;">
                        <el-button type="primary" size="small" @click="handleUpdateStatus(row)">状态变更</el-button>
                        <el-button type="primary" size="small" @click="handleUpdateRemark(row)">备注编辑</el-button>
                    </div>
                </template>
            </el-table-column>

        </el-table>
    </div>


    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
            @change="onPageChange" />
    </div>

    <el-dialog v-if="dialogVisible" v-model="dialogVisible" title="状态变更" width="600px">
        <StatusUpdate :versionInfo="versionInfo" @cancel="dialogVisible = false" @confirm="onUpdateStatusConfirm" />
    </el-dialog>

    <el-dialog v-if="dialogRemarkUpdateVisible" v-model="dialogRemarkUpdateVisible" title="备注编辑" width="600px">
        <RemarkUpdate :remarkInfo="remarkInfo" @cancel="dialogRemarkUpdateVisible = false" @confirm="onUpdateRemarkConfirm" />
    </el-dialog>


</template>


<script setup>
import { ref, reactive, watch, onMounted, onActivated } from 'vue';
import http from '@/utils/http/http.js';
import { useProjectStore } from '@/stores/project.js';
import StatusUpdate from './status_update.vue';
import RemarkUpdate from './remark_update.vue';
import { useRouter, useRoute } from 'vue-router';
import { useTestPlanStore } from '@/stores/testPlan.js';
import { ElMessage } from 'element-plus';
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/product_versions/list', '产品版本列表');

const tableColumns = ref([
    { key: "project_name", name: "所属项目", show: true, disabled: true },
    { key: "type_name", name: "版本类型", show: true, disabled: true },
    { key: "use_name", name: "版本用途", show: true },
    { key: "plan_version", name: "计划版本号", show: true },
    { key: "customer_version", name: "客户版本号", show: true },
    { key: "version_name", name: "版本名称", show: true },
    { key: "status", name: "状态", show: true },
    { key: "publisher_name", name: "发布人", show: true },
    { key: "version_number", name: "实际版本号", show: true },
    { key: "publish_time", name: "发布日期", show: true },
    { key: "finish_time", name: "完成日期", show: true },
    { key: "duration", name: "完成天数", show: true },
    { key: "test_plan", name: "测试计划", show: true },
    { key: "test_report", name: "测试报告", show: true },
    { key: "desc", name: "版本描述", show: true },
    { key: "version_package_address", name: "版本包地址", show: true },
    { key: "remark", name: "备注", show: true },
    { key: "operation", name: "操作", show: true }
]);

// 设置弹窗的显示状态
const isSettingVisible = ref(false);

// 全选或取消全选逻辑
const selectAllColumn = (checked) => {
    tableColumns.value.forEach((column) => {
        if (!column.disabled) { // 跳过禁用的列
            column.show = checked;
        }
    });
};


// 重置列设置
const resetColumns = () => {
    tableColumns.value.forEach((column) => {
        if (!column.disabled) { // 跳过禁用的列
            column.show = true;
        }
    });
};

const dialogRemarkUpdateVisible = ref(false);
const remarkInfo = ref({});
let dialogVisible = ref(false);
let versionInfo = ref(null);
let projectStore = useProjectStore();
let testPlanStore = useTestPlanStore();
let filterCount = ref(0);
const tableData = ref([]);
const router = useRouter();
const route = useRoute();

let form = reactive({
    page: 1,
    pagesize: 10,
    project_number: '',
});

let total = ref(0);

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/product_versions', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'project_number'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function openReport(url) {
    if (url == null || url == '' || url == undefined) {
        ElMessage.error('无报告');
        return;
    }
    window.open(url);
};

function toTestReport(row) {
    router.push({ path: '/test_reports_v2', query: { version_name: row.version_name } });
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleRefresh() {
    update_table();
};

function handleUpdateStatus(row) {
    versionInfo.value = row;
    dialogVisible.value = true;
};

function handleUpdateRemark(row) {
    remarkInfo.value = {
        id: row.id,
        remark: row.remark,
    };
    dialogRemarkUpdateVisible.value = true;
};

function onUpdateRemarkConfirm() {
    dialogRemarkUpdateVisible.value = false;
    update_table();
};
    
function onUpdateStatusConfirm() {
    dialogVisible.value = false;
    update_table();
};

function handleTestPlan(row) {
    testPlanStore.setSoftwareVersion(row.version_name);
    router.push({ path: "/test_plans_v2" });
};

watch(() => projectStore.project_info, () => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

onMounted(() => {
    if (route.query.version_name) {
        form.version_name = route.query.version_name;
    }
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

onActivated(() => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

</script>


<style lang="scss" scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input,
    .el-select {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;

    .el-select {
        width: 500px;
    }

}

.el-aside {
    width: 200px;
    height: 100% !important;

    margin-top: 20px;
}

.el-main {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);

    margin-top: 0px;

    .table-container {
        flex: 1;
        overflow: auto;
    }
}


.custom-html {
    white-space: pre-wrap;

    a {
        color: #3370ff !important;
    }
}

.tp .el-form-item {
    margin: 0px;
}

.column-popper-title {
    border-bottom: 1px solid #ebeef5;
}

/* 自定义滚动条样式 */
.column-content::-webkit-scrollbar {
    width: 6px;
    /* 滚动条宽度 */
}

.column-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    /* 轨道背景色 */
    border-radius: 4px;
    /* 轨道圆角 */
}

/* 滚动条滑块默认样式（浅色） */
.column-content::-webkit-scrollbar-thumb {
    background: #e4e3e3;
    /* 浅色 */
    border-radius: 4px;
    /* 滑块圆角 */
}

/* 滚动条滑块悬停样式（深色） */
.column-content::-webkit-scrollbar-thumb:hover {
    background: #c7c7c7;
    /* 深色 */
}
</style>