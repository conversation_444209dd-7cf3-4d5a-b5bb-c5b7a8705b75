<template>
    <div class="c-container">
        <h2>{{ title }}</h2>
        <el-divider></el-divider>
        <el-form label-position="right" class="test-case-form">
            <el-form-item label="提交人：">
                <span>{{ submitter }}</span>
            </el-form-item>
            <el-form-item label="提交时间：">
                <span>{{ submit_time }}</span>
            </el-form-item>
            <el-form-item label="用例版本：">
                <span>{{ version }}</span>
            </el-form-item>
            <el-form-item label="用例总数：">
                <span>{{ case_total }}</span>
            </el-form-item>
        </el-form>

        <el-table :data="tableData" style="width: 100%" border height="100%" class="case-container"
            :show-overflow-tooltip="{ effect: 'dark', placement: 'bottom' }" stripe>
            <el-table-column type="expand" fixed>
                <template #default="props">
                    <div class="step-container">
                        <h3>步骤：</h3>
                        <el-table :data="props.row.steps" border height="250">
                            <el-table-column prop="cmd_type" label="指令类型" min-width="200" align="center"></el-table-column>
                            <el-table-column prop="cmd_data" label="指令内容" min-width="400" align="center"></el-table-column>
                            <el-table-column prop="cmd_period" label="周期" min-width="100" align="center"></el-table-column>
                            <el-table-column prop="cmd_expect" label="期望结果" min-width="200" align="center"></el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <el-table-column type="index" prop="index" label="序号" min-width="80" align="center"></el-table-column>
            <el-table-column prop="id" label="用例编号" min-width="120" align="center"></el-table-column>
            <el-table-column prop="module" label="用例模块" min-width="120" align="center"></el-table-column>
            <el-table-column prop="name" label="用例名称" min-width="200" align="center"></el-table-column>
            <el-table-column prop="version" label="用例版本" min-width="100" align="center"></el-table-column>
            <el-table-column prop="vision_algorithm" label="关联视觉算法" min-width="100" align="center"></el-table-column>
            <el-table-column prop="vision_revert" label="视觉检测结果取反" min-width="100" align="center"></el-table-column>
            <el-table-column prop="manual_decision" label="人工判定" min-width="100" align="center"></el-table-column>
            <el-table-column prop="describe" label="前置条件" min-width="300" align="center"></el-table-column>
            <el-table-column prop="cycle" label="执行次数" min-width="100" align="center"></el-table-column>
            <el-table-column prop="state" label="用例状态" min-width="120" align="center" fixed="right"></el-table-column>
        </el-table>

    </div>

</template>

<script setup>

import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import http from '@/utils/http/http.js';

const route = useRoute();

let title = ref('');
let submitter = ref('');
let submit_time = ref('');
let version = ref('');
let case_total = ref('');
let tableData = ref([]);

onMounted(() => {
    const id = route.params.id;
    http.get('/test_m/test_cases/' + id).then(res => {
        let data = res.data.data;
        title.value = `【${data.project_number}】${data.name}`;
        submitter.value = data.submitter;
        submit_time.value = data.update_time;
        version.value = data.version;

        http.get(data.tc_file, { baseURL: "http://hq.hwauto.com.cn:10011" }).then(res => {
            let parser = new DOMParser();
            let xmlDoc = parser.parseFromString(res.data, "text/xml");
            let cases = xmlDoc.getElementsByTagName("case");
            case_total.value = cases.length;
            for (let i = 0; i < cases.length; i++) {
                let state = cases[i].getAttribute("state");
                if (!state) {
                    state = "未评审";
                }
                let item = {
                    index: i + 1,
                    id: cases[i].getAttribute("id"),
                    module: cases[i].getAttribute("module"),
                    name: cases[i].getElementsByTagName("option")[0].getAttribute("name"),
                    version: cases[i].getAttribute("version"),
                    state,
                    vision_algorithm: cases[i].getAttribute("vision_algorithm"),
                    vision_revert: cases[i].getAttribute("vision_revert"),
                    manual_decision: cases[i].getAttribute("manual_decision"),
                    describe: cases[i].getAttribute("describe"),
                    cycle: cases[i].getAttribute("cycle"),
                };
                item.steps = [];
                let steps = cases[i].getElementsByTagName("step");
                for (let j = 0; j < steps.length; j++) {
                    let cmd_type = steps[j].textContent;
                    let cmd_data = '';
                    let cmd_period = '';
                    let cmd_expect = '';
                    if (cmd_type === "CAN") {
                        cmd_data = JSON.stringify({
                            can_type: steps[j].getAttribute("can_type"),
                            cycle_period: steps[j].getAttribute("cycle_period"),
                            id: steps[j].getAttribute("id"),
                            msg: steps[j].getAttribute("msg"),
                            uds: steps[j].getAttribute("recv_id") + ' ' + steps[j].getAttribute("expect"),
                        });
                        cmd_period = steps[j].getAttribute("cycle_period");
                        cmd_expect = steps[j].getAttribute("expect");
                    } else if (cmd_type === "I2C") {
                        cmd_data = JSON.stringify({
                            cmd: steps[j].getAttribute("cmd"),
                            option: steps[j].getAttribute("option"),
                            min: steps[j].getAttribute("min"),
                            max: steps[j].getAttribute("max"),
                        });
                        cmd_expect = steps[j].getAttribute("expect");
                    } else if (cmd_type === "CustomizeCMD") {
                        cmd_data = steps[j].getAttribute("CustomizeCMD") + " " + steps[j].getAttribute("data");
                        cmd_expect = steps[j].getAttribute("expect");
                    } else if (cmd_type === "LIN") {

                    }

                    let step = {
                        cmd_type,
                        cmd_data,
                        cmd_period,
                        cmd_expect,
                    };
                    item.steps.push(step);
                }
                tableData.value.push(item);
            }

        });
    });
});


</script>


<style lang="scss" scope>
.c-container {
    height: calc(100vh - 100px);
    display: flex;
    flex-direction: column;

}

.case-container {
    flex: auto;
}

.step-container {
    padding: 20px;
    background-color: #f5f5f5;
}

.el-popper {
    font-size: 14px;
    max-width: 300px;
    padding: 6px 12px;
}

.test-case-form {
    padding: 0 20px;

    .el-form-item {
        margin-bottom: 0;
    }
}
</style>