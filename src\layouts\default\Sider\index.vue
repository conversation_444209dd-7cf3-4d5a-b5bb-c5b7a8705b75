<template>
    <div class="sider-c">
        <div class="sider-logo-c">
            <el-image style="width: 22px; height: 22px;" :src="logoImage"></el-image>
            <span style="color: #409eff;font-weight: bolder;margin-left: 10px;" v-if="!sider.isCollapse">研发测试管理平台</span>
        </div>

        <el-scrollbar style="width: 100%">
            <el-menu @select="onMenuSelect" class="el-menu-vertical" :collapse="sider.isCollapse"
                :text-color="'#2a2a2ab3'" :collapse-transition="false" :default-active="sider.activeIndex"
                mode="vertical" style="width: 100%"
                unique-opened   
                >
                <sider-item v-for="(i, index) in menu" :key="index.toString()" :route="i" :index="index.toString()" />
            </el-menu>
        </el-scrollbar>

        <div class="hamburger-container" @click="toggleSider">
            <svg class="hamburger" :class="{ 'is-active': !sider.isCollapse }" viewBox="0 0 1024 1024"
                xmlns="http://www.w3.org/2000/svg" width="64" height="64">
                <path
                    d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 0 0 0-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0 0 14.4 7z" />
            </svg>
        </div>
    </div>
</template>

<script setup>
// import { watch } from 'vue';
import menu from '@/api/menu.json';
import { useSiderStore } from '@/stores/sider.js';
import logoImage from '@/assets/icons/20240625-174127.png';
import SiderItem from './SiderItem.vue';
// import { useRoute } from 'vue-router';

if (import.meta.env.DEV) {
    menu.push({
        "title": "调试",
        "path": "/test",
        "icon": {
            "name": "codicon:debug-console",
            "source": "iconify"
        }
    });
}

// const route = useRoute();
const sider = useSiderStore();

function onMenuSelect(index) {
    sider.setActiveIndex2(null);
    sider.setActiveIndex(index);
}

function toggleSider() {
    sider.toggle();
};

// function getItems(tar, source, suffix) {
//   source.forEach((item, index) => {
//     if (item.children && item.children.length > 0) {
//       getItems(tar, item.children, suffix + index.toString() + '-');
//     } else {
//       tar.push({
//         index: suffix + index.toString(),
//         route: item
//       });
//     }
//   });
// };

// const menu2 = [];
// getItems(menu2, menu, '');

</script>
<style lang="scss" scope>
div.sider-logo-c {
    height: 55px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;

    cursor: pointer;
}

div.sider-c {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.hamburger-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 60px;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
        background: rgba(0, 0, 0, .025)
    }

    padding-left: 13px;
    margin-bottom: 20px;

    border-top: 2px solid #ebeef5;
}

.hamburger {
    width: 35px;
    height: 35px;
}

.hamburger.is-active {
    transform: rotate(180deg);
}

</style>
