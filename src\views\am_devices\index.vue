<template>
    <div class="devices-container">
        <!-- 顶部工具栏 -->
        <div class="toolbar">
            <div class="view-selector">
                <el-radio-group v-model="currentView">
                    <el-radio-button label="基础视图" value="table" />
                    <el-radio-button label="工位视图" value="station" />
                </el-radio-group>
            </div>
        </div>

        <!-- 内容区域 - 动态组件切换不同视图 -->
        <div class="content-view">
            <component :is="currentComponent" />
        </div>
    </div>
</template>

<script setup>
import { ref, computed} from 'vue';
import BaseList from './baseList.vue';
import StationList from './stationList.vue';
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/am_devices/list', '实验设备列表');

// 当前视图选择
const currentView = ref('station');

// 基于当前视图选择返回对应组件
const currentComponent = computed(() => {
    switch (currentView.value) {
        case 'station':
            return StationList;
        case 'table':
        default:
            return BaseList;
    }
});

</script>

<style lang="scss" scoped>
.devices-container {
    display: flex;
    flex-direction: column;
    height: 100%;

    .toolbar {
        display: flex;
        justify-content: end;
        align-items: center;
        margin-bottom: 5px;

        .view-selector {
            // 视图选择器样式
        }
    }

    .content-view {
        flex: 1;
        overflow: hidden;
        padding: 10px;
    }
}
</style>