<template>
    <div>
        <el-form :model="form" label-width="auto" status-icon ref="formRef" style="padding: 20px;">
            <el-form-item label="测试机台" prop="machine_number">
                <el-select v-model="form.machine_number" placeholder="请选择测试机台" filterable>
                    <el-option v-for="item in machines" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end;">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="confirm">确 定</el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http/http.js';

const props = defineProps({
    test_case_ids: {
        type: Array,
        default: []
    }
});
const emit = defineEmits(['confirm', 'cancel']);

const machines = ref([]);

const form = ref({
    test_case_ids: props.test_case_ids,
    machine_number: ''
});

const cancel = () => {
    emit('cancel');
}

const confirm = () => {
    emit('confirm', form.value);
}

onMounted(() => {

    http.get('/machines', { params: { pagesize: 10000 } }).then(res => {
        let data = res.data.data.results;
        machines.value = data.map(item => {
            return { label: item.name + "(" + item.m_number + ")", value: item.m_number };
        });
    });

})
</script>


<style scoped></style>