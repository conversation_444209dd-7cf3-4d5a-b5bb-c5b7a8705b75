<template>
    <el-form :model="form" :rules="rules" ref="formRef">
        <el-row :gutter="10">
            <el-col :span="8">
                <el-form-item label="发送ID" prop="send_id">
                    <el-input v-model="form.send_id"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="16">
                <el-form-item label="发送报文" prop="send_msg">
                    <el-input v-model="form.send_msg"></el-input>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row :gutter="10">
            <el-col :span="12">
                <el-form-item label="检查数字类型" prop="check_number_type">
                    <el-select v-model="form.check_number_type" placeholder="请选择检查数字类型">
                        <el-option label="Enhanced" value="Enhanced"></el-option>
                        <el-option label="Classic" value="Classic"></el-option>
                        <el-option label="Automatic" value="Automatic"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="方向" prop="direction">
                    <el-select v-model="form.direction" placeholder="请选择方向">
                        <el-option label="Publisher" value="Publisher"></el-option>
                        <el-option label="Disabled" value="Disabled"></el-option>
                        <el-option label="Subscriber" value="Subscriber"></el-option>
                        <el-option label="Subscriber Automatic Length" value="Subscriber Automatic Length"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row :gutter="10">
            <el-col :span="10">
                <el-form-item label="循环周期(ms)" prop="period">
                    <el-input-number v-model="form.period" :min="0" class="full-width"></el-input-number>
                </el-form-item>
            </el-col>
            <el-col :span="7">
                <el-form-item label="通道" prop="channel">
                    <el-select v-model="form.channel" placeholder="请选择通道">
                        <el-option label="通道1" value="1"></el-option>
                        <el-option label="通道2" value="2"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="7">
                <el-form-item label="模式" prop="mode">
                    <el-select v-model="form.mode" placeholder="请选择模式">
                        <el-option label="Master" value="Master"></el-option>
                        <el-option label="Slave" value="Slave"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
</template>


<script setup>

import { ref, watch, onMounted } from 'vue';

const props = defineProps({
    initData: {
        type: Object,
        default: () => ({}),
    },
});

const model = defineModel();

const formRef = ref(null);

const form = ref({
    send_id: '',
    send_msg: '',
    period: 0,
    channel: '1',
    mode: 'Mater',
    check_number_type: 'Enhanced',
    direction: 'Publisher',
});

const rules = ref({
    send_id: [
        { required: true, message: '请输入发送ID', trigger: 'blur' },
    ],
    send_msg: [
        { required: true, message: '请输入发送报文', trigger: 'blur' },
    ],
    period: [
        { required: true, message: '请输入循环周期', trigger: 'blur' },
    ],
    channel: [
        { required: true, message: '请选择通道', trigger: 'blur' },
    ],
    mode: [
        { required: true, message: '请选择模式', trigger: 'blur' },
    ],
    check_number_type: [
        { required: true, message: '请选择检查数字类型', trigger: 'blur' },
    ],
    direction: [
        { required: true, message: '请选择方向', trigger: 'blur' },
    ],
    
});

watch(form, () => {
    model.value = form.value;
}, { immediate: true });

watch(() => props.initData, (val) => {
    Object.assign(form.value, val);
}, { immediate: true });


const validate = (callback) => {
    formRef.value.validate(callback);
};

defineExpose({
    validate,
});

</script>

<style scoped>
.full-width {
    width: 100%;
}
</style>