<template>
    <div style="display: flex;flex-direction: column;height: calc(100vh - 160px);">
        <div class="tool-bar-container">
            <!-- <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button> -->
            <div style="margin-left: auto; display: flex; gap: 10px;">
                <div>
                    <el-button icon="Setting" text bg @click="isSettingVisible = true">设置</el-button>
                      <!-- 设置弹窗 -->
                            <el-popover
                            v-model:visible="isSettingVisible"
                            width="180"
                            trigger="manual"
                            placement="bottom"
                            >
                            <template #reference>
                            <!-- 设置按钮作为触发器 -->
                            <div style="display: inline-block;"></div>
                            </template>
                            <!-- 操作按钮 -->
                            <div class="column-popper-title">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                        <el-checkbox
                        :model-value="tableColumns.every(item => item.show)"
                            :indeterminate="tableColumns.some(item => item.show) && tableColumns.some(item => !item.show)"
                            label="列展示"
                                @change="selectAllColumn"
                            />
                        <el-button text @click="resetColumns" style="margin-right: -10px;">重置</el-button>
                        </div>
                        </div>
                        <!-- 列设置内容 -->
                            <div class="column-content" style="max-height: 200px; overflow-y: auto;">
                            <div class="column-item" v-for="column in tableColumns" :key="column.key">
                            <el-checkbox v-model="column.show" :label="column.name"  :disabled="column.disabled"></el-checkbox>
                            </div>
                        </div>
                        </el-popover>
                        
                        </div>
                <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                    <el-button text bg @click="handleReset">重置</el-button>
                </el-tooltip>
                <filterButton @click="onFilterStatusChange" :count="filterCount" />
                <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
            </div>
        </div>

        <div class="filter-container" v-if="showFilterContainer">
            <el-input v-model="form.name_re" placeholder="请输入用例名称" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
            <el-input v-model="form.id_re" placeholder="请输入用例编号" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
        </div>


        <el-table :data="tableData" stripe border style="width: 100%; flex: 1;">

<!-- 测试类别列 -->
<el-table-column v-if="tableColumns[0].show" label="测试类别" min-width="200" align="center">
    <template #default="{ row }">
        <div style="white-space: pre-wrap;">{{ row.test_category }}</div>
    </template>
</el-table-column>

<!-- 测试用例编号列 -->
<el-table-column v-if="tableColumns[1].show" prop="id" label="测试用例编号" min-width="200" align="center"></el-table-column>

<!-- 测试项目名称列 -->
<el-table-column v-if="tableColumns[2].show" label="测试项目名称" min-width="300" align="center">
    <template #default="{ row }">
        <div style="white-space: pre-wrap;">{{ row.name }}</div>
    </template>
</el-table-column>

<!-- 测试步骤列 -->
<el-table-column v-if="tableColumns[3].show" label="测试步骤" min-width="500" align="center">
    <template #default="{ row }">
        <div style="white-space: pre-wrap;">{{ row.test_steps }}</div>
    </template>
</el-table-column>

<!-- 期望结果列 -->
<el-table-column v-if="tableColumns[4].show" label="期望结果" min-width="500" align="center">
    <template #default="{ row }">
        <div style="white-space: pre-wrap;">{{ row.expected_result }}</div>
    </template>
</el-table-column>

<!-- 优先级列 -->
<el-table-column v-if="tableColumns[5].show" label="优先级" min-width="100" align="center">
    <template #default="{ row }">
        <div style="white-space: pre-wrap;">{{ row.priority }}</div>
    </template>
</el-table-column>

<!-- 应用软件迭代列 -->
<el-table-column v-if="tableColumns[6].show" label="应用软件迭代" min-width="120" align="center">
    <template #default="{ row }">
        <div style="white-space: pre-wrap;">{{ row.software_iteration }}</div>
    </template>
</el-table-column>

<!-- 配置变化列 -->
<el-table-column v-if="tableColumns[7].show" label="配置变化" min-width="100" align="center">
    <template #default="{ row }">
        <div style="white-space: pre-wrap;">{{ row.config_changes }}</div>
    </template>
</el-table-column>

<!-- Boot迭代列 -->
<el-table-column v-if="tableColumns[8].show" label="Boot迭代" min-width="100" align="center">
    <template #default="{ row }">
        <div style="white-space: pre-wrap;">{{ row.boot_iteration }}</div>
    </template>
</el-table-column>

<!-- 硬件迭代列 -->
<el-table-column v-if="tableColumns[9].show" label="硬件迭代" min-width="100" align="center">
    <template #default="{ row }">
        <div style="white-space: pre-wrap;">{{ row.hardware_iteration }}</div>
    </template>
</el-table-column>

<!-- 测试结果列 -->
<el-table-column v-if="tableColumns[10].show" label="测试结果" min-width="100" align="center">
    <template #default="{ row }">
        <div style="white-space: pre-wrap;">{{ row.test_result }}</div>
    </template>
</el-table-column>

<!-- 测试数据列 -->
<el-table-column v-if="tableColumns[11].show" label="测试数据" min-width="100" align="center">
    <template #default="{ row }">
        <div style="white-space: pre-wrap;">{{ row.test_data }}</div>
    </template>
</el-table-column>

<!-- 备注列 -->
<el-table-column v-if="tableColumns[12].show" label="备注" min-width="100" align="center">
    <template #default="{ row }">
        <div style="white-space: pre-wrap;">{{ row.remarks }}</div>
    </template>
</el-table-column>

<!-- 操作列 -->
<el-table-column v-if="tableColumns[13].show" label="操作" min-width="150" fixed="right" align="center">
    <template #default="{ row }">
        <div style="display: flex; justify-content: center; gap: 10px;">
        
        </div>
    </template>
</el-table-column>

</el-table>


        <div class="pagination-container">
            <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
                v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
                @change="onPageChange" />
        </div>
    </div>

</template>


<script setup>
import { ref, reactive, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import filterButton from '@/components/filterButton.vue';

const tableColumns = ref([
  { key: "test_category", name: "测试类别", show: true, disabled: true },
  { key: "id", name: "测试用例编号", show: true, disabled: true },
  { key: "name", name: "测试项目名称", show: true },
  { key: "test_steps", name: "测试步骤", show: true },
  { key: "expected_result", name: "期望结果", show: true },
  { key: "priority", name: "优先级", show: true },
  { key: "software_iteration", name: "应用软件迭代", show: true },
  { key: "config_changes", name: "配置变化", show: true },
  { key: "boot_iteration", name: "Boot迭代", show: true },
  { key: "hardware_iteration", name: "硬件迭代", show: true },
  { key: "test_result", name: "测试结果", show: true },
  { key: "test_data", name: "测试数据", show: true },
  { key: "remarks", name: "备注", show: true },
  { key: "operation", name: "操作", show: true }
]);


// 设置弹窗的显示状态
const isSettingVisible = ref(false);

// 全选或取消全选逻辑
const selectAllColumn = (checked) => {
  tableColumns.value.forEach((column) => {
    if (!column.disabled) { // 跳过禁用的列
      column.show = checked;
    }
  });
};


// 重置列设置
const resetColumns = () => {
  tableColumns.value.forEach((column) => {
    if (!column.disabled) { // 跳过禁用的列
      column.show = true;
    }
  });
};

const tableData = ref([]);

const dialogAddVisible = ref(false);
const dialogEditVisible = ref(false);
const r_id = ref(0);

let form = reactive({
    name: '',
    number: '',
});

let total = ref(0);
const filterCount = ref(0);
let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/es_test_cases/nio', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleAdd() {
    dialogAddVisible.value = true;
};

function handleEdit(row) {
    r_id.value = row.id;
    dialogEditVisible.value = true;
};

function handleDelete(row) {
    ElMessageBox.confirm(
        '确定删除吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.delete(`/test_case_tags/${row.id}`).then(res => {
            ElMessage({
                message: '删除成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessage({
                type: 'error',
                message: err.response.data.msg
            });
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};

function onAddSuccess() {
    dialogAddVisible.value = false;
    update_table();
};

function onEditSuccess() {
    dialogEditVisible.value = false;
    update_table();
};

function handleRefresh() {
    update_table();
};

onMounted(() => {
    update_table();
});

</script>


<style scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    display: flex;
    justify-content: flex-start;
}

.column-popper-title {
  border-bottom: 1px solid #ebeef5;
}

/* 自定义滚动条样式 */
.column-content::-webkit-scrollbar {
  width: 6px; /* 滚动条宽度 */
}

.column-content::-webkit-scrollbar-track {
  background: #f1f1f1; /* 轨道背景色 */
  border-radius: 4px; /* 轨道圆角 */
}

/* 滚动条滑块默认样式（浅色） */
.column-content::-webkit-scrollbar-thumb {
  background: #e4e3e3; /* 浅色 */
  border-radius: 4px; /* 滑块圆角 */
}

/* 滚动条滑块悬停样式（深色） */
.column-content::-webkit-scrollbar-thumb:hover {
  background: #c7c7c7; /* 深色 */
}
</style>