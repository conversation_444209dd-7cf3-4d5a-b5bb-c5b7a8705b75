<template>
    <div style="display: flex; flex-direction: column; height: calc(100vh - 150px);">

    

        <div class="tool-bar-container">
            <el-button icon="Plus" type="primary" @click="handleAdd">新增模块</el-button>
            <div style="margin-left: auto; display: flex; gap: 10px;">
                <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
            </div>
            <router-link to="/functions2/list_view">
                <el-button  class="mind_map" key="plain" text bg >思维导图</el-button>
            </router-link>
        </div>


        <div class="filter-container" v-if="showFilterContainer">
            <el-input v-model="form.name" size="large" placeholder="请输入名称" suffix-icon="Search"
                @keyup.enter="onFilter"></el-input>
            <el-input v-model="form.number" size="large" placeholder="请输入编号" suffix-icon="Search"
                @keyup.enter="onFilter"></el-input>
        </div>


        <el-table :data="tableData" stripe border row-key="id" style="width: 100%; table-layout: fixed;">

<!-- 名称列 -->
<el-table-column prop="name" label="名称" min-width="200" align="left"></el-table-column>

<!-- 编号列 -->
<el-table-column prop="number" label="编号" min-width="200" align="left"></el-table-column>

<!-- 描述列 -->
<el-table-column prop="desc" label="描述" min-width="500" align="left"></el-table-column>

<!-- 操作列 -->
<el-table-column label="操作" min-width="290" fixed="right" align="center">
    <template #default="{ row }">
        <div style="display: flex; justify-content: center; gap: 10px;">
            <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="primary" size="small" @click="handleAdd2(row)">新增子模块</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
            <el-button type="danger" size="small" @click="handleDeprecate(row)">弃用</el-button>
        </div>
    </template>
</el-table-column>

</el-table>

        <el-dialog v-if="dialogAddVisible" v-model="dialogAddVisible" title="添加模块" width="800"
            :close-on-click-modal="false">
            <Add @confirm="onAddConfirm" @cancel="onAddCancel" :parent="parent" />
        </el-dialog>

        <el-dialog v-if="dialogEditVisible" v-model="dialogEditVisible" title="编辑模块" width="800"
            :close-on-click-modal="false">
            <Edit @affirm="onEditAffirm" @cancel="onEditCancel" :r_id="r_id" />
        </el-dialog>
    </div>

</template>


<script setup>
import { ref, reactive, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import Add from './add.vue';
import Edit from './edit.vue';
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/functions2/list', '模块列表');

const tableData = ref([]);

let r_id = ref(0);

const dialogAddVisible = ref(false);

const dialogEditVisible = ref(false);

const parent = ref(null);

let form = reactive({
    name: '',
    number: '',
});

let showFilterContainer = ref(false);

function filterItems(items) {
    items = items.filter(item => item.deprecated === false);
    
    items.forEach(item => {
        if (item.children && item.children.length > 0) {
            item.children = filterItems(item.children);
        }
    });

    return items;
};

function update_table() {
    http.get('/functions', { params: form }).then(res => {
        let items = res.data.data.results;
        items = filterItems(items);
        tableData.value = items;
    });
};

function onFilter() {
    update_table();
};

function handleAdd() {
    parent.value = null;
    dialogAddVisible.value = true;
};

function handleAdd2(row) {
    parent.value = row;
    dialogAddVisible.value = true;
};

function handleEdit(row) {
    r_id.value = row.id;
    dialogEditVisible.value = true;
};

function handleDelete(row) {
    ElMessageBox.confirm(
        `确定删除${row.name}模块吗?`,
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.delete(`/functions/${row.id}`).then(res => {
            ElMessage({
                message: '删除成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessageBox.alert(
                err.response.data.msg,
                '警告',
                {
                    confirmButtonText: '确定',
                    type: 'warning',
                }
            )
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};

function handleDeprecate(row) {
    ElMessageBox.confirm(
        `确定弃用${row.name}模块吗?`,
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.put(`/functions/${row.id}/deprecate`).then(res => {
            ElMessage({
                message: '弃用成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessageBox.alert(
                err.response.data.msg,
                '警告',
                {
                    confirmButtonText: '确定',
                    type: 'warning',
                }
            )
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消弃用'
        });
    });
};

function onAddConfirm() {
    dialogAddVisible.value = false;
    update_table();
};

function onAddCancel() {
    dialogAddVisible.value = false;
};

function onEditAffirm() {
    dialogEditVisible.value = false;
    update_table();
};

function onEditCancel() {
    dialogEditVisible.value = false;
};

function handleRefresh() {
    update_table();
};

onMounted(() => {
    update_table();
});

</script>


<style scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    display: flex;
    justify-content: flex-start;
    
}

.mind_map{
    margin-left: 10px;
   
    
}

</style>