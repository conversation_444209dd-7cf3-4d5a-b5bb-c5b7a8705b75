<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="程序名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入机台名称"></el-input>
            </el-form-item>

            <el-form-item label="程序编号" prop="number">
                <el-input v-model="form.number" placeholder="请输入机台编号" readonly></el-input>
            </el-form-item>

            <el-form-item label="程序类型" prop="type">
                <el-select v-model="form.type" placeholder="请选择程序类型">
                    <el-option label="类型0" value="0"></el-option>
                    <el-option label="类型1" value="1"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="程序图标" prop="icon">
                <el-upload class="upload-demo" action="" :auto-upload="false" :file-list="iconList"
                    :on-change="handleFileChange('icon')" :before-upload="beforeUpload" accept=".jpg, .svg, .png">
                    <el-button slot="trigger" type="primary" plain>选择图标</el-button>
                    <div slot="tip" class="el-upload__tip">只能上传一个文件(.jpg, .svg, .png)</div>
                </el-upload>
                <el-input style="display: none;"></el-input>
            </el-form-item>

            <el-form-item label="额外参数">
                <AddExtraArgs v-model="form.extra_args" />
            </el-form-item>

            <el-form-item label="程序描述">
                <el-input v-model="form.desc" placeholder="请输入机台描述"></el-input>
            </el-form-item>

            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onConfirm">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import http from '@/utils/http/http.js';
import AddExtraArgs from './addExtraArgs.vue';


const props = defineProps({
    r_id: {
        type: Number,
        required: true,
    },
});

const emit = defineEmits(['confirm', 'cancel'])

const formRef = ref(null);

const form = ref({
    name: '',
    number: '',
    desc: '',
    type: '0',
    icon: null,
    extra_args: [['', '']],
});

const rules = ref({
    name: [
        { required: true, message: '请输入程序名称', trigger: 'blur' },
    ],
    number: [
        { required: true, message: '请输入程序编号', trigger: 'blur' },
    ],
    type: [
        { required: true, message: '请选择程序类型', trigger: 'change' },
    ],
});

const handleFileChange = (field) => (file) => {
    form.value[field] = file.raw;
    if (field === 'icon') {
        iconList.value = [file];
    }
};

const beforeUpload = (file) => {
    return false; // 阻止自动上传
};

const onConfirm = () => {
    formRef.value.validate(async (valid) => {
        if (valid) { 
            let extra_args = form.value.extra_args.reduce((acc, [key, value]) => {
                if (key) acc[key] = value;
                return acc;
            }, {});

            const formData = new FormData();
            formData.append('name', form.value.name);
            formData.append('number', form.value.number);
            formData.append('desc', form.value.desc);
            formData.append('type', form.value.type);
            if (form.value.icon) {
                formData.append('icon', form.value.icon);
            }
            formData.append('extra_args', JSON.stringify(extra_args));

            http.put(`/programs/${props.r_id}`, formData).then(res => {
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '编辑失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

function onCancel() {
    emit('cancel');
};

onMounted(() => { 
    if (props.r_id) {
        http.get(`/programs/${props.r_id}`).then(res => {
            form.value.name = res.data.data.name;
            form.value.number = res.data.data.number;
            form.value.desc = res.data.data.desc;
            form.value.type = res.data.data.type.toString();
            form.value.extra_args = Object.entries(res.data.data.extra_args);
        });
    };
});

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>