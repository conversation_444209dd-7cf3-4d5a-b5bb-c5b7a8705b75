<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" :rules="rules" label-width="auto" status-icon ref="formRef">

            <el-form-item label="步骤类型" prop="type">
                <el-select v-model="form.type" placeholder="请选择步骤类型">
                    <el-option v-if="props.type == 'MANUAL_EXECUTION'" label="手动执行" value="MANUAL"></el-option>
                    <template v-else>
                        <el-option label="CAN报文" value="CAN"></el-option>
                        <el-option label="LIN报文" value="LIN"></el-option>
                        <el-option label="I2C指令" value="I2C"></el-option>
                        <el-option label="自定义指令 [备注：非CAN报文、LIN报文、I2C指令]" value="CUSTOM_CMD"></el-option>
                    </template>
                </el-select>
            </el-form-item>

            <StepParams :type="form.type" v-model="form.params" ref="stepParamsRef" />

            <el-form-item label="步骤描述" prop="desc">
                <el-input type="textarea" v-model="form.desc" :rows="4"></el-input>
            </el-form-item>

            <el-form-item label="期望结果" prop="expectation">
                <el-input type="textarea" v-model="form.expectation" :rows="4"></el-input>
            </el-form-item>

            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

import StepParams from './stepParams.vue';

const formRef = ref(null);

const stepParamsRef = ref(null);

const props = defineProps({
    type: String,
    index: {
        type: Number,
        default: -1,
    }
});

const form = ref({
    type: props.type == 'MANUAL_EXECUTION' ? 'MANUAL' : '',
    params: {},
    desc: '',
    expectation: '',
});

const rules = ref({
    type: [
        { required: true, message: '请输入步骤类型', trigger: 'blur' },
    ],
    desc: [
        { required: true, message: '请输入步骤描述', trigger: 'blur' },
    ],
    expectation: [
        { required: true, message: '请输入期望结果', trigger: 'blur' },
    ],
});

const emit = defineEmits(['confirm', 'cancel']);

const onSubmit = () => {
    formRef.value.validate((valid) => {
        if (valid) {
            if (props.type == 'MANUAL_EXECUTION') {
                emit('confirm', form.value, props.index);
            } else {
                stepParamsRef.value.validate((valid) => {
                    if (valid) {
                        emit('confirm', form.value, props.index);
                    }
                });
            }
        }
    });
};

function onCancel() {
    emit('cancel');
};

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>