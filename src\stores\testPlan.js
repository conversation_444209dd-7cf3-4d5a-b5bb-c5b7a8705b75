import { defineStore } from 'pinia'

export const useTestPlanStore = defineStore('testPlan', {
    state: () => ({
        softwareVersion: null,
    }),
    actions: {
        setSoftwareVersion(softwareVersion) {
            this.softwareVersion = softwareVersion
        },
        clearSoftwareVersion() {
            this.softwareVersion = null
        },
    },
    persist: {
        key: 'testPlan',
        storage: sessionStorage
      }
  })