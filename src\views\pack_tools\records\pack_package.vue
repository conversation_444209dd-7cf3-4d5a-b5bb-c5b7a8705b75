<template>
    <div class="container">
        <template v-if="PackageInfo">
            <el-form label-width="auto" class="form">
                <el-form-item label="项目:">
                    <span>{{ PackageInfo.project_name }}</span>
                    <el-input style="display: none"></el-input>
                </el-form-item>
                <el-form-item label="升级包:">
                    <span>{{ PackageInfo.output_file_name }}</span>
                    <el-input style="display: none"></el-input>
                </el-form-item>
                <el-form-item label="升级包MD5:">
                    <span>{{ PackageInfo.output_file_md5 }}</span>
                    <el-input style="display: none"></el-input>
                </el-form-item>
                <el-form-item label="软件版本：">
                    <span>{{ PackageInfo.software_version }}</span>
                    <el-input style="display: none"></el-input>
                </el-form-item>
                <el-form-item label="显示与触摸屏版本:">
                    <span>{{ PackageInfo.screen_touchpad_version }}</span>
                    <el-input style="display: none"></el-input>
                </el-form-item>
                <el-form-item label="打包软件版本:">
                    <span>{{ PackageInfo.packer_version }}</span>
                    <el-input style="display: none"></el-input>
                </el-form-item>
                <el-form-item label="操作人:">
                    <span>{{ PackageInfo.operator_name }}</span>
                    <el-input style="display: none"></el-input>
                </el-form-item>
                <el-form-item label="验证状态:">
                    <span>{{ PackageInfo.package_status ? '验证通过' : '验证不通过' }}</span>
                    <el-input style="display: none"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" plain @click="handleDownload"
                        style="width: 100%;margin-top: 10px;">下载</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template v-else>
            <h2 style="margin-top: 40px;">链接已失效</h2>
        </template>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { useRoute } from 'vue-router';

const route = useRoute();

const PackageInfo = ref(null);

function handleDownload() {
    window.open(import.meta.env.VITE_BASE_URL + '/pack_tool/records/share_urls/' + route.params.token + '/download');
}

onMounted(() => {
    axios.get(import.meta.env.VITE_BASE_URL + '/pack_tool/records/share_urls/' + route.params.token).then(res => {
        PackageInfo.value = res.data.data;
    }).catch(err => {
        console.log(err);
    });
});

</script>

<style lang="scss" scoped>
.container {
    display: flex;
    justify-content: center;
    height: 100vh;
    background-color: #f5f5f5;
}

.form {
    padding: 20px;
    width: 400px;
}

:deep(.el-form-item) {
    margin: 0;
}
</style>