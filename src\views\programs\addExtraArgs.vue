<template>

    <div style="width: 100%;">
        <div v-for="(v, index) in model" class="recv_msg_item">
            <el-row :gutter="10" style="width: 100%;">
                <el-col :span="10">
                    <el-input v-model="model[index][0]">
                    </el-input>
                </el-col>
                <el-col :span="12">
                    <el-input v-model="model[index][1]">
                    </el-input>
                </el-col>
                <el-col :span="2">
                    <el-button type="danger" plain @click="handleDelete(index)" icon="Minus">
                    </el-button>
                </el-col>
            </el-row>
        </div>
        <el-button type="primary" plain @click="handleAdd" icon="Plus">添加参数</el-button>
    </div>

</template>

<script setup>
const model = defineModel();

function handleDelete(index) {
    if (model.value.length === 1) {
        return
    }
    model.value.splice(index, 1)
}

function handleAdd() {
    model.value.push(['', ''])
}

</script>

<style scoped>
.recv_msg_item {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.recv_msg_item:not(:last-child) {
    margin-bottom: 10px;
}
</style>