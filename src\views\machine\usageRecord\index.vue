<template>
    <!-- <div class="search-container">
        <el-input placeholder="请输入搜索内容" class="search-input">
            <template #append>
                <el-button icon="Search" @click="onSearch" class="search-button">搜索</el-button>
            </template>
        </el-input>
    </div> -->

    <div class="tool-bar-container">
        <el-button type="info" plain @click="onFilterStatusChange" >
            筛选<el-icon class="el-icon--right" ><component :is="filterButtonIcon"></component></el-icon>
        </el-button>
        <el-button icon="Refresh" type="info" plain @click="handleRefresh">刷新</el-button>
    </div>

    <div class="filter-container" v-if="showFilterContainer">
        <el-input v-model="form.name" size="large" placeholder="请输入名称" suffix-icon="Search" @keyup.enter="onFilter"></el-input>
        <el-input v-model="form.number" size="large" placeholder="请输入编号" suffix-icon="Search" @keyup.enter="onFilter"></el-input>
    </div>

    <div class="table-container">
        <el-table :data="tableData" stripe border style="width: 100%">

            <el-table-column prop="machine_name" label="机台名称" width="200" align="center"></el-table-column>
            <el-table-column prop="user" label="使用人" width="120" align="center"></el-table-column>
            <el-table-column prop="start_time" label="开始时间" width="120" align="center"></el-table-column>
            <el-table-column prop="end_time" label="结束时间" width="120" align="center"></el-table-column>
            <el-table-column prop="project" label="项目" width="120" align="center"></el-table-column>
            <el-table-column prop="content" label="测试内容" width="300" align="center"></el-table-column>
            
        </el-table>
    </div>

     <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize"
            :total="total" background @change="onFilter" />
     </div>

</template>


<script setup>

import { ref, reactive, onMounted } from 'vue';
import http from '@/utils/http/http.js';

const tableData = ref([]);

let form = reactive({
    name: '',
    number: '',
    page: 1,
    pagesize: 10
});

let total = ref(0);

let searchParams = {};

let showFilterContainer = ref(false);
let filterButtonIcon = ref("ArrowDown");

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
    if (showFilterContainer.value) {
        filterButtonIcon.value = "ArrowUp";
    } else {
        filterButtonIcon.value = "ArrowDown";
    }
};

function update_table() {
    http.get('/machines/usage_records', { params: form}).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });
};

function onSearch() {
    update_table();
};

function onFilter() {
    update_table();
};


function handleRefresh() {
    update_table();
};

onMounted(() => {
    update_table();
});

</script>


<style scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
  border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}
</style>