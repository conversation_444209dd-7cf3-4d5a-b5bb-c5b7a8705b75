<template>

    <div style="width: 100%;">
        <div v-for="(v, index) in model" class="recv_msg_item">
            <el-input v-model="model[index]"></el-input> <el-button type="danger" plain @click="handleDelete(index)" icon="Minus" style="margin-left: 10px"></el-button>
        </div>
        <el-button type="primary" plain @click="handleAdd" icon="Plus">添加接收报文</el-button>
        
    </div>

</template>

<script setup>
import { ref, watch } from 'vue'

const model = defineModel();
const last_item = ref('')

function handleDelete(index) {
    if (model.value.length === 1) {
        return
    }
    model.value.splice(index, 1)
}

function handleAdd() {
    model.value.push('')
}

</script>

<style scoped>
.recv_msg_item {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.recv_msg_item:not(:last-child) {
    margin-bottom: 10px;
}
</style>