<template>
    <el-select ref="selectRef" v-model="model" :placeholder="props.includeAll ? '所有项目' : '请选择项目'" @change="onChange" filterable>
        <template #prefix v-if="props.includePrefix">
            <span style="color: var(--el-input-text-color,var(--el-text-color-regular));">项目：</span>
        </template>
        <template #footer v-if="props.includeAll" class="pppp">

            <div class="project-footer">
                <div class="project-footer-item" @click="onAllProjectClick">所有项目</div>
            </div>

        </template>
        <el-option v-for="item in projects" :label="`${item.name}(${item.projectCode})`"
            :value="item.projectCode"></el-option>
    </el-select>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http/http.js';

const props = defineProps({
    includePrefix: {
        type: Boolean,
        default: true
    },
    includeAll: {
        type: Boolean,
        default: true
    }
    
});

const selectRef = ref(null);
const model = defineModel();
const emit = defineEmits(['change']);

const projects = ref([]);

function onChange(number) {
    let info = projects.value.find(item => item.projectCode === number);
    emit('change', info);
};

function onAllProjectClick() {
    model.value = '';
    selectRef.value.blur();
    emit('change');
};

// 暴露方法 通过project_number获取项目信息
function getProjectInfo(project_number) {
    return projects.value.find(item => item.projectCode === project_number);
};

defineExpose({
    getProjectInfo
});

onMounted(() => {
    http.get('/projects/p/all').then(res => {
        let data = res.data.data.results;
        projects.value = data;
    }).catch(err => {
        console.log(err);
    });
});

</script>

<style lang="scss" scoped>
.project-footer {
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    .project-footer-item {
        margin: 0;
        box-sizing: border-box;
        color: var(--el-text-color-regular);
        cursor: pointer;
        font-size: var(--el-font-size-base);
        height: 34px;
        line-height: 34px;
        overflow: hidden;
        padding: 0 0 0 20px;
        position: relative;
        text-overflow: ellipsis;
        white-space: nowrap;

        &:hover {
            background-color: var(--el-fill-color-light);
        }
    }
}

</style>