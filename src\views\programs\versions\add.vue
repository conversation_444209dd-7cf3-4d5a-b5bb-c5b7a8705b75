<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="程序" prop="program_id">
                <el-select v-model="form.program_id" placeholder="请选择程序">
                    <el-option v-for="item in programs" :label="item.name + '(' + item.number + ')'"
                        :value="item.id"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="版本号" prop="version">
                <el-input v-model="form.version" placeholder="请输入版本号"></el-input>
            </el-form-item>

            <el-form-item label="版本路径(WINDOWS)" prop="path">
                <el-input v-model="form.path" placeholder="请输入版本路径"></el-input>
            </el-form-item>

            <el-form-item label="版本路径(LINUX)">
                <el-input v-model="form.linux_path" placeholder="请输入版本路径"></el-input>
            </el-form-item>

            <el-form-item label="版本描述" prop="desc">
                <el-input type="textarea" :rows="4" v-model="form.desc" placeholder="请输入版本描述"></el-input>
            </el-form-item>

            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';

import http from '@/utils/http/http.js';


const formRef = ref(null);


const form = ref({
    program_id: '',
    version: '',
    path: '',
    linux_path: '',
    desc: '',
});

const rules = ref({
    program_id: [
        { required: true, message: '请选择程序', trigger: 'change' },
    ],
    version: [
        { required: true, message: '请输入版本号', trigger: 'blur' },
    ],
    path: [
        { required: true, message: '请输入版本路径', trigger: 'blur' },
    ],
    desc: [
        { required: true, message: '请输入版本描述', trigger: 'blur' },
    ],

});

let programs = ref([]);

const emit = defineEmits(['confirm', 'cancel']);

const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            http.post('/programs/versions', form.value).then(res => {
                ElMessage({
                    message: '添加成功.',
                    type: 'success',
                });
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

function onCancel() {
    emit('cancel');
};


onMounted(() => {
    http.get('/programs', { params: { page: 1, pagesize: 1000 } }).then(res => {
        programs.value = res.data.data.results;
    });
});


</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>