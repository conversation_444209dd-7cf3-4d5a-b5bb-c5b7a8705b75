.app-container {
  height: 100%;
}

.nav-container {
  height: 50px;
  background: #fff;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);

  padding: 0;

}

// .side-container {
//   height: 100%;
//   width: $sideBarWidth !important;
//   background-color: $menuBg;
//   overflow: hidden;

//   .el-menu {
//     border: none;
//     height: 100%;
//     width: 100% !important;
//   }
// }

// .hideSider {
//   .side-container {
//     width: 0 !important;
//   }
// }