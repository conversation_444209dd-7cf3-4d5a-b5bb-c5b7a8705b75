import { defineStore } from "pinia";

export const useProjectStore = defineStore('project', {
    state: () => ({
        project_info: {},
        // 代码管理相关状态
        codeManagement: {
            gitlab: '',           // 选择的项目仓库
            project_branch: '',   // 选择的分支
            spaceOptions: [],     // 可用的仓库列表
            branchOptions: [],    // 可用的分支列表
            sdkVersion: '',       // SDK版本
        }
    }),
    actions: {
        update(project_info) {
            this.project_info = project_info
        },
        // 更新代码管理相关状态
        updateCodeManagement(data) {
            this.codeManagement = { ...this.codeManagement, ...data }
        },
        // 清空代码管理状态（项目切换时使用）
        clearCodeManagement() {
            this.codeManagement = {
                gitlab: '',
                project_branch: '',
                spaceOptions: [],
                branchOptions: [],
                sdkVersion: '',
            }
        },
        // 设置仓库信息
        setRepositoryInfo(gitlab, project_branch = '') {
            this.codeManagement.gitlab = gitlab
            this.codeManagement.project_branch = project_branch
        },
        // 设置仓库选项
        setSpaceOptions(options) {
            this.codeManagement.spaceOptions = options
        },
        // 设置分支选项
        setBranchOptions(options) {
            this.codeManagement.branchOptions = options
        },
        // 设置SDK版本
        setSdkVersion(version) {
            this.codeManagement.sdkVersion = version
        }
    },
    persist: {
        key: 'project',
        storage: sessionStorage
    }
},
);