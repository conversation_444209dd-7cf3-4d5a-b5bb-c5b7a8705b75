<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">
            <el-form-item label="部门">
                <el-select v-model="form.department" placeholder="请选择部门" clearable>
                    <el-option label="系统测试部" value="系统测试部"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="名称" prop="name">
                <el-input v-model="form.name"></el-input>
            </el-form-item>

            <el-form-item label="类型" prop="code">
                <el-select v-model="form.type_id" placeholder="请选择类型" clearable>
                    <el-option v-for="item in skill_types" :label="item.name" :value="item.id"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="描述">
                <el-input type="textarea" :rows="4" v-model="form.desc"></el-input>
            </el-form-item>

            <div class="submit-button-container">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http/http.js';

const formRef = ref(null);
const skill_types = ref(null);

http.get('/testers/skill_types', { params: { pagesize: 100000 } }).then(res => {
        let data = res.data.data.results;
        skill_types.value = data;

    }).catch(err => {
        console.log(err);
    });

const form = ref({
    name: '',
    type_id: '',
    desc: '',
});

const rules = ref({
    name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
    ],
    type_id: [
        { required: true, message: '请选择类型', trigger: 'blur' },
    ],
});

const emit = defineEmits(['submit', 'cancel'])

const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            http.post('/testers/skills', form.value).then(res => {
                emit('submit');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onCancel = () => {
    emit('cancel');
};

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>