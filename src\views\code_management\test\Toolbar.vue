<template>
  <!-- 顶部工具栏 -->
  <div class="toolbar">
    <div class="toolbar-left">
      <!-- 左侧版本标识 -->
      <div class="version-info">
        <el-tag type="info" effect="plain" :style="{ border: 'none'}">
          <el-icon style="color: #409eff;"><InfoFilled /></el-icon>
          版本: {{ sdkVersion || 'N/A' }}
        </el-tag>
      </div>
    </div>
    <div class="toolbar-right">
      <!-- 工作空间和分支 -->
      <div class="config-selects">
        <el-form-item label="项目仓库:" class="toolbar-form-item" style="font-size: 14px; font-weight: 300; color: #666;">
          <el-select
            v-model="form.gitlab"
            class="input-field"
            placeholder="请选择或输入仓库地址"
            filterable
            allow-create
            clearable
            style="border: none; padding: 0px"
          >
            <el-option
              v-for="item in spaceOptions"
              :key="item"
              :label="item.split('/').pop()"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工作空间:" class="toolbar-form-item" style="font-size: 14px; font-weight: 300; color: #666;">
          <el-select
            v-model="form.project_branch"
            class="input-field"
            placeholder="请选择或输入分支"
            filterable
            allow-create
            clearable
            style="border: none; padding: 0px;"
          >
            <el-option
              v-for="branch in branchOptions"
              :key="branch"
              :label="branch"
              :value="branch"
            />
          </el-select>
        </el-form-item>
      </div>
      <!-- 功能按钮 -->
      <div class="button-group">
        <el-button type="primary" :disabled="!hasEditPermission" @click="handExport">
          Export
        </el-button>
        <el-button type="primary" :disabled="!hasEditPermission" @click="handleSave">
          Commit
        </el-button>
        <el-button type="success" :disabled="!hasEditPermission" @click="handlePublish">
          Push
        </el-button>
        <el-button type="danger" :disabled="!hasEditPermission" @click="handlemerge">
          Merge
        </el-button>
      </div>
    </div>
    
    <!-- 合并分支弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="选择合并分支"
      width="400px"
      :show-close="true"
      center
      class="merge-dialog"
    >
      <div class="dialog-content">
        <el-form label-position="top">
          <el-form-item label="目标分支">
            <el-select v-model="mergeBranch" :placeholder="mergeBranch" style="width: 100%">
              <el-option v-for="item in ['dev']" :key="item" :label="item" :value="item">
                {{ item }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="() => { confirmAction(mergeBranch); dialogVisible = false; }">确定合并</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';
import { ElMessageBox } from 'element-plus';
import messageManager from '@/utils/messageManager';
import http from '@/utils/http/http';
import { useProjectStore } from '@/stores/project.js';

// 事件定义
const emit = defineEmits(['data-loaded', 'project-changed']);

// Store 和状态管理
const projectStore = useProjectStore();

// 项目信息
let project_code = '';
let project_name = '';

// 表单数据
const form = reactive({
  gitlab: '',
  project_branch: ''
});

// 选项数据
const spaceOptions = ref([]);
const branchOptions = ref([]);

// 状态数据
const sdkVersion = ref('');
const workspace = ref('');
const branch_status = ref('');
const loading = ref(false);
const branch_create = ref(true);

// 权限控制
const hasEditPermission = computed(() => branch_create.value);

// 响应式数据
const dialogVisible = ref(false);
const mergeBranch = ref('dev');




// 初始化时从全局状态恢复数据
const initializeFromStore = () => {
  const codeManagement = projectStore.codeManagement;
  form.gitlab = codeManagement.gitlab || '';
  form.project_branch = codeManagement.project_branch || '';
  spaceOptions.value = codeManagement.spaceOptions || [];
  branchOptions.value = codeManagement.branchOptions || [];
  sdkVersion.value = codeManagement.sdkVersion || '';
};

// 获取仓库列表
const get_space = async () => {
  try {
    loading.value = true;
    console.log('🔄 调用get_space - 开始获取仓库信息，项目代码:', project_code);

    const response = await http.get('/code_management/space_options', {
      project_code: project_code
    });

    if (response.data.status === 1) {
      spaceOptions.value = response.data.space_options || [];
      console.log('✅ 仓库列表:', spaceOptions.value);

      // 设置默认仓库为第一个
      if (spaceOptions.value.length > 0 && !form.gitlab) {
        form.gitlab = spaceOptions.value[0];
        console.log('✅ 设置默认仓库:', form.gitlab);
      } else if (spaceOptions.value.length === 0) {
        messageManager.warning('该项目暂无可用仓库');
      }
    } else {
      messageManager.error(response.data.message || '获取仓库列表失败');
    }
  } catch (error) {
    console.error('❌ 获取仓库列表错误:', error);
    messageManager.error('获取仓库列表失败，请重试');
  } finally {
    loading.value = false;
  }
};






// 获取分支列表
const get_branch = async () => {
  if (!form.gitlab) {
    console.warn('⚠️ 仓库地址为空，无法获取分支');
    return;
  }

  try {
    loading.value = true;
    console.log('🔄 调用get_branch - 开始获取分支信息');

    const response = await http.get('/code_management/branch_options', {
      project_code: project_code,
      gitlab: form.gitlab
    });

    if (response.data.status === 1) {
      branchOptions.value = response.data.branch_options || [];
      console.log('✅ 分支列表:', branchOptions.value);

      // 设置默认分支为第一个
      if (branchOptions.value.length > 0 && !form.project_branch) {
        form.project_branch = branchOptions.value[0];
        console.log('✅ 设置默认分支:', form.project_branch);
      } else if (branchOptions.value.length === 0) {
        messageManager.warning('该仓库暂无可用分支');
      }
    } else {
      messageManager.error(response.data.message || '获取分支列表失败');
    }
  } catch (error) {
    console.error('❌ 获取分支列表错误:', error);
    messageManager.error('获取分支列表失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 提交分支信息并获取SDK版本
const submit_branch_info = async () => {
  if (!form.gitlab || !form.project_branch) {
    console.warn('⚠️ 仓库或分支信息不完整，无法提交');
    return;
  }

  try {
    loading.value = true;
    console.log('🔄 调用submit_branch_info - 提交分支信息');

    const response = await http.get('/code_management/branch_submit', {
      project_code: project_code,
      gitlab: form.gitlab,
      project_branch: form.project_branch
    });

    if (response.data.config_status === 1) {
        sdkVersion.value = response.data.sdk_version;
        workspace.value = response.data.workspace || '';
        branch_status.value = response.data.branch_status || '';
        console.log('✅ SDK版本:', sdkVersion.value);

        // 更新分支创建权限状态
        branch_create.value = response.data.branch_create === true || response.data.branch_create === 'true';
        console.log('✅ 分支创建权限:', branch_create.value);

        messageManager.success('项目配置加载成功');

        // 通知父组件数据已加载
        emit('data-loaded', {
          sdkVersion: sdkVersion.value,
          workspace: workspace.value,
          branch_status: branch_status.value,
          hasEditPermission: branch_create.value
        });
    } else {
      messageManager.error(response.data.message || '提交分支信息失败');
    }
  } catch (error) {
    console.error('❌ 提交分支信息错误:', error);
    messageManager.error('提交分支信息失败，请重试');
  } finally {
    loading.value = false;
  }
};


// 功能按钮处理函数
// Export/Download 操作
const handExport = () => {
  ElMessageBox.confirm(
    '确定要下载当前配置吗？',
    '下载确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    loading.value = true;
    http.post('/code_management/config_export', {
      params: { workspace_path: workspace.value, branch_status: branch_status.value },
      timeout: 60000
    }).then(response => {
      loading.value = false;
      if (response.data.export_status === 1) {
        messageManager.success('配置下载成功');
      } else {
        messageManager.error('配置下载失败');
      }
    }).catch(() => {
      loading.value = false;
      messageManager.error('下载失败');
    });
  }).catch(() => {
    messageManager.info('已取消下载');
  });
};

// Commit 操作
const handleSave = async () => {
  ElMessageBox.prompt('请输入 commit 信息', '保存配置', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    inputErrorMessage: '请输入 commit 信息',
    inputValidator: (value) => value != null && value.trim() !== ''
  }).then(({ value }) => {
    loading.value = true;
    http.post('/code_management/config_commit', {
      params: { commit_message: value, workspace_path: workspace.value, branch_status: branch_status.value },
      timeout: 60000
    }).then(response => {
      loading.value = false;
      if (response.data.commit_status === 1) {
        messageManager.success('配置已保存:commit成功');
      } else {
        messageManager.error('配置保存失败:commit失败');
      }
    }).catch(() => {
      loading.value = false;
      messageManager.error('保存失败');
    });
  }).catch(() => {
    messageManager.info('取消保存');
  });
};

// Push 操作
const handlePublish = () => {
  ElMessageBox.confirm(
    '确定要发布当前配置吗？发布后将生效并覆盖现有配置。',
    '发布确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    loading.value = true;
    http.post('/code_management/config_push', {
      params: { workspace_path: workspace.value, branch_status: branch_status.value },
      timeout: 60000
    }).then(response => {
      loading.value = false;
      if (response.data.push_status === 1) {
        messageManager.success('配置已发布');
      } else {
        messageManager.error('配置发布失败');
      }
    }).catch(() => {
      loading.value = false;
      messageManager.info('已取消发布');
    });
  }).catch(() => {
    messageManager.info('已取消发布');
  });
};

// Merge 操作
const handlemerge = () => {
  dialogVisible.value = true;
};

// merge 确认操作
const confirmAction = (mergeBranch) => {
  ElMessageBox.confirm(
    '确定要merge当前分支到目标分支吗？',
    '发布确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    loading.value = true;
    http.post('/code_management/merge_project', {
      params: { workspace_path: workspace.value, merge_branch: mergeBranch },
      timeout: 60000
    }).then(() => {
      messageManager.success('已发送merge信息,请等待管理员审批');
      loading.value = false;
    }).catch(() => {
      messageManager.error('merge失败');
      loading.value = false;
    });
  }).catch(() => {
    messageManager.info('已取消merge');
  });
};

// 监听仓库变化，自动获取分支
watch(() => form.gitlab, async (newGitlab) => {
  if (newGitlab) {
    console.log('🔄 仓库变化，重新获取分支:', newGitlab);
    await get_branch();

    // 保存到全局状态
    projectStore.updateCodeManagement({
      gitlab: newGitlab,
      spaceOptions: spaceOptions.value
    });
  }
});

// 监听分支变化，自动提交分支信息
watch(() => form.project_branch, async (newBranch) => {
  if (newBranch && form.gitlab) {
    console.log('🔄 分支变化，提交分支信息:', newBranch);
    await submit_branch_info();

    // 保存到全局状态
    projectStore.updateCodeManagement({
      project_branch: newBranch,
      branchOptions: branchOptions.value,
      sdkVersion: sdkVersion.value
    });
  }
});

// 组件挂载时初始化数据
onMounted(async () => {
  try {
    // 从全局状态初始化数据
    initializeFromStore();

    // 监控项目信息变化
    const info = projectStore.project_info || {};
    project_code = info.projectCode || '';
    project_name = info.name || '';

    console.log('project_code:', project_code);
    console.log('project_name:', project_name);
    console.log('从全局状态恢复的仓库信息:', form.gitlab);
    console.log('从全局状态恢复的分支信息:', form.project_branch);

    if (project_code) {
      // 获取仓库列表
      await get_space();

      // 如果有仓库信息，获取分支列表
      if (form.gitlab) {
        await get_branch();

        // 如果有分支信息，提交分支信息
        if (form.project_branch) {
          await submit_branch_info();
        }
      }
    } else {
      messageManager.warning('请先选择项目');
    }
  } catch (error) {
    console.error('❌ 组件初始化错误:', error);
    messageManager.error('组件初始化失败');
  }
});

</script>

<style scoped>
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 35px 30px;
  height: 50px;
  background: #fff;
  color: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  border-radius: 8px;
  /* margin: 0px 20px; */
}


.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
}
.version-info {
  margin-right: 20px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.config-selects {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-form-item {
  margin-bottom: 0;
}

.input-field {
  min-width: 150px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.merge-dialog {
  border-radius: 8px;
}

.dialog-content {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
