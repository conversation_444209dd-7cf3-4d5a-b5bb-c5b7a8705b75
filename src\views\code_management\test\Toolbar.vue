<template>
  <!-- 顶部工具栏 -->
  <div class="toolbar">
    <div class="toolbar-left">
      <!-- 左侧版本标识 -->
      <div class="version-info">
        <el-tag type="info" effect="plain" :style="{ border: 'none'}">
          <el-icon style="color: #409eff;"><InfoFilled /></el-icon>
          版本: {{ sdkVersion || 'N/A' }}
        </el-tag>
      </div>
    </div>
    <div class="toolbar-right">
      <!-- 工作空间和分支 -->
      <div class="config-selects">
        <el-form-item label="项目仓库:" class="toolbar-form-item" style="font-size: 14px; font-weight: 300; color: #666;">
          <el-select
            :model-value="form.gitlab"
            @update:model-value="updateGitlab"
            class="input-field"
            placeholder="请选择或输入仓库地址"
            filterable
            allow-create
            clearable
            style="border: none; padding: 0px"
            @visible-change="handleGitlabClick"
          >
            <el-option
              v-for="item in spaceOptions"
              :key="item"
              :label="item.split('/').pop()"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工作空间:" class="toolbar-form-item" style="font-size: 14px; font-weight: 300; color: #666;">
          <el-select
            :model-value="form.project_branch"
            @update:model-value="updateBranch"
            class="input-field"
            placeholder="请选择或输入分支"
            filterable
            allow-create
            clearable
            style="border: none; padding: 0px;"
          >
            <el-option
              v-for="branch in branchOptions"
              :key="branch"
              :label="branch"
              :value="branch"
            />
          </el-select>
        </el-form-item>
      </div>
      <!-- 功能按钮 -->
      <div class="button-group">
        <el-button type="primary" :disabled="!branch_create" @click="handExport">
          Export
        </el-button>
        <el-button type="primary" :disabled="!branch_create" @click="handleSave">
          Commit
        </el-button>
        <el-button type="success" :disabled="!branch_create" @click="handlePublish">
          Push
        </el-button>
        <el-button type="danger" :disabled="!branch_create" @click="handlemerge">
          Merge
        </el-button>
      </div>
    </div>
    
    <!-- 合并分支弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="选择合并分支"
      width="400px"
      :show-close="true"
      center
      class="merge-dialog"
    >
      <div class="dialog-content">
        <el-form label-position="top">
          <el-form-item label="目标分支">
            <el-select v-model="mergeBranch" :placeholder="mergeBranch" style="width: 100%">
              <el-option v-for="item in ['dev']" :key="item" :label="item" :value="item">
                {{ item }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAction">确定合并</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';
import { ElMessageBox } from 'element-plus';
import messageManager from '@/utils/messageManager';
import http from '@/utils/http/http';
import { useProjectStore } from '@/stores/project.js';

// 使用 store
const projectStore = useProjectStore();

// 项目相关变量
let project_code = '';
let project_name = '';

// Props
const props = defineProps({
  hasEditPermission: {
    type: Boolean,
    default: true
  }
});

// 内部状态管理
const sdkVersion = ref('');
const form = reactive({
  gitlab: '',
  project_branch: ''
});
const spaceOptions = ref([]);
const branchOptions = ref([]);
const loading = ref(false);
const branch_create = ref(true);


const emit = defineEmits([
  'data-loaded',
  'project-changed'
]);



// 响应式数据
const dialogVisible = ref(false);
const mergeBranch = ref('dev');
const workspace = ref('');
const branch_status = ref('');

// 初始化时从全局状态恢复数据
const initializeFromStore = () => {
  const codeManagement = projectStore.codeManagement;
  form.gitlab = codeManagement.gitlab || '';
  form.project_branch = codeManagement.project_branch || '';
  spaceOptions.value = codeManagement.spaceOptions || [];
  branchOptions.value = codeManagement.branchOptions || [];
  sdkVersion.value = codeManagement.sdkVersion || '';
};

// 获取仓库列表
const get_space = async () => {
  try {
    loading.value = true;
    console.log('🔄 调用get_space - 开始获取仓库信息，项目代码:', project_code);

    const response = await http.post('/code_management/space_options', {
      project_code: project_code
    });

    if (response.data.status === 1) {
      spaceOptions.value = response.data.space_options || [];
      console.log('仓库列表:', spaceOptions.value);

      // 设置默认仓库为第一个
      if (spaceOptions.value.length > 0 && !form.gitlab) {
        form.gitlab = spaceOptions.value[0];
        console.log('设置默认仓库:', form.gitlab);
      } else if (spaceOptions.value.length === 0) {
        messageManager.warning('该项目暂无可用仓库');
      }
    } else {
      messageManager.error(response.data.message || '获取仓库列表失败');
    }
  } catch (error) {
    console.error('获取仓库列表错误:', error);
    messageManager.error('获取仓库列表失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 获取分支列表
const get_branch = async () => {
  if (!form.gitlab) {
    console.warn('仓库地址为空，无法获取分支');
    return;
  }

  try {
    loading.value = true;
    console.log('🔄 调用get_branch - 开始获取分支信息');

    const response = await http.post('/code_management/branch_options', {
      project_code: project_code,
      gitlab: form.gitlab
    });

    if (response.data.status === 1) {
      branchOptions.value = response.data.branch_options || [];
      console.log('分支列表:', branchOptions.value);

      // 设置默认分支为第一个
      if (branchOptions.value.length > 0 && !form.project_branch) {
        form.project_branch = branchOptions.value[0];
        console.log('设置默认分支:', form.project_branch);
      } else if (branchOptions.value.length === 0) {
        messageManager.warning('该仓库暂无可用分支');
      }
    } else {
      messageManager.error(response.data.message || '获取分支列表失败');
    }
  } catch (error) {
    console.error('获取分支列表错误:', error);
    messageManager.error('获取分支列表失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 提交分支信息并获取SDK版本
const submit_branch_info = async () => {
  if (!form.gitlab || !form.project_branch) {
    console.warn('仓库或分支信息不完整，无法提交');
    return;
  }

  try {
    loading.value = true;
    console.log('🔄 调用submit_branch_info - 提交分支信息');

    const response = await http.post('/code_management/branch_submit', {
      project_code: project_code,
      gitlab: form.gitlab,
      project_branch: form.project_branch
    });

    if (response.data.config_status === 1) {
        sdkVersion.value = response.data.sdk_version;
        workspace.value = response.data.workspace || '';
        branch_status.value = response.data.branch_status || '';
        console.log('SDK版本:', sdkVersion.value);

        // 更新分支创建权限状态
        branch_create.value = response.data.branch_create === true || response.data.branch_create === 'true';
        console.log('分支创建权限:', branch_create.value);

        messageManager.success('项目配置加载成功');

        // 通知父组件数据已加载
        emit('data-loaded', {
          sdkVersion: sdkVersion.value,
          workspace: workspace.value,
          branch_status: branch_status.value,
          hasEditPermission: branch_create.value
        });
    } else {
      messageManager.error(response.data.message || '提交分支信息失败');
    }
  } catch (error) {
    console.error('提交分支信息错误:', error);
    messageManager.error('提交分支信息失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时初始化数据
onMounted(() => {
  try {
    // 从全局状态初始化数据
    initializeFromStore();

    // 监控项目信息变化
    const info = projectStore.project_info || {};
    project_code = info.projectCode || '';
    project_name = info.name || '';

    console.log('Toolbar - project_code:', project_code);
    console.log('Toolbar - project_name:', project_name);

    if (project_code === "" && project_name === "") {
      console.info('项目信息为空，请先选择项目');
    } else {
      console.info('项目信息已选择，开始初始化');
      // 如果没有仓库信息，则获取仓库列表
      if (!form.gitlab || spaceOptions.value.length === 0) {
        get_space();
      } else {
        // 如果有仓库信息但没有分支信息，则获取分支列表
        if (!form.project_branch || branchOptions.value.length === 0) {
          get_branch();
        } else {
          // 如果都有，直接提交分支信息
          submit_branch_info();
        }
      }
    }
  } catch (error) {
    console.error('Toolbar组件初始化错误:', error);
    messageManager.error('工具栏初始化失败，请刷新页面重试');
  }
});

// 监控项目信息变化
watch(() => projectStore.project_info, (newval) => {
  if (newval) {
    const newProjectCode = newval.projectCode || '';
    const newProjectName = newval.name || '';

    // 只有当项目代码真正发生变化时才重新获取仓库信息
    if (newProjectCode !== project_code) {
      console.log('Toolbar - 项目切换:', project_code, '->', newProjectCode);

      project_code = newProjectCode;
      project_name = newProjectName;

      // 检查项目是否选择
      if (project_code !== "" || project_name !== "") {
        // 清空本地状态
        form.gitlab = '';
        form.project_branch = '';
        spaceOptions.value = [];
        branchOptions.value = [];
        get_space();

        // 通知父组件项目已变更
        emit('project-changed', { project_code, project_name });
      }
    }
  }
});

// 监控仓库信息变化
watch(() => form.gitlab, (newval, oldval) => {
  if (newval !== oldval && newval) {
    form.project_branch = '';
    get_branch();
  }
});

// 监控分支信息变化
watch(() => form.project_branch, (newval, oldval) => {
  if (newval !== oldval && newval) {
    submit_branch_info();
  }
});

const handExport = () => {
    ElMessageBox.confirm(
    '确定要下载当前配置吗？',
    '下载确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
      messageManager.success('下载成功');
    }).catch(() => {
      messageManager.info('下载失败');
    });
}

// 更新表单数据
const updateGitlab = (value) => {
  form.gitlab = value;
};

const updateBranch = (value) => {
  form.project_branch = value;
};

// 事件处理函数
const handleGitlabClick = () => {
  if (!project_code) {
    messageManager.warning('请选择项目！');
  }
};






// 功能按钮
// commit 操作
const handleSave = async () => {
  ElMessageBox.prompt('请输入 commit 信息', '保存配置', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    inputErrorMessage: '请输入 commit 信息',
    inputValidator: (value) => value != null && value.trim() !== ''
  }).then(({ value }) => {
    loading.value = true;
    http.post('/code_management/config_commit', {
      params: { commit_message: value, workspace_path: workspace.value, branch_status: branch_status.value },
      timeout: 60000
    }).then(response => {
      loading.value = false;
      if (response.data.commit_status === 1) {
        messageManager.success('配置已保存:commit成功');
      } else {
        messageManager.error('配置保存失败:commit失败');
      }
    }).catch(() => {
      loading.value = false;
      messageManager.error('保存失败');
    });
  }).catch(() => {
    messageManager.info('取消保存');
  });
};

// push 操作
const handlePublish = () => {
  ElMessageBox.confirm(
    '确定要发布当前配置吗？发布后将生效并覆盖现有配置。',
    '发布确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    loading.value = true;
    http.post('/code_management/config_push', {
      params: { workspace_path: workspace.value, branch_status: branch_status.value },
      timeout: 60000
    }).then(response => {
      loading.value = false;
      if (response.data.push_status === 1) {
        messageManager.success('配置已发布');
      } else {
        messageManager.error('配置发布失败');
      }
    }).catch(() => {
      loading.value = false;
      messageManager.info('已取消发布');
    });
  }).catch(() => {
    messageManager.info('已取消发布');
  });
};


const handlemerge = () => {
  dialogVisible.value = true;
}

// merge 确认操作
const confirmAction = (mergeBranch) => {
  ElMessageBox.confirm(
    '确定要merge当前分支到目标分支吗？',
    '发布确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    loading.value = true;
    http.post('/code_management/merge_project', {
      params: { workspace_path: workspace.value, merge_branch: mergeBranch },
      timeout: 60000
    }).then(() => {
      messageManager.success('已发送merge信息,请等待管理员审批');
      loading.value = false;
    }).catch(() => {
      messageManager.error('merge失败');
      loading.value = false;
    });
  }).catch(() => {
    messageManager.info('已取消merge');
  });
};


</script>

<style scoped>
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 35px 30px;
  height: 50px;
  background: #fff;
  color: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  border-radius: 8px;
  /* margin: 0px 20px; */
}


.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
}
.version-info {
  margin-right: 20px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.config-selects {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-form-item {
  margin-bottom: 0;
}

.input-field {
  min-width: 150px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.merge-dialog {
  border-radius: 8px;
}

.dialog-content {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
