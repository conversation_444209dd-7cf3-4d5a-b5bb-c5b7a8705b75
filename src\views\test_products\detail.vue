<template>
    <div class="product-detail">
        <!-- 左侧：层级结构 -->
        <div class="left-panel">
            <el-card shadow="never" class="tree-card">
                <template #header>
                    <div class="tree-header">
                        <h3>
                            <el-icon>
                                <Operation />
                            </el-icon>
                            产品结构
                        </h3>
                        <div class="tree-actions">
                            <el-button size="small" @click="expandAll">
                                <el-icon>
                                    <component :is="isAllExpanded ? 'Fold' : 'Expand'" />
                                </el-icon>
                                {{ isAllExpanded ? '收起' : '展开' }}
                            </el-button>
                        </div>
                    </div>
                </template>

                <el-scrollbar height="calc(100vh - 200px)">
                    <el-tree ref="treeRef" :data="treeData" :props="treeProps" :expand-on-click-node="false"
                        :highlight-current="true" node-key="id" class="product-tree" @node-click="handleNodeClick">
                        <template #default="{ node, data }">
                            <div class="tree-node">
                                <div class="node-content">
                                    <el-icon class="node-icon">
                                        <Box v-if="data.depth === 1" />
                                        <Cpu v-else />
                                    </el-icon>
                                    <span class="node-name" :title="data.name">{{ data.name }}<el-tag style="margin-left: 5px;">{{ data.m_number }}</el-tag></span>
                                </div>
                            </div>
                        </template>
                    </el-tree>
                </el-scrollbar>
            </el-card>
        </div>

        <!-- 右侧：详情展示 -->
        <div class="right-panel">
            <el-card shadow="never" class="detail-card">
                <template #header>
                    <div class="detail-header">
                        <div class="header-left">
                            <h3>{{ selectedNode?.name || '请选择组件' }}</h3>
                        </div>
                    </div>
                </template>

                <el-scrollbar height="calc(100vh - 200px)">
                    <!-- 详情内容 -->
                    <div v-if="selectedNode" class="detail-content">
                        <!-- 基本信息 -->
                        <div class="info-section">
                            <div class="section-header">
                                <h4>
                                    <el-icon><InfoFilled /></el-icon>
                                    基本信息
                                </h4>
                            </div>
                            <el-descriptions :column="1" border size="small" class="custom-descriptions">
                                <el-descriptions-item label="料号">
                                    <el-text>{{ selectedNode.m_number || '未设置' }}</el-text>
                                </el-descriptions-item>
                                <el-descriptions-item label="所属项目" :span="2">
                                    <el-text>{{ selectedNode.project_name }}</el-text>
                                    <el-tag size="small" type="primary" style="margin-left: 8px;">
                                        {{ selectedNode.project_number }}
                                    </el-tag>
                                </el-descriptions-item>
                                <el-descriptions-item label="创建时间">
                                    <el-text>{{ formatDateTime(selectedNode.create_time) }}</el-text>
                                </el-descriptions-item>
                                <el-descriptions-item label="更新时间">
                                    <el-text>{{ formatDateTime(selectedNode.update_time) }}</el-text>
                                </el-descriptions-item>
                            </el-descriptions>
                        </div>

                        <!-- 统计概览 -->
                        <div class="info-section">
                            <div class="section-header">
                                <h4>
                                    <el-icon><DataAnalysis /></el-icon>
                                    统计概览
                                </h4>
                            </div>
                            <div class="stats-grid">
                                <div class="stat-card hardware">
                                    <div class="stat-icon">
                                        <el-icon><Cpu /></el-icon>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number">{{ selectedNode.hardware_versions?.length || 0 }}</div>
                                        <div class="stat-label">硬件版本</div>
                                    </div>
                                </div>
                                <div class="stat-card software">
                                    <div class="stat-icon">
                                        <el-icon><Monitor /></el-icon>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number">{{ selectedNode.software_versions?.length || 0 }}</div>
                                        <div class="stat-label">软件版本</div>
                                    </div>
                                </div>
                                <div class="stat-card components">
                                    <div class="stat-icon">
                                        <el-icon><Operation /></el-icon>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number">{{ selectedNode.components?.length || 0 }}</div>
                                        <div class="stat-label">子组件</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 硬件版本 -->
                        <div v-if="selectedNode.hardware_versions?.length > 0" class="info-section">
                            <div class="section-header">
                                <h4>
                                    <el-icon><Cpu /></el-icon>
                                    硬件版本 ({{ selectedNode.hardware_versions.length }})
                                </h4>
                                <el-button size="small" text @click="toggleVersionsExpand('hardware')">
                                    {{ hardwareExpanded ? '收起' : '展开' }}全部
                                </el-button>
                            </div>
                            <el-collapse v-model="hardwareActiveNames" class="version-collapse">
                                <el-collapse-item 
                                    v-for="(version, index) in selectedNode.hardware_versions" 
                                    :key="version.id"
                                    :name="version.id"
                                    class="version-collapse-item"
                                >
                                    <template #title>
                                        <div class="collapse-title">
                                            <div class="title-left">
                                                <el-icon><Cpu /></el-icon>
                                                <span class="version-name">{{ version.name }}</span>
                                                <el-tag size="small" type="primary">{{ version.num }}</el-tag>
                                            </div>
                                            <div class="title-right">
                                            </div>
                                        </div>
                                    </template>
                                    
                                    <div class="version-content">
                                        <el-row :gutter="16">
                                            <el-col :span="12">
                                                <div class="info-group">
                                                    <label>PCBA信息</label>
                                                    <p><strong>代码:</strong> {{ version.pcbaCode }}</p>
                                                    <p><strong>名称:</strong> {{ version.pcbaName }}</p>
                                                </div>
                                            </el-col>
                                            <el-col :span="12">
                                                <div class="info-group">
                                                    <label>时间信息</label>
                                                    <p><strong>计划发布:</strong> {{ formatDate(version.planReleaseTime) }}</p>
                                                    <p><strong>实际发布:</strong> {{ formatDate(version.realReleaseTime) }}</p>
                                                </div>
                                            </el-col>
                                        </el-row>

                                        <div v-if="version.description" class="info-group">
                                            <label>版本描述</label>
                                            <div v-html="version.description" class="desc-content"></div>
                                        </div>

                                        <div v-if="version.packageUrl" class="info-group">
                                            <label>包地址</label>
                                            <el-link :href="version.packageUrl" target="_blank" type="primary" class="package-link">
                                                <el-icon><Link /></el-icon>
                                                {{ version.packageUrl }}
                                            </el-link>
                                        </div>
                                    </div>
                                </el-collapse-item>
                            </el-collapse>
                        </div>

                        <!-- 软件版本 -->
                        <div v-if="selectedNode.software_versions?.length > 0" class="info-section">
                            <div class="section-header">
                                <h4>
                                    <el-icon><Monitor /></el-icon>
                                    软件版本 ({{ selectedNode.software_versions.length }})
                                </h4>
                                <el-button size="small" text @click="toggleVersionsExpand('software')">
                                    {{ softwareExpanded ? '收起' : '展开' }}全部
                                </el-button>
                            </div>
                            <el-collapse v-model="softwareActiveNames" class="version-collapse">
                                <el-collapse-item 
                                    v-for="version in selectedNode.software_versions" 
                                    :key="version.id"
                                    :name="version.id"
                                    class="version-collapse-item"
                                >
                                    <template #title>
                                        <div class="collapse-title">
                                            <div class="title-left">
                                                <el-icon><Monitor /></el-icon>
                                                <span class="version-name">{{ version.name }}</span>
                                                <el-tag size="small" type="success">{{ version.num }}</el-tag>
                                            </div>
                                            <div class="title-right">
                                            </div>
                                        </div>
                                    </template>
                                    
                                    <div class="version-content">
                                        <el-row :gutter="16">
                                            <el-col :span="12">
                                                <div class="info-group">
                                                    <label>版本信息</label>
                                                    <p><strong>版本号:</strong> {{ version.num }}</p>
                                                    <p><strong>类型:</strong> {{ version.type_name }}</p>
                                                </div>
                                            </el-col>
                                            <el-col :span="12">
                                                <div class="info-group">
                                                    <label>时间信息</label>
                                                    <p><strong>计划发布:</strong> {{ formatDate(version.planReleaseTime) }}</p>
                                                    <p><strong>实际发布:</strong> {{ formatDate(version.realReleaseTime) }}</p>
                                                </div>
                                            </el-col>
                                        </el-row>

                                        <div v-if="version.description" class="info-group">
                                            <label>版本描述</label>
                                            <div v-html="version.description" class="desc-content"></div>
                                        </div>

                                        <div v-if="version.packageUrl" class="info-group">
                                            <label>包地址</label>
                                            <el-link :href="version.packageUrl" target="_blank" type="primary" class="package-link">
                                                <el-icon><Link /></el-icon>
                                                {{ version.packageUrl }}
                                            </el-link>
                                        </div>
                                    </div>
                                </el-collapse-item>
                            </el-collapse>
                        </div>

                        <!-- 子组件 -->
                        <div v-if="selectedNode.components?.length > 0" class="info-section">
                            <div class="section-header">
                                <h4>
                                    <el-icon><Operation /></el-icon>
                                    子组件 ({{ selectedNode.components.length }})
                                </h4>
                            </div>
                            <div class="sub-components-grid">
                                <div 
                                    v-for="component in selectedNode.components" 
                                    :key="component.id"
                                    class="sub-component-card"
                                    @click="selectNode(component)"
                                >
                                    <div class="component-header">
                                        <el-icon><Cpu /></el-icon>
                                        <span class="component-name">{{ component.name }}</span>
                                    </div>
                                    <div class="component-info">
                                        <div class="component-stats">
                                            <span class="stat-item">
                                                <el-icon><Cpu /></el-icon>
                                                {{ component.hardware_versions?.length || 0 }}
                                            </span>
                                            <span class="stat-item">
                                                <el-icon><Monitor /></el-icon>
                                                {{ component.software_versions?.length || 0 }}
                                            </span>
                                            <span class="stat-item">
                                                <el-icon><Operation /></el-icon>
                                                {{ component.components?.length || 0 }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 无版本信息提示 -->
                        <div v-if="!selectedNode.hardware_versions?.length && !selectedNode.software_versions?.length && !selectedNode.components?.length" class="info-section">
                            <el-empty description="该组件暂无版本信息和子组件" />
                        </div>
                    </div>

                    <!-- 未选择节点时的提示 -->
                    <div v-else class="no-selection">
                        <el-empty description="请从左侧选择一个组件查看详情">
                            <template #image>
                                <el-icon size="60" color="#c0c4cc"><Operation /></el-icon>
                            </template>
                        </el-empty>
                    </div>
                </el-scrollbar>
            </el-card>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import { 
    Cpu, Monitor, Operation, Box, Location, Download, InfoFilled, 
    DataAnalysis, Link, Expand, Fold 
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import http from '@/utils/http/http.js';

// Props
const props = defineProps({
    r_id: {
        type: [String, Number],
        required: true
    }
});

// 响应式数据
const productData = ref({});
const selectedNode = ref(null);
const treeRef = ref(null);
const isAllExpanded = ref(false);
const hardwareActiveNames = ref([]);
const softwareActiveNames = ref([]);
const hardwareExpanded = ref(false);
const softwareExpanded = ref(false);

// 树形组件配置
const treeProps = {
    children: 'components',
    label: 'name'
};

// 计算属性
const treeData = computed(() => {
    if (!productData.value.id) return [];
    return [productData.value];
});

// 监听选中节点变化，重置折叠状态
watch(selectedNode, () => {
    hardwareActiveNames.value = [];
    softwareActiveNames.value = [];
    hardwareExpanded.value = false;
    softwareExpanded.value = false;
});

// 方法
const handleNodeClick = (data) => {
    selectedNode.value = data;
    console.log('选中节点:', data);
};

const selectNode = (node) => {
    selectedNode.value = node;
    nextTick(() => {
        treeRef.value?.setCurrentKey(node.id);
    });
};

const expandAll = () => {
    if (isAllExpanded.value) {
        const allNodes = treeRef.value?.store.nodesMap;
        for (let nodeId in allNodes) {
            allNodes[nodeId].expanded = false;
        }
    } else {
        const allNodes = treeRef.value?.store.nodesMap;
        for (let nodeId in allNodes) {
            allNodes[nodeId].expanded = true;
        }
    }
    isAllExpanded.value = !isAllExpanded.value;
};

const toggleVersionsExpand = (type) => {
    if (type === 'hardware') {
        if (hardwareExpanded.value) {
            hardwareActiveNames.value = [];
        } else {
            hardwareActiveNames.value = selectedNode.value.hardware_versions.map(v => v.id);
        }
        hardwareExpanded.value = !hardwareExpanded.value;
    } else if (type === 'software') {
        if (softwareExpanded.value) {
            softwareActiveNames.value = [];
        } else {
            softwareActiveNames.value = selectedNode.value.software_versions.map(v => v.id);
        }
        softwareExpanded.value = !softwareExpanded.value;
    }
};

const getStateType = (state) => {
    const stateMap = {
        'HAVE_TEST': 'success',
        'NO_TEST': 'warning',
        'TESTING': 'primary',
        'TEST_FAIL': 'danger'
    };
    return stateMap[state] || 'info';
};

const getStateText = (state) => {
    const textMap = {
        'HAVE_TEST': '已测试',
        'NO_TEST': '未测试',
        'TESTING': '测试中',
        'TEST_FAIL': '测试失败'
    };
    return textMap[state] || state;
};

const formatDate = (dateStr) => {
    if (!dateStr) return '未设置';
    return new Date(dateStr).toLocaleDateString('zh-CN');
};

const formatDateTime = (dateStr) => {
    if (!dateStr) return '未设置';
    return new Date(dateStr).toLocaleString('zh-CN');
};

const exportInfo = () => {
    ElMessage.success('导出功能开发中...');
};

// 初始化数据
onMounted(() => {
    http.get(`/test_products/${props.r_id}`).then(res => {
        let data = res.data.data;
        productData.value = data;

        nextTick(() => {
            selectedNode.value = data;
            treeRef.value?.setCurrentKey(data.id);
        });
    });
});
</script>

<style lang="scss" scoped>
.product-detail {
    display: flex;
    height: calc(100vh - 120px);
    gap: 16px;
    padding: 20px;
    background: #f5f7fa;

    .left-panel {
        width: 320px;
        flex-shrink: 0;

        .tree-card {
            height: 100%;
            border-radius: 8px;

            .tree-header {
                display: flex;
                justify-content: space-between;
                align-items: center;

                h3 {
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    color: #333;
                    font-size: 16px;
                }

                .tree-actions {
                    .el-button {
                        padding: 4px 8px;
                    }
                }
            }

            .product-tree {
                :deep(.el-tree-node__content) {
                    height: 40px;
                    border-radius: 6px;
                    margin: 2px 0;
                    transition: all 0.3s;

                    &:hover {
                        background: #f0f9ff;
                    }
                }

                :deep(.el-tree-node.is-current > .el-tree-node__content) {
                    background: #e1f5fe;
                    color: #1976d2;
                    font-weight: 500;
                }

                .tree-node {
                    width: 100%;

                    .node-content {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        flex: 1;

                        .node-icon {
                            color: #409eff;
                            font-size: 16px;
                        }

                        .node-name {
                            font-weight: 500;
                            color: #333;
                            flex: 1;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }

                        .node-badges {
                            display: flex;
                            align-items: center;
                            gap: 4px;

                            .version-badge {
                                :deep(.el-badge__content) {
                                    font-size: 10px;
                                    padding: 0 4px;
                                }

                                .el-icon {
                                    font-size: 10px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .right-panel {
        flex: 1;
        overflow: hidden;

        .detail-card {
            height: 100%;
            border-radius: 8px;

            .detail-header {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .header-left {
                    display: flex;
                    align-items: center;
                    gap: 12px;

                    h3 {
                        margin: 0;
                        color: #333;
                        font-size: 18px;
                    }
                }

                .header-right {
                    .el-button {
                        border-radius: 6px;
                    }
                }
            }

            .detail-content {
                padding: 0 4px;

                .info-section {
                    margin-bottom: 24px;
                    background: white;
                    border-radius: 8px;
                    padding: 16px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

                    .section-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 16px;

                        h4 {
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            margin: 0;
                            color: #333;
                            font-size: 16px;
                            font-weight: 600;
                        }
                    }

                    .custom-descriptions {
                        :deep(.el-descriptions__label) {
                            font-weight: 500;
                            background: #fafafa;
                        }
                    }

                    .stats-grid {
                        display: grid;
                        grid-template-columns: repeat(3, 1fr);
                        gap: 16px;

                        .stat-card {
                            display: flex;
                            align-items: center;
                            padding: 16px;
                            border-radius: 8px;
                            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);

                            &.hardware {
                                background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
                                .stat-icon { color: #3b82f6; }
                            }

                            &.software {
                                background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
                                .stat-icon { color: #22c55e; }
                            }

                            &.components {
                                background: linear-gradient(135deg, #fef7ff 0%, #fae8ff 100%);
                                .stat-icon { color: #a855f7; }
                            }

                            .stat-icon {
                                font-size: 24px;
                                margin-right: 12px;
                            }

                            .stat-content {
                                .stat-number {
                                    font-size: 24px;
                                    font-weight: 600;
                                    color: #1f2937;
                                }

                                .stat-label {
                                    font-size: 12px;
                                    color: #6b7280;
                                }
                            }
                        }
                    }

                    .version-collapse {
                        :deep(.el-collapse-item__header) {
                            border: none;
                            background: #f8fafc;
                            border-radius: 6px;
                            margin-bottom: 8px;
                            padding: 0 16px;
                        }

                        :deep(.el-collapse-item__content) {
                            padding: 16px;
                            background: white;
                            border-radius: 6px;
                            border: 1px solid #e5e7eb;
                        }

                        .collapse-title {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            width: calc(100% - 32px);

                            .title-left {
                                display: flex;
                                align-items: center;
                                gap: 8px;

                                .version-name {
                                    font-weight: 500;
                                    color: #333;
                                }
                            }
                        }

                        .version-content {
                            .info-group {
                                margin-bottom: 16px;

                                &:last-child {
                                    margin-bottom: 0;
                                }

                                label {
                                    display: block;
                                    font-weight: 500;
                                    color: #374151;
                                    margin-bottom: 8px;
                                    font-size: 14px;
                                }

                                p {
                                    margin: 4px 0;
                                    font-size: 13px;
                                    color: #6b7280;
                                }

                                .desc-content {
                                    background: #f9fafb;
                                    padding: 12px;
                                    border-radius: 6px;
                                    font-size: 13px;
                                    white-space: pre-wrap;
                                    border: 1px solid #e5e7eb;
                                }

                                .package-link {
                                    word-break: break-all;
                                    font-size: 13px;
                                }
                            }
                        }
                    }

                    .sub-components-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                        gap: 12px;

                        .sub-component-card {
                            background: white;
                            border: 1px solid #e5e7eb;
                            border-radius: 8px;
                            padding: 16px;
                            cursor: pointer;
                            transition: all 0.3s;

                            &:hover {
                                border-color: #3b82f6;
                                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
                                transform: translateY(-2px);
                            }

                            .component-header {
                                display: flex;
                                align-items: center;
                                gap: 8px;
                                margin-bottom: 12px;

                                .el-icon {
                                    color: #3b82f6;
                                }

                                .component-name {
                                    font-weight: 500;
                                    color: #333;
                                }
                            }

                            .component-info {
                                display: flex;
                                justify-content: space-between;
                                align-items: center;

                                .component-stats {
                                    display: flex;
                                    gap: 12px;

                                    .stat-item {
                                        display: flex;
                                        align-items: center;
                                        gap: 4px;
                                        font-size: 12px;
                                        color: #6b7280;

                                        .el-icon {
                                            font-size: 12px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .no-selection {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
            }
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .product-detail {
        .left-panel {
            width: 280px;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr) !important;
        }

        .sub-components-grid {
            grid-template-columns: 1fr !important;
        }
    }
}

@media (max-width: 768px) {
    .product-detail {
        flex-direction: column;
        height: auto;
        padding: 12px;

        .left-panel {
            width: 100%;
            height: 300px;
        }

        .right-panel {
            height: 600px;
        }

        .stats-grid {
            grid-template-columns: 1fr !important;
        }
    }
}
</style>