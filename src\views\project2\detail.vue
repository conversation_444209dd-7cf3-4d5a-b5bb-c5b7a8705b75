<template>

    <div>

        <div v-if="isEmpty" style="height: calc(-150px + 100vh);">
            <div class="el-empty">
                <el-image :src="emptyImage" style="width: 160px;" />
                <div style="margin-top: var(--el-empty-description-margin-top);">
                    <p style="color: var(--el-text-color-secondary); font-size: var(--el-font-size-base);">请选择项目查看</p>
                </div>
            </div>
        </div>

        <div v-else>
            <div style="display: flex; align-items: center;">
                <h3>{{ title }}</h3>
            </div>
            <div class="op-container">
                <span style="font-weight:bold">主导人：{{ p_data?.manager?.realName }}</span>
                <!-- <el-button text bg :icon="Setting" @click="handleMsgConf">消息配置</el-button>
                <el-button text bg :icon="Setting" @click="handleProjectConf">项目配置</el-button> -->
            </div>
            <div class="c-container">

                <el-tabs v-model="activeName">
                    <!-- <el-tab-pane label="基本信息" name="1">
                        <el-form>
                            <el-form-item label="主导人：">
                                <span>{{ p_data?.manager?.realName }}</span>
                            </el-form-item>
                        </el-form>
                    </el-tab-pane> -->
                    <el-tab-pane label="用例统计" name="2">
                        <TestCaseStats />
                    </el-tab-pane>
                    <el-tab-pane label="配置信息" name="3">
                        <el-button text bg :icon="Setting" @click="handleMsgConf">消息配置</el-button>
                        <el-button text bg :icon="Setting" @click="handleProjectConf">项目配置</el-button>
                        <el-form>
                            <el-form-item label="消息推送人：">
                                <div v-if="msg_data?.related_people" style="display: flex; align-items: center; gap: 8px;">
                                    <div v-for="item in msg_data.related_people" :key="item.id" style="display: flex; align-items: center; cursor: pointer;" @click="openFeishuChat(item.openId)">
                                        <el-avatar :size="25" :src="item.avatar" />&nbsp;&nbsp;
                                        <span>{{ item.name }}</span>
                                    </div>
                                </div>
                            </el-form-item>
                            <el-form-item label="消息推送时间：">
                                <span>{{ msg_data?.msg_effective_time_start }} - {{ msg_data?.msg_effective_time_end
                                    }}</span>
                            </el-form-item>
                            <el-form-item label="DBC文件:">
                                <el-link :underline="false" type="primary" :href="dbc_path" v-if="dbc_path">下载</el-link>
                            </el-form-item>
                            <el-form-item label="cfg路径：">
                                <span>{{ pExtraConfig && pExtraConfig.canoe_cfg_path }}</span>
                            </el-form-item>
                            <el-form-item label="功能寻址ID：">
                                <span>{{ pExtraConfig && pExtraConfig.func_address_id }}</span>
                            </el-form-item>
                            <el-form-item label="物理寻址ID：">
                                <span>{{ pExtraConfig && pExtraConfig.phy_address_id }}</span>
                            </el-form-item>
                            <el-form-item label="响应ID：">
                                <span>{{ pExtraConfig && pExtraConfig.response_id }}</span>
                            </el-form-item>
                            <!-- 新增产品类型 -->
                            <el-form-item label="产品类型">
                                <el-tag v-for="pt in product_type_names">{{ pt }}</el-tag>
                            </el-form-item>

                        </el-form>
                    </el-tab-pane>
                    <el-tab-pane label="项目成员" name="4">
                        <el-table height="500" :data="tableData" stripe border>

                            <el-table-column label="姓名" width="150" align="left">
                                <template #default="{ row }">
                                <!-- 确保 row.userInfo && row.userInfo.openId 存在 -->
                                <div
                                    v-if="row.userInfo && row.userInfo.openId"
                                    style="display: flex; align-items: center; justify-content: flex-start; cursor: pointer;"
                                    @click="openFeishuChat(row.userInfo.openId)"
                                >
                                    <el-avatar :size="25" :src="row.userInfo.avatar" />&nbsp;&nbsp;
                                    {{ row.userInfo.realName }}
                                </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="userInfo.jobTitle" label="行政角色" min-width="200"
                                align="center"></el-table-column>
                            <el-table-column prop="roleName" label="项目角色" min-width="150"
                                align="center"></el-table-column>
                            <el-table-column prop="progress" label="系统角色" min-width="150"
                                align="center"></el-table-column>
                            <el-table-column prop="projectStatus" label="角色描述" min-width="200"
                                align="center"></el-table-column>
                            <el-table-column prop="joinTime" label="加入时间" min-width="180"
                                align="center"></el-table-column>
                            <el-table-column prop="updateTime" label="更新时间" min-width="180"
                                align="center"></el-table-column>
                            <el-table-column prop="description" label="更新人" min-width="150"
                                align="center"></el-table-column>
                            <el-table-column prop="description" label="状态" min-width="100"
                                align="center"></el-table-column>

                        </el-table>
                    </el-tab-pane>
                    <el-tab-pane label="功能模块" name="5">
                        <Modules />
                    </el-tab-pane>
                    <el-tab-pane label="点检项" name="6">
                        <InspectionItems />
                    </el-tab-pane>
                    <el-tab-pane label="版本提测率" name="7">
                        <ProductVersionTr />
                    </el-tab-pane>
                </el-tabs>

            </div>

            <el-dialog v-if="dialogAddMsgVisible" v-model="dialogAddMsgVisible" title="消息推送配置" width="800"
                :close-on-click-modal="false">
                <MsgAdd :name="p_data?.name" :number="p_data?.projectCode" :related_people="related_people"
                    :msg_data="msg_data" @affirm="onAddAffirm" @cancel="onAddCancel" />
            </el-dialog>

            <el-dialog v-if="dialogConfVisible" v-model="dialogConfVisible" title="项目配置" width="800"
                :close-on-click-modal="false">
                <Conf :name="p_data?.name" :number="p_data?.projectCode" :msg_data="msg_data" :configs="pExtraConfig"  @affirm="onConfAffirm"
                    @cancel="onConfCancel" />
            </el-dialog>
        </div>
    </div>

</template>

<script setup>
import { ref, watch, computed, provide, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import { Setting } from '@element-plus/icons-vue';
import MsgAdd from '@/views/project/add.vue';
import { useProjectStore } from '@/stores/project.js';
import emptyImage from '@/assets/images/empty.png';
import Conf from './Conf.vue';
import TestCaseStats from './testCaseStats.vue';
import Modules from './modules.vue'
import InspectionItems from './inspection-items.vue'
import ProductVersionTr from './product-version-tr.vue';
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/projects/detail', '项目信息');

const isEmpty = ref(true);
const activeName = ref('2');
const title = ref(null);
const tableData = ref([]);
const p_data = ref(null);
const msg_data = ref(null);
const related_people = ref([]);
const projectStore = useProjectStore();
const pExtraConfig = ref(null);
const project_number = ref(null);
// add
const product_type_names = computed(() => {
    if (pExtraConfig.value && pExtraConfig.value.product_types) {
        return pExtraConfig.value.product_types.map(item => product_type_map.value[item]);
    }
})

const dialogAddMsgVisible = ref(false);
const dialogConfVisible = ref(false);

const product_type_map = ref({});

const dbc_path = computed(() => {
    if (pExtraConfig.value?.dbc_path || null) {
        return import.meta.env.VITE_BASE_URL + pExtraConfig.value.dbc_path;
    }
});


provide('project_number', project_number);


const handleMsgConf = () => {
    dialogAddMsgVisible.value = true;
}

const onAddAffirm = () => {
    dialogAddMsgVisible.value = false;

    update_msg_data();
}

const onAddCancel = () => {
    dialogAddMsgVisible.value = false;
}

const handleProjectConf = () => {
    dialogConfVisible.value = true;
}

const onConfAffirm = () => {
    dialogConfVisible.value = false;

    update_msg_data();
}

const onConfCancel = () => {
    dialogConfVisible.value = false;
}

// add
onMounted(() => {
    http.get(`/product_types`).then(res => {
        res.data.data.results.forEach(item => {
            product_type_map.value[item.id] = item.name;
        });
    });
});


function update_msg_data() {
    http.get(`/projects/detail/by_number`, { params: { number: p_data.value.projectCode } }).then(res => {
        msg_data.value = res.data.data || null;
        pExtraConfig.value = res.data.data?.configs || null;
    });
}

watch(() => projectStore.project_info.id, (newVal, oldVal) => {
    if (newVal) {
        isEmpty.value = false;

        http.get(`/projects/p/${newVal}`).then(res => {
            let data = res.data.data;
            p_data.value = data;
            title.value = `${data.name}【${data.projectCode}】`;
            tableData.value = data.projectMemberPOList;
            related_people.value = data.projectMemberPOList.filter(item => item.roleName == "硬件工程师" || item.roleName == "软件工程师" || item.roleName == "测试工程师").map(item => {
                return { email: item.userInfo?.username, name: item.userInfo?.realName, avatar: item.userInfo?.avatar, openId: item.userInfo?.openId };
            });

            project_number.value = data.projectCode;

            update_msg_data();

        });
    } else {
        isEmpty.value = true;
    }
}, { immediate: true });

function openFeishuChat(openId) {
  // 构造飞书聊天窗口的URL
  const chatUrl = `https://applink.feishu.cn/client/chat/open?openId=${openId}`;
  
  // 直接打开飞书聊天窗口
  window.open(chatUrl, "_blank");
}
</script>


<style lang="scss" scoped>
.c-container {
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
}

.step-container {
    padding: 20px;
    background-color: #f5f5f5;
}

.el-popper {
    font-size: 14px;
    max-width: 300px;
    padding: 6px 12px;
}

.test-case-form {
    padding: 0 20px;

    .el-form-item {
        margin-bottom: 0;
    }
}

:deep(.el-collapse-item__arrow) {
    order: -1;
    margin: 0 8px 0 0;
}

:deep(.el-collapse-item__header) {
    font-size: 16px;
}

.op-container {
    display: flex;
    justify-content: flex-start;
    padding: 0 20px 10px 0;
}

.el-form-item {
    margin-bottom: 0;
}

.el-empty {
    height: 100%;
    align-items: center;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: var(--el-empty-padding);
    text-align: center;
}

h3 {

font-size: 18px;

}
</style>