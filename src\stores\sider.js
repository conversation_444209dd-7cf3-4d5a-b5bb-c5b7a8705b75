import { defineStore } from 'pinia'

export const useSiderStore = defineStore('sider', {
    state: () => ({
        isCollapse: false,
        oldActiveIndex: null,
        activeIndex: null,
        activeIndex2: null,
        t: false,
    }),
    actions: {
        toggle() {
            this.isCollapse = !this.isCollapse
        },
        setActiveIndex(index) {
            this.activeIndex = index
        },
        setActiveIndex2(index) {
            this.activeIndex2 = index
        },
    },
    persist: {
        key: 'sider',
        storage: sessionStorage
      }
  })