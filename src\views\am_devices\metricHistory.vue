<template>
    <!-- 时间范围筛选 -->
    <div class="filter-row">
        <div class="time-selector">
            <el-radio-group v-model="timeRange" size="default" @change="handleTimeRangeChange">
                <el-radio-button label="24h">近24小时</el-radio-button>
                <el-radio-button label="48h">近48小时</el-radio-button>
                <el-radio-button label="72h">近72小时</el-radio-button>
                <el-radio-button label="custom">自定义</el-radio-button>
            </el-radio-group>

            <el-date-picker v-if="timeRange === 'custom'" v-model="customTimeRange" type="datetimerange"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss" @change="handleCustomTimeChange" class="custom-date-picker" />
        </div>

        <el-button size="default" type="primary" @click="fetchHistoryData">
            <el-icon>
                <Refresh />
            </el-icon> 刷新
        </el-button>
    </div>

    <!-- 表格 -->
    <el-table :data="historyData" stripe border style="width: 100%; margin-top: 15px" height="450px">
        <el-table-column prop="cur_time" label="时间" width="250" align="center" />
        <el-table-column label="状态" width="100" align="center">
            <template #default="{ row }">
                <el-tag :type="row.status === 0 ? 'success' : 'danger'">
                    {{ row.status === 0 ? '正常' : '异常' }}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="value" label="值" width="100" align="center" />
        <el-table-column prop="extra_info" label="额外信息" min-width="200" align="center" />
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="historyTotal" background
            @change="onPageChange" />
    </div>

</template>


<script setup>
import { ref, onMounted } from 'vue';
import { Refresh } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import http from '@/utils/http/http2.js';

const props = defineProps({
    meta: {
        type: Object,
        required: true
    },
    metric: {
        type: Object,
        required: true
    },
});


const historyData = ref([]);
const historyTotal = ref(0);
const timeRange = ref('24h');
const customTimeRange = ref([]);

const form = ref({
    page: 1,
    pagesize: 15,
    start_time: null,
    end_time: null,
    exec_id: props.meta.exec_id,
    device_id: props.meta.device_id,
    object_type: 'metrics',
    object_id: props.metric.code,
});

const formatTime = (time) => {
    if (!time) return '--';
    return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
};

const getTimeRange = () => {
    // 如果是自定义时间范围且已经设置了时间
    if (timeRange.value === 'custom' && customTimeRange.value && customTimeRange.value.length === 2) {
        return {
            start: customTimeRange.value[0],
            end: customTimeRange.value[1]
        };
    }

    // 预设时间范围
    const end = dayjs();
    let start;

    switch (timeRange.value) {
        case '24h':
            start = end.subtract(24, 'hour');
            break;
        case '48h':
            start = end.subtract(48, 'hours');
            break;
        case '72h':
            start = end.subtract(72, 'hours');
            break;
        default:
            start = end.subtract(24, 'hours');
    }

    return {
        start: start.format('YYYY-MM-DD HH:mm:ss'),
        end: end.format('YYYY-MM-DD HH:mm:ss')
    };
};

const onPageChange = () => {
    fetchHistoryData();
};

const handleCustomTimeChange = () => {
    if (customTimeRange.value && customTimeRange.value.length === 2) {
        fetchHistoryData();
    }
};

const handleTimeRangeChange = () => {
    if (timeRange.value !== 'custom') {
        fetchHistoryData();
    }
};

const fetchHistoryData = () => {

    const { start, end } = getTimeRange();
    form.value.start_time = start;
    form.value.end_time = end;

    // console.log('请求参数:', form.value);

    http.get(`/monitor/events`, {
        params: form.value,
    }).then(res => {
        let results = res.data.data.results || [];
        historyData.value = results.map(item => ({
            ...item.after_value,
            cur_time: formatTime(item.cur_time),
        }));
        historyTotal.value = res.data.data.count || 0;
    }).catch(err => {
        console.error('获取历史数据失败:', err);
    })
}

onMounted(() => {
    fetchHistoryData();
});

</script>

<style scoped>
.filter-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.time-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.custom-date-picker {
    width: 400px;
}

.pagination {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
}
</style>