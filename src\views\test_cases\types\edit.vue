<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="类型名称" prop="name">
                <el-input v-model="form.name"></el-input>
            </el-form-item>

            <el-form-item label="类型编号" prop="number">
                <el-input v-model="form.number"></el-input>
            </el-form-item>

            <el-form-item label="类型描述">
                <el-input v-model="form.desc"></el-input>
            </el-form-item>

            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onConfirm">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

import http from '@/utils/http/http.js';

const props = defineProps({
    r_id: {
        type: Number,
        required: true,
    },
});

const emit = defineEmits(['confirm', 'cancel'])

const formRef = ref(null);

const form = ref({
    name: '',
    number: '',
    desc: '',
});

const rules = ref({
    name: [
        { required: true, message: '请输入类型名称', trigger: 'blur' },
    ],
    number: [
        { required: true, message: '请输入类型编号', trigger: 'blur' },
    ],
});

const onConfirm = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            http.put(`/test_m/test_case_types/${props.r_id}`, form.value).then(res => {
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '编辑失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

function onCancel() {
    emit('cancel');
};

onMounted(() => {
    if (props.r_id) {
        http.get(`/test_m/test_case_types/${props.r_id}`).then(res => {
            form.value.name = res.data.data.name;
            form.value.number = res.data.data.number;
            form.value.desc = res.data.data.desc; 
        });
    };
});

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>