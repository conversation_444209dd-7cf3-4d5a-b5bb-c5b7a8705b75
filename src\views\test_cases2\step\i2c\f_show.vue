<template>
    <el-form>

        <el-form-item label="指令：">
            <span>{{ params.cmd }}</span>
        </el-form-item>
        <el-form-item label="接收报文：">
            <span>{{ params.recv_msg }}</span>
        </el-form-item>

        <el-form-item label="判断条件：">
            <span>{{ params.option }}</span>
        </el-form-item>

        <el-form-item v-if="params.option == '等于'" label="精确值：">
            <span>{{ params.equal }}</span>
        </el-form-item>

        <template v-else-if="params.option == '范围'">
            <el-form-item label="最小值：">
                <span>{{ params.min }}</span>
            </el-form-item>
            <el-form-item label="最大值：">
                <span>{{ params.max }}</span>
            </el-form-item>
        </template>

        <el-form-item label="循环周期(ms)：">
            <span>{{ params.period }}</span>
        </el-form-item>

    </el-form>
</template>

<script setup>

import { computed } from 'vue';

const props = defineProps(
    {
        params: {
            type: Object,
            required: true,
        },
    }
);

const params = computed(() => props.params);

</script>

<style scoped>
.el-form-item {
    margin-bottom: 0;
}
</style>