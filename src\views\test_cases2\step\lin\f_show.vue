<template>
    <el-form>
        <el-form-item label="发送ID：">
            <span>{{ params.send_id }}</span>
        </el-form-item>

        <el-form-item label="发送报文：">
            <span>{{ params.send_msg }}</span>
        </el-form-item>

        <el-form-item label="检查数字类型：">
            <span>{{ params.check_number_type }}</span>
        </el-form-item>

        <el-form-item label="方向：">
            <span>{{ params.direction }}</span>
        </el-form-item>

        <el-form-item label="循环周期(ms)：" prop="period">
            <span>{{ params.period }}</span>
        </el-form-item>

        <el-form-item label="通道：">
            <span>通道{{ params.channel }}</span>
        </el-form-item>

        <el-form-item label="模式：">
            <span>{{ params.mode }}</span>
        </el-form-item>

    </el-form>
</template>

<script setup>

import { computed } from 'vue';

const props = defineProps(
    {
        params: {
            type: Object,
            required: true,
        },
    }
);

const params = computed(() => props.params);

</script>

<style scoped>
.el-form-item {
    margin-bottom: 0;
}
</style>