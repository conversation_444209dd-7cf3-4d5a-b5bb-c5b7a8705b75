<template>
    <div style="height: calc(-150px + 100vh);">
            <div class="el-empty">
                <el-image :src="emptyImage" style="width: 160px;" />
                <div style="margin-top: var(--el-empty-description-margin-top);">
                    <p style="color: var(--el-text-color-secondary); font-size: var(--el-font-size-base);">请选择(其他)项目查看</p>
                </div>
            </div>
        </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';
import emptyImage from '@/assets/images/empty.png';
// 弹框显示状态
const dialogVisible = ref(false);

// 确认按钮处理
const handleConfirm = () => {
  dialogVisible.value = false;
};

// 组件挂载时显示弹框
onMounted(() => {
  dialogVisible.value = true;
});
</script>


<style>

.el-empty {
    height: 100%;
    align-items: center;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: var(--el-empty-padding);
    text-align: center;
}

</style>