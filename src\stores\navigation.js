// stores/navigation.js
import { defineStore } from 'pinia'

export const useNavigationStore = defineStore('navigation', {
  state: () => ({
    activeCodeModule: null
  }),
  actions: {
    setActiveCodeModule(moduleName) {
      this.activeCodeModule = moduleName
    },
    clearActiveCodeModule() {
      this.activeCodeModule = null
    }
  },
  persist: true // 使用pinia-plugin-persistedstate实现持久化
})