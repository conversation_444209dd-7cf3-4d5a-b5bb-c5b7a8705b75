<template>
    <div>
        <div class="status-update">
            <el-form label-width="auto" class="form">
                <el-form-item label="当前状态:">
                    <span>{{ getStatusText(status) }}</span>
                    <el-input style="display: none;" />
                </el-form-item>
                <el-form-item label="目标状态:">
                    <el-select v-model="targetStatus" placeholder="请选择状态">
                        <el-option label="待压测" :value=0></el-option>
                        <el-option label="压测中" :value=1></el-option>
                        <el-option label="已复现" :value=2></el-option>
                        <el-option label="已解决" :value=3></el-option>
                        <el-option label="已关闭" :value=4></el-option>
                        <el-option label="未复现" :value=5></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
        </div>

        <div style="display: flex; justify-content: end;">
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onConfirm">确认</el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import http from '@/utils/http/http.js';
import { ElMessageBox } from 'element-plus';

const props = defineProps({
    versionInfo: {
        type: Object,
        required: true,
    },
});

const statusMap = {
    0: '待压测',
    1: '压测中',
    2: '已复现',
    3: '已解决',
    4: '已关闭',
    5: '未复现',
};

// 在使用 statusMap 时，增加对空值的判断
function getStatusText(status) {
    if (status === 0 || status === null || status === undefined || status === '') {
        return '待压测';
    }
    return statusMap[status] || '未知状态';
}

const status = computed(() => props.versionInfo.status);
const plan_type = computed(() => props.versionInfo.plan_type);
const targetStatus = ref('');

const emit = defineEmits(['confirm', 'cancel']);

function onCancel() {
    emit('cancel');
};

function onConfirm() {
    if (targetStatus.value === '') {
        ElMessageBox.alert('请选择目标状态', '提示', {
            confirmButtonText: '确定',
            type: 'warning',
        });
        return;
    }
    http.post('/stress_tests/update_status', { id: props.versionInfo.id, status: targetStatus.value }).then(res => {
        emit('confirm');
    }).catch(err => {
        ElMessageBox.alert(err.response.data.msg, '错误', {
            confirmButtonText: '确定',
            type: 'error',
        });
    });
};
</script>

<style scoped>
.status-update {
    display: flex;
    justify-content: center;
    align-items: center;
}

.form {
    padding: 20px;
    width: 100%;
}
</style>