# Toolbar 组件功能迁移测试文档

## 迁移完成的功能

### 1. 数据管理
- ✅ 项目相关变量 (`project_code`, `project_name`)
- ✅ 表单数据 (`form.gitlab`, `form.project_branch`)
- ✅ 选项数据 (`spaceOptions`, `branchOptions`)
- ✅ SDK版本 (`sdkVersion`)
- ✅ 权限控制 (`branch_create`)

### 2. HTTP 请求函数
- ✅ `get_space()` - 获取仓库列表
- ✅ `get_branch()` - 获取分支列表  
- ✅ `submit_branch_info()` - 提交分支信息并获取SDK版本

### 3. 监听器
- ✅ 项目信息变化监听
- ✅ 仓库信息变化监听
- ✅ 分支信息变化监听

### 4. 组件生命周期
- ✅ `onMounted` - 初始化数据和监听器
- ✅ Store 数据恢复

### 5. 事件通信
- ✅ `data-loaded` - 通知主组件数据加载完成
- ✅ `project-changed` - 通知主组件项目变更

## 主组件简化

### 移除的功能
- ❌ 工具栏相关的 props 传递
- ❌ HTTP 请求函数
- ❌ 项目和分支监听器
- ❌ Store 相关逻辑
- ❌ 功能按钮处理函数

### 保留的功能
- ✅ 芯片配置相关逻辑
- ✅ 窗口大小变化处理
- ✅ 基本的组件结构

## 测试要点

1. **初始化流程**
   - 组件加载时是否正确获取项目信息
   - 是否按顺序执行：获取仓库 → 获取分支 → 提交分支信息

2. **数据流**
   - 项目切换时是否正确清空并重新获取数据
   - 仓库选择时是否正确获取对应分支
   - 分支选择时是否正确提交信息

3. **权限控制**
   - 按钮是否根据 `branch_create` 状态正确启用/禁用

4. **错误处理**
   - HTTP 请求失败时是否有正确的错误提示
   - 空数据情况是否有合适的处理

## 预期行为

1. 页面加载时显示 TipDialog
2. 项目选择后，Toolbar 自动获取仓库列表
3. 仓库选择后，自动获取分支列表
4. 分支选择后，自动提交信息并获取SDK版本
5. 数据加载完成后，隐藏 TipDialog，显示主界面
6. 按钮根据权限状态正确显示
