<template>
    <div class="n-container">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/test_records' }">测试记录</el-breadcrumb-item>
            <el-breadcrumb-item
                :to="{ path: '/test_records/items', query: { id: test_record_item.raw?.test_record_id } }">记录项</el-breadcrumb-item>
            <el-breadcrumb-item>详情</el-breadcrumb-item>
        </el-breadcrumb>
    </div>

    <div class="header-container">
        <h2>{{ test_record_item.raw?.test_case_name }}</h2>
        <div>
            <span v-if="!jump_flag" style="margin-right: 10px;">{{ current_index + ' / ' + count }}</span>
            <el-button v-if="!jump_flag && current_index > 1" size="small" @click="onLast">上一个</el-button>
            <el-button v-if="!jump_flag && current_index < count" size="small" @click="onNext">下一个</el-button>
            <el-button v-if="!jump_flag" size="small" @click="onBack">返回</el-button>
        </div>
    </div>
    <div class="tool-container">
        <el-button :disabled="!(test_record_item.result === null)" text bg icon="Pointer"
            @click="handleDecide">判定</el-button>
        <el-button :disabled="!(test_record_item.result === false && test_record_item.status === 0)" text bg
            icon="Pointer" @click="handlePush">上报问题</el-button>
        <el-button :disabled="!(test_record_item.result === false && test_record_item.status === 0)" text bg
            icon="Pointer" @click="handleFasleAlarm">误判</el-button>
    </div>

    <el-collapse v-model="activeNames">
        <el-collapse-item title="状态信息" name="1">
            <div style="max-width: 1200px; margin-left: 20px;">
                <el-row>
                    <el-col :span="12">
                        <el-form label-position="right">
                            <el-form-item label="测试结果：">
                                <el-tag v-if="test_record_item.result === null" type="warning">待判定</el-tag>
                                <el-tag v-else-if="test_record_item.result" type="success">PASS</el-tag>
                                <el-tag v-else type="danger">NG</el-tag>
                            </el-form-item>
                            <el-form-item label="处理状态：">
                                <el-tag v-if="test_record_item.status == 0" type="warning">待处理</el-tag>
                                <el-tag v-else-if="test_record_item.status == 1" type="success">已处理</el-tag>
                            </el-form-item>
                        </el-form>
                    </el-col>
                    <el-col :span="12">
                        <el-form label-position="right">
                            <el-form-item label="测试时间：">
                                <span>{{ test_record_item.raw?.test_time + " ~ " + test_record_item.raw?.end_time
                                    }}</span>
                            </el-form-item>
                            <el-form-item label="测试人员：">
                                <span>{{ test_record_item.tester_name }}</span>
                            </el-form-item>
                        </el-form>
                    </el-col>
                </el-row>
            </div>
        </el-collapse-item>
        <el-collapse-item title="用例信息" name="2">
            <div style="max-width: 1200px; margin-left: 20px;">
                <el-row>
                    <el-col :span="12">
                        <el-form label-position="right">
                            <el-form-item label="项目名称：">
                                <span>{{ test_case.project_name }}</span>
                            </el-form-item>
                            <el-form-item label="项目编号：">
                                <span>{{ test_case.project_number }}</span>
                            </el-form-item>
                            <el-form-item label="机台编号：">
                                <span>{{ test_record_item.raw?.machine_number }}</span>
                            </el-form-item>
                        </el-form>
                    </el-col>
                    <el-col :span="12">
                        <el-form label-position="right">
                            <el-form-item label="用例名称：">
                                <span>{{ test_case.name }}</span>
                            </el-form-item>
                            <el-form-item label="用例编号：">
                                <span>{{ test_case.number }}</span>
                            </el-form-item>
                            <el-form-item label="用例版本：">
                                <span>V{{ test_case.version }}.0</span>
                            </el-form-item>
                            <el-form-item label="用例状态：">
                                <el-tag v-if="test_case.status == 'PENDING'" type="primary">待评审</el-tag>
                                <el-tag v-else-if="test_case.status == 'REVIEWING'" type="warning">评审中</el-tag>
                                <el-tag v-else-if="test_case.status == 'APPROVED'" type="success">评审通过</el-tag>
                                <el-tag v-else-if="test_case.status == 'REJECTED'" type="danger">评审不通过</el-tag>
                                <el-tag v-else-if="test_case.status == 'DEPRECATED'" type="info">废弃</el-tag>
                                <el-tag v-else type="danger">未知</el-tag>
                            </el-form-item>
                        </el-form>
                    </el-col>
                </el-row>
            </div>
        </el-collapse-item>
        <el-collapse-item title="测试步骤详情" name="3">
            <div class="table-container">
                <el-table ref="stepTableRef" :data="stepData" stripe border style="width: 100%" row-key="id"
                    height="600">

                    <el-table-column label="序号" min-width="100" align="center">
                        <template #default="{ row, $index }">
                            {{ $index + 1 }}
                        </template>
                    </el-table-column>
                    <el-table-column label="步骤类型" min-width="120" align="center">
                        <template #default="{ row }">
                            <span v-if="row.step_type == 'CAN'">CAN报文</span>
                            <span v-else-if="row.step_type == 'LIN'">LIN报文</span>
                            <span v-else-if="row.step_type == 'I2C'">I2C指令</span>
                            <span v-else-if="row.step_type == 'CustomizeCMD'">自定义指令</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="步骤指令" prop="command" min-width="200" align="center"></el-table-column>
                    <el-table-column label="步骤参数" prop="params" min-width="400" align="center">
                    </el-table-column>
                    <el-table-column prop="expect" label="期望值" min-width="200" align="center"></el-table-column>
                    <el-table-column prop="step_actual" label="实测值" min-width="200" align="center"></el-table-column>
                    <el-table-column prop="step_result" label="测试结果" min-width="200" align="center"></el-table-column>
                    <el-table-column prop="start_time" label="开始时间" min-width="200" align="center"></el-table-column>
                    <el-table-column prop="end_time" label="结束时间" min-width="200" align="center"></el-table-column>

                </el-table>
            </div>
        </el-collapse-item>
        <el-collapse-item title="关联资料" name="4">
            <el-table ref="stepTableRef" :data="resources" stripe style="width: 100%" row-key="id" height="600">

                <el-table-column prop="resource_name" label="关联资源名称" width="200" align="center"></el-table-column>
                <el-table-column prop="resource_path" label="关联资源url" min-width="200" align="center">
                    <template #default="{ row }">
                        <el-link type="primary" :href="row.resource_path" target="_blank">{{ row.resource_path
                            }}</el-link>
                    </template>
                </el-table-column>

            </el-table>
        </el-collapse-item>
    </el-collapse>


    <el-dialog v-if="dialogDecideVisible" v-model="dialogDecideVisible" title="人工判定测试项结果" width="800"
        :close-on-click-modal="false" center>
        <Decide @pass="onDecidePass" @ng="onDecideNg" @cancel="onDecideCancel" :r_id="test_record_item_id" />
    </el-dialog>

    <el-dialog v-if="dialogFalseAlarmVisible" v-model="dialogFalseAlarmVisible" title="测试项结果误判" width="800"
        :close-on-click-modal="false">
        <FalseAlarm @cancel="onFalseAlarmCancel" @confirm="onFalseAlarmConfirm" :r_id="test_record_item_id" />
    </el-dialog>

    <el-dialog v-if="dialogPushVisible" v-model="dialogPushVisible" title="上报问题到产品开发平台" width="920"
        :close-on-click-modal="false">
        <IssuePush :r_id="test_record_item_id" @confirm="onPushConfirm" @cancel="onPushCancel" />
    </el-dialog>
</template>


<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import http from '@/utils/http/http.js';
import { useRouter, useRoute } from 'vue-router';
import Decide from './decide.vue';
import FalseAlarm from './falseAlarm.vue';
import IssuePush from './push.vue';

const dialogDecideVisible = ref(false);
const dialogFalseAlarmVisible = ref(false);
const dialogPushVisible = ref(false);
const activeNames = ref(['1', '2', '3', '4']);
const router = useRouter();
const route = useRoute();

const test_record_item = ref({});
const test_plan = ref(null);
const count = ref(parseInt(route.query.count));
const current_index = ref(parseInt(route.query.current));
const resources = ref([]);
const jump_flag = ref(false);

if (route.query.q) {
    var query = JSON.parse(route.query.q);
} else {
    var query = {};
    jump_flag.value = true;
}

const test_record_item_id = computed(() => {
    return route.params.id;
});

watch(test_record_item_id, () => {
    http.get(`/test_records/items/resources`, { params: { test_record_item_id: test_record_item_id.value } }).then(res => {
        console.log('resources', res.data.data.results);
        resources.value = res.data.data.results;
    });
}, { immediate: true });

const test_plan_id = computed(() => {
    return test_record_item.value.raw?.test_plan_id;
});
const test_sub_plan_id = computed(() => {
    return test_record_item.value.raw?.test_sub_plan_id;
});
const test_case_id = computed(() => {
    return test_record_item.value.raw?.test_case_id;
});
const stepData = computed(() => {
    return test_record_item.value.raw?.steps;
});
const test_case = computed(() => {
    if (test_plan.value && test_plan_id.value) {
        console.log('test_case', test_case);
        return test_plan.value.sub_plans[test_sub_plan_id.value].test_cases.find(item => item.id === test_case_id.value);
    } else {
        return {};
    }
});

function onLast() {
    if (current_index.value > 1) {
        let params = {
            ...query
        };
        params.page = current_index.value - 1;
        params.pagesize = 1;
        params.test_record_id = test_record_item.value.raw.test_record_id;
        http.get("/test_records/items", { params }).then(res => {
            if (res.data.data.count == count.value) {
                let id = res.data.data.results[0].id;
                router.push({ path: `/test_records/items/${id}`, query: { q: JSON.stringify(query), count: count.value, current: current_index.value - 1 } });
            } else {
                let id = res.data.data.results[0].id;
                router.push({ path: `/test_records/items/${id}`, query: { q: JSON.stringify(query), count: count.value - 1, current: current_index.value - 1 } });
            }
        });
    }
};

function onNext() {
    if (current_index.value < count.value) {
        let params = {
            ...query,
        };
        params.page = current_index.value + 1;
        params.pagesize = 1;
        params.test_record_id = test_record_item.value.raw.test_record_id;
        http.get("/test_records/items", { params }).then(res => {
            console.log(res.data.data.count, count.value);
            console.log(res.data.data);
            if (res.data.data.count == count.value) {
                let id = res.data.data.results[0].id;
                router.push({ path: `/test_records/items/${id}`, query: { q: JSON.stringify(query), count: count.value, current: current_index.value + 1 } });
            } else {
                current_index.value -= 1;
                count.value -= 1;
                onNext();
            }
        });
    }
};

function onBack() {
    router.push({ path: "/test_records/items/", query: { id: test_record_item.value.raw.test_record_id } });
};


function handleDecide() {
    dialogDecideVisible.value = true;
};

function onDecideCancel() {
    dialogDecideVisible.value = false;
};

function onDecidePass() {
    dialogDecideVisible.value = false;
    update_detail();
};

function onDecideNg() {
    dialogDecideVisible.value = false;
    update_detail();
};

function handleFasleAlarm() {
    dialogFalseAlarmVisible.value = true;
};

function onFalseAlarmCancel() {
    dialogFalseAlarmVisible.value = false;
};

function onFalseAlarmConfirm() {
    dialogFalseAlarmVisible.value = false;
    update_detail();
};

function handlePush() {
    dialogPushVisible.value = true;
};

function onPushConfirm() {
    dialogPushVisible.value = false;
    update_detail();
};

function onPushCancel() {
    dialogPushVisible.value = false;
};

function update_detail() {
    http.get(`/test_records/items/${test_record_item_id.value}`).then(res => {
        test_record_item.value = res.data.data;
    });
};

onMounted(() => {
    http.get(`/test_records/items/${test_record_item_id.value}`).then(res => {
        test_record_item.value = res.data.data;
        console.log('test_record_item', test_record_item);
        http.get(`/test_plans/${res.data.data.test_plan_id}`).then(res => {
            test_plan.value = res.data.data;
        });
    });
});

</script>

<style lang="scss" scoped>
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tool-container {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
}

:deep(.el-collapse-item__arrow) {
    order: -1;
    margin: 0 8px 0 0;
}

:deep(.el-form-item) {
    margin-bottom: 0;
}

:deep(.el-collapse-item__header) {
    font-size: 16px;
    font-weight: 600;
}
</style>