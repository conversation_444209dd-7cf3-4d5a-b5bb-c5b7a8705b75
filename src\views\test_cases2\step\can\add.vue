<template>
    <el-form :model="form" :rules="rules" ref="formRef">

        <el-form-item label="消息类型" prop="msg_type">
            <el-select v-model="form.msg_type" placeholder="请选择消息类型">
                <el-option label="自定义消息" value="MSG_CUSTOM"></el-option>
                <el-option label="功能寻址消息" value="MSG_FUNC_ADDR"></el-option>
                <el-option label="物理寻址消息" value="MSG_PHY_ADDR"></el-option>
            </el-select>
        </el-form-item>

        <el-row :gutter="10">
            <el-col :span="8">
                <el-form-item label="发送ID" prop="send_id">
                    <el-input v-model="form.send_id" :readonly="send_id_readonly"></el-input>
                </el-form-item>
            </el-col>
        </el-row>

        <el-form-item label="发送报文" prop="send_msg">
            <el-input type="textarea" :rows="3" v-model="form.send_msg" class="resizable-textarea"></el-input>
        </el-form-item>

        <el-row :gutter="10">

            <el-col :span="8">
                <el-form-item label="接收ID" prop="recv_id">
                    <el-input v-model="form.recv_id" :readonly="recv_id_readonly"></el-input>
                </el-form-item>
            </el-col>

        </el-row>

        <el-form-item label="接收报文" prop="recv_msg">
            <AddRecvMsg v-model="form.recv_msg" />
        </el-form-item>

        <el-row :gutter="10">
            <el-col :span="12">
                <el-form-item label="循环周期(ms)" prop="period">
                    <el-input-number v-model="form.period" :min="0" class="full-width"></el-input-number>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="通道" prop="channel">
                    <el-select v-model="form.channel" placeholder="请选择通道">
                        <el-option label="通道1" value="1"></el-option>
                        <el-option label="通道2" value="2"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>

    </el-form>
</template>


<script setup>
import { ref, watch } from 'vue';
import AddRecvMsg from './addRecvMsg.vue';

const props = defineProps({
    initData: {
        type: Object,
        default: () => ({}),
    },
});

const model = defineModel();

const formRef = ref(null);
const send_id_readonly = ref(false);
const recv_id_readonly = ref(false);

const form = ref({
    msg_type: 'MSG_CUSTOM',
    send_id: '',
    send_msg: '',
    recv_id: '',
    recv_msg: [''],
    channel: '1',
    period: 0,
    type: 'CAN',
});

const rules = ref({
    send_id: [
        { required: true, message: '请输入发送ID', trigger: 'blur' },
    ],
    send_msg: [
        { required: true, message: '请输入发送报文', trigger: 'blur' },
    ],
    recv_id: [
        { required: true, message: '请输入接收ID', trigger: 'blur' },
    ],
    recv_msg: [
        { required: true, message: '请输入接收报文', trigger: 'blur' },
    ],
    period: [
        { required: true, message: '请输入循环周期', trigger: 'blur' },
    ],
    channel: [
        { required: true, message: '请选择通道', trigger: 'blur' },
    ],
    type: [
        { required: true, message: '请选择类型', trigger: 'blur' },
    ],
    msg_type: [
        { required: true, message: '请选择消息类型', trigger: 'blur' },
    ],
});

watch(form, () => {
    model.value = form.value;
}, { immediate: true });

watch(() => props.initData, (val) => {
    if (!Array.isArray(val.recv_msg)) {
        val.recv_msg = [val.recv_msg];
    };
    Object.assign(form.value, val);
}, { immediate: true });

watch((() => form.value.msg_type), () => {
    if (form.value.msg_type === 'MSG_FUNC_ADDR') {
        send_id_readonly.value = true;
        recv_id_readonly.value = true;
        form.value.send_id = 'PH_FUNC_ADDR';
        form.value.recv_id = 'PH_RESP';
    } else if (form.value.msg_type === 'MSG_PHY_ADDR') {
        send_id_readonly.value = true;
        recv_id_readonly.value = true;
        form.value.send_id = 'PH_PHY_ADDR';
        form.value.recv_id = 'PH_RESP';
    } else if (form.value.msg_type === 'MSG_CUSTOM') {
        send_id_readonly.value = false;
        recv_id_readonly.value = false;
    }
}, { immediate: true });

const validate = (callback) => {
    formRef.value.validate(callback);
};

defineExpose({
    validate,
});

</script>

<style scoped>
.full-width {
    width: 100%;
}

.resizable-textarea :deep(textarea) {
  resize: both;
}

:deep(div.el-form-item__label) {
    font-weight: bold;
}
</style>