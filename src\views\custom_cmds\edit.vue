<template>
    <div class="submit-button-container">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onSubmit">提交</el-button>
    </div>

    <el-divider style="margin: 5px 0 20px;" />

    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="指令名称" prop="cmd_name">
                <el-input v-model="form.cmd_name"></el-input>
            </el-form-item>

            <el-form-item label="指令编码" prop="cmd_code">
                <el-input v-model="form.cmd_code"></el-input>
            </el-form-item>

            <el-form-item label="描述">
                <el-input type="textarea" :rows="3" v-model="form.desc"></el-input>
            </el-form-item>

        </el-form>
        <Params v-model="form.params" />
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import Params from './params/index.vue';

const props = defineProps({
    r_id: {
        type: Number,
        required: true,
    }
})

const formRef = ref(null);

const form = ref({
    cmd_name: '',
    cmd_code: '',
    params: [],
    desc: '',
});

const rules = ref({
    cmd_name: [
        { required: true, message: '请输入指令名称', trigger: 'blur' },
    ],
    cmd_code: [
        { required: true, message: '请输入指令编码', trigger: 'blur' },
    ],
});

const emit = defineEmits(['submit', 'cancel'])

const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            http.put(`/custom_cmds/${props.r_id}`, form.value).then(res => {
                emit('submit');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onCancel = () => {
    emit('cancel');
};

onMounted(() => {
    if (props.r_id) {
        http.get(`/custom_cmds/${props.r_id}`).then(res => {
            form.value.cmd_name = res.data.data.cmd_name;
            form.value.cmd_code = res.data.data.cmd_code;
            form.value.desc = res.data.data.desc; 
            form.value.params = res.data.data.params;
        });
    };
});

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>