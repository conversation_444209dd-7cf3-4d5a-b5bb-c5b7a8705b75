<template>
  <el-divider />
  <div class="add-container">
      <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

          <el-form-item label="项目名称" prop="name">
              <el-input v-model="form.name" :readonly="props.name != ''"></el-input>
          </el-form-item>

          <el-form-item label="项目编号" prop="number">
              <el-input v-model="form.number" :readonly="props.number != ''"></el-input>
          </el-form-item>

          <el-form-item label="DBC文件">
              <el-upload class="upload-demo" action="" :auto-upload="false" :file-list="dbcList"
                  :on-change="handleFileChange('dbc')" :before-upload="beforeUpload" accept=".dbc">
                  <el-button slot="trigger" type="primary" plain>选择DBC文件</el-button>
                  <div slot="tip" class="el-upload__tip" style="margin-left: 16px;">只能上传一个文件(.dbc)</div>
              </el-upload>
          </el-form-item>

          <el-form-item label="CANoe工程cfg路径">
              <el-input v-model="form.configs.canoe_cfg_path"></el-input>
          </el-form-item>

          <el-form-item label="功能寻址ID">
              <el-input v-model="form.configs.func_address_id" placeholder="请输入0x开头的16进制格式数据"></el-input>
          </el-form-item>

          <el-form-item label="物理寻址ID">
              <el-input v-model="form.configs.phy_address_id" placeholder="请输入0x开头的16进制格式数据"></el-input>
          </el-form-item>

          <el-form-item label="响应ID">
              <el-input v-model="form.configs.response_id" placeholder="请输入0x开头的16进制格式数据"></el-input>
          </el-form-item>
          <!-- 增加产品类型 -->
          <el-form-item label="产品类型">
              <el-select v-model="form.configs.product_types"  placeholder="请选择产品类型" multiple> 
                <el-option v-for="item in productTypes" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
          </el-form-item>

          <div class="submit-button-container">
              <el-button type="default" @click="onCancel">取消</el-button>
              <el-button type="primary" @click="onSubmit">提交</el-button>
          </div>

      </el-form>
  </div>
</template>

<script setup>
import { ref,onMounted } from 'vue';

import http from '@/utils/http/http.js';
import { useConfirmDialog } from '@vueuse/core';

const props = defineProps({
  name: {
      type: String,
      default: '',
  },
  number: {
      type: String,
      default: '',
  },
  configs: {
      type: Object,
      default: {},
  },
//   add
  productTypes: {
      type: Object,
      default: {},
  },
});


const dbcList = ref([]);

const productTypes = ref([]); // 用于存储从后端获取的产品类型数据

const handleFileChange = (field) => (file) => {
  form.value[field] = file.raw;
  if (field === 'dbc') {
      dbcList.value = [file];
  }
};

const beforeUpload = (file) => {
  return false; // 阻止自动上传
};

const formRef = ref(null);

const form = ref({
  name: props.name,
  number: props.number,
  dbc: null,
  configs: props.configs || {},
//   productTypes: props.productTypes || [] //用于存储后端请求数据
});

const rules = ref({
  name: [
      { required: true, message: '请输入项目名称', trigger: 'blur' },
  ],
  number: [
      { required: true, message: '请输入项目编号', trigger: 'blur' },
  ],
});

const emit = defineEmits(['affirm', 'cancel']);

// add
onMounted(() => {
    http.get(`/product_types`).then(res => {
        productTypes.value = res.data.data.results;
    });
});



const onSubmit = () => {
  formRef.value.validate(async (valid) => {
      if (valid) {
          const formData = new FormData();
          formData.append('name', form.value.name);
          formData.append('number', form.value.number);
          formData.append('configs', JSON.stringify(form.value.configs));
 
            
          if (form.value.dbc) {
              formData.append('dbc', form.value.dbc)
          };
      
          

          http.post('/projects/config', formData).then(res => {
              ElMessage({
                  message: '提交成功.',
                  type: 'success',
              });
              emit('affirm');
          }).catch(err => {
              ElMessageBox.alert(err.response.data.msg, '提交失败', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'error',
              })
          });
      };
  });
};

function onCancel() {
  emit('cancel');
};

</script>

<style lang="scss" scoped>
.add-container {
  padding: 0 20px;
}

.submit-button-container {
  display: flex;
  justify-content: flex-end;
}
</style>
