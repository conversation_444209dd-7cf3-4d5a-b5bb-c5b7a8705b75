<template>
    <el-affix :offset="63">
        <div class="top-tool-container">

            <span>测试计划创建</span>

            <el-button @click="onBack" style="margin-left: auto;margin-right: 10px;">返回</el-button>
            <div class="submit-button-container">
                <el-button type="primary" @click="onSave">保存</el-button>
            </div>
        </div>
    </el-affix>

    <div style="margin-bottom: 10px;">
        <el-button type="primary" @click="form.durability = '0'; form.plan_type = 'FULL_FUNCTIONALITY_TEST'"
            :plain="form.durability != '0'" :disabled="op_type === 'edit'">新建功能测试计划</el-button>
        <el-button type="primary" @click="form.durability = '1'; form.plan_type = 'DURABILITY_TEST'"
            :plain="form.durability != '1'" :disabled="op_type === 'edit'">新建耐久测试计划</el-button>
    </div>

    <el-collapse v-model="activeNames">
        <el-collapse-item title="基本信息" name="1">
            <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

                <el-row :gutter="10" style="max-width: 1400px;">

                    <el-col :span="12">
                        <el-form-item label="计划名称" prop="name">
                            <el-input v-model="form.name" placeholder="请输入计划名称"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="所属项目" prop="project_number" ref="projectRef">
                            <el-select v-model="form.project_number" placeholder="请选择所属项目" filterable
                                :disabled="op_type === 'edit'" @change="onProjectChange">
                                <el-option v-for="item in projects" :label="`${item.name}(${item.code})`"
                                    :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                </el-row>

                <el-row :gutter="10" style="max-width: 1400px;">

                    <el-col :span="12">

                        <el-form-item label="产品软件版本" prop="software_version">
                            <el-select v-model="form.software_version" placeholder="请选择产品软件版本">
                                <el-option v-for="v in software_versions" :label="v.label"
                                    :value="v.value"></el-option>
                            </el-select>
                        </el-form-item>

                    </el-col>

                    <el-col :span="12">

                        <el-form-item label="关联版本">
                            <el-tree-select ref="productVersionRef" v-model="form.product_version" check-on-click-node
                                lazy multiple :load="loadVersions" :data="projectVersionData"
                                :cache-data="initProjectVersionData" v-if="projectVersionV"
                                :props="{ label: 'label', value: 'value', children: 'children', isLeaf: 'isLeaf' }" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="10" style="max-width: 1400px;">
                    <el-col :span="12">
                        <el-form-item label="计划类型" prop="plan_type">
                            <el-select v-model="form.plan_type" placeholder="请选择计划类型"
                                :disabled="form.durability == '1'">
                                <el-option label="全功能测试" value="FULL_FUNCTIONALITY_TEST"></el-option>
                                <el-option label="版本回归测试" value="VERSION_REGRESSION_TEST"></el-option>
                                <el-option label="专项验证测试" value="SPECIFIC_VALIDATION_TEST"></el-option>
                                <el-option label="问题验证测试" value="PROBLEM_VALIDATION_TEST"></el-option>
                                <el-option label="耐久测试" value="DURABILITY_TEST"
                                    v-if="form.durability == '1'"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="计划描述">
                            <el-input v-model="form.desc" type="textarea" :rows="3" placeholder="请输入计划描述"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

            </el-form>
        </el-collapse-item>
        <el-collapse-item title="子计划" name="2">
            <div class="tool-bar-container">
                <el-button icon="Plus" type="primary" plain @click="handleAddSubPlan">添加子计划</el-button>
                <el-button icon="Minus" type="primary" plain @click="handleDeleteSubPlan">删除当前子计划</el-button>
            </div>
            <el-tabs type="border-card" v-model="currentSubPlan">
                <el-tab-pane v-for="(p, index) in subPlans" :name="index.toString()" :label="p">
                    <AddSubPlan :initData="sub_plans ? sub_plans[p] : {}" :project_number="form.project_number"
                        :durability="form.durability" v-model="form.sub_plans[p]"
                        :ref="el => { if (el) addSubPlanRefs[index] = el; }" />
                </el-tab-pane>
            </el-tabs>
        </el-collapse-item>
    </el-collapse>

</template>


<script setup>
import { ref, onMounted, nextTick } from 'vue'
import http from '@/utils/http/http.js';
import { useRouter, useRoute } from 'vue-router';
import AddSubPlan from './addSubPlan.vue';

const projects = ref([]);
const testCaseData = ref([]);
const formRef = ref(null);
const router = useRouter();
const route = useRoute();
const activeNames = ref(['1', '2']);
const subPlans = ref(['子计划1']);
const currentSubPlan = ref('0');
const productVersionRef = ref(null);
const projectVersionData = ref([]);
const projectVersionDataInit = ref([]);
const projectVersionV = ref(true);
const initProjectVersionData = ref([]);
const sub_plans = ref({});
const addSubPlanRefs = ref([]);
const software_versions = ref([]);

const op_type = ref(route.query.type);

const form = ref({
    name: '',
    project_number: route.query.project_number || '',
    durability: '0',
    product_version: [],
    desc: '',
    sub_plans: {},
    plan_type: 'FULL_FUNCTIONALITY_TEST',
    software_version: ''
})

const rules = ref({
    name: [
        { required: true, message: '请输入计划名称', trigger: 'blur' }
    ],
    project_number: [
        { required: true, message: '请选择所属项目', trigger: 'change' }
    ],
    durability: [
        { required: true, message: '请选择是否是耐久测试', trigger: 'change' }
    ],
    plan_type: [
        { required: true, message: '请选择计划类型', trigger: 'change' }
    ],
    software_version: [
        { required: true, message: '请选择产品软件版本', trigger: 'change' }
    ]
})

function onProjectChange() {
    projectVersionV.value = false;
    nextTick(() => {
        projectVersionV.value = true;
    });
    form.value.software_version = '';
    software_versions.value = [];
    loadSoftwareVersions();
};

const onSaveCreate = () => {
    formRef.value.validate((valid) => {
        if (valid) {

            for (let i = 0; i < addSubPlanRefs.value.length; i++) {
                addSubPlanRefs.value[i].validate((valid) => {
                    if (!valid) {
                        return false;
                    }
                });
            }

            const p = projects.value.find(item => item.code == form.value.project_number);

            let data = {
                ...form.value,
                project_name: p?.name,
                project_id: p?.projectId,
                test_cases: testCaseData.value.map(item => item.id)
            }

            let pv = data.product_version.map(item => {
                return productVersionRef.value.getNode(item)?.data;
            });

            data.product_version = JSON.stringify(pv);

            data.sub_plans = Object.values(data.sub_plans).filter(item => item);

            http.post('/test_plans', data).then(res => {
                router.push('/test_plans/list');
            }).catch(err => {
                ElMessageBox.alert(
                    err.response.data.msg,
                    '警告',
                    {
                        confirmButtonText: '确定',
                        type: 'warning',
                    }
                )
            });
        } else {
            return false;
        }
    });
}

const onSaveEdit = () => {
    formRef.value.validate((valid) => {
        if (valid) {
            for (let i = 0; i < addSubPlanRefs.value.length; i++) {
                addSubPlanRefs.value[i].validate((valid) => {
                    if (!valid) {
                        return false;
                    }
                });
            }

            const p = projects.value.find(item => item.code == form.value.project_number);

            let data = {
                ...form.value,
                project_name: p?.name,
                project_id: p?.projectId,
                test_cases: testCaseData.value.map(item => item.id)
            }

            let pv = data.product_version.map(item => {
                let i = initProjectVersionData.value.find(item2 => item2.value == item);
                if (i) {
                    return i;
                }
                return productVersionRef.value.getNode(item).data;
            });

            data.product_version = JSON.stringify(pv);

            data.sub_plans = Object.values(data.sub_plans).filter(item => item);

            http.put(`/test_plans/${route.query.id}`, data).then(res => {
                router.push('/test_plans/list');
            }).catch(err => {
                ElMessageBox.alert(
                    err.response.data.msg,
                    '警告',
                    {
                        confirmButtonText: '确定',
                        type: 'warning',
                    }
                )
            });
        } else {
            return false;
        }
    });
}

const onSave = () => {
    if (route.query.type === 'edit') {
        onSaveEdit();
    } else {
        onSaveCreate();
    }
}

let subPlanCount = 1;
const handleAddSubPlan = () => {
    if (form.value.durability === '1') {
        return;
    }

    subPlanCount += 1;
    subPlans.value.push('子计划' + subPlanCount);
    currentSubPlan.value = (subPlans.value.length - 1).toString();
}

const handleDeleteSubPlan = () => {
    if (subPlans.value.length === 1) {
        return;
    }
    form.value.sub_plans[subPlans.value[parseInt(currentSubPlan.value)]] = null;
    subPlans.value.splice(parseInt(currentSubPlan.value), 1);
    currentSubPlan.value = '0';
}

function updateOnEditTestPlan() {
    const id = route.query.id;
    if (id) {
        http.get(`/test_plans/${id}`).then(res => {
            const data = res.data.data;
            form.value = {
                name: data.name,
                project_number: data.project_number,
                durability: data.durability ? '1' : '0',
                desc: data.desc,
                software_version: data.software_version,
                product_version: data.product_version.map(item => item.id),
                sub_plans: {},
                plan_type: data.plan_type,
            }

            initProjectVersionData.value = data.product_version;

            subPlans.value = [];
            subPlanCount = 0;

            nextTick(() => {
                for (let i = 0; i < data.sub_plans.length; i++) {
                    subPlans.value.push('子计划' + (i + 1));
                    subPlanCount += 1;
                    sub_plans.value['子计划' + (i + 1)] = data.sub_plans[i];
                }
            });

            loadSoftwareVersions();

        }).catch(err => {
            console.log(err);
        });
    }
}

function loadVersions(node, resolve) {
    if (node.level === 0) {
        return resolve(projectVersionDataInit.value);
    }
    if (node.level === 1) {
        http.get('/issues/project_version_number',
            { params: { type: node.data.code, project_number: form.value.project_number } }).then(res => {
                if (res.data.err_code != 0) {
                    resolve([{ label: '无数据', value: '无数据', isLeaf: true, disabled: true }]);
                    return;
                }
                let data = res.data.data.records;
                data = data.map(item => {
                    item.label = item.name;
                    item.value = item.id;
                    item.type_name = node.data.label;
                    item.isLeaf = true;
                    return item;
                });
                resolve(data);
                if (data.length == 0) {
                    resolve([{ label: '无数据', value: '无数据', isLeaf: true, disabled: true }]);
                }
            });
    }
}

function loadSoftwareVersions() {
    let project_id = projects.value.find(item => item.code == form.value.project_number)?.projectId;
    http.get('/issues/project_version_number',
        { params: { type: "SOFTWARE", project_number: form.value.project_number} }).then(res => {
            let data = res.data.data?.records || [];
            data = data.map(item => {
                item.label = item.name;
                item.value = item.name;
                return item;
            });
            software_versions.value = data || [];
        });
}

function onBack() {
    router.push({ path: '/test_plans' });
}

onMounted(() => {

    http.get('/projects/p', { params: { pagesize: 10000 } }).then(res => {
        let data = res.data.data.results;
        projects.value = data;
    }).catch(err => {
        console.log(err);
    });

    http.get('/issues/project_version_type').then(res => {
        let data = res.data.data.dictList;
        data = data.map(item => {
            item.value = item.id;
            item.isLeaf = false;
            return item;
        }).filter(item => item.code != "SOFTWARE");
        projectVersionDataInit.value = data;
        projectVersionData.value = data;
    });

    if (route.query.type === 'edit') {
        updateOnEditTestPlan();
    };

    loadSoftwareVersions();

})

</script>


<style lang="scss" scoped>
.top-tool-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    height: 50px;
}

:deep(.el-collapse-item__arrow) {
    order: -1;
    margin: 0 8px 0 0;
}

:deep(.el-collapse-item__header) {
    font-size: 16px;
}
</style>