<template>
    <div>

        <div class="status-update">
            <el-form label-width="auto" class="form" :rules="formRules" ref="formRef" :model="form">
                <el-form-item label="备注:" prop="remark">
                    <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" :rows="5"></el-input>
                </el-form-item>
            </el-form>
        </div>

        <div style="display: flex; justify-content: end;">
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onConfirm" :loading="loading">确认</el-button>
        </div>
    </div>

</template>


<script setup>
import http from '@/utils/http/http.js';
import { ElMessageBox } from 'element-plus';
import { reactive, ref} from 'vue';

const props = defineProps({
    remarkInfo: {
        type: Object,
        required: true,
    },
});
const loading = ref(false);

const formRef = ref(null);

const form = reactive({
    remark: props.remarkInfo.remark || '',
});
const formRules = reactive({
    remark: [
        { required: true, message: '备注不能为空', trigger: 'blur' }
    ],
});

const emit = defineEmits(['confirm', 'cancel']);

function onCancel() {
    emit('cancel');
};

function submitForm() {
    loading.value = true;
    http.post('/product_versions/remark_update', {
        id: props.remarkInfo.id,
        remark: form.remark,
    }, { timeout: 600000 }).then(res => {
        ElMessage.success('状态更新成功');
        emit('confirm');
    }).catch(err => {
        ElMessageBox.alert(err.response?.data?.msg || '请求失败', '错误', {
            confirmButtonText: '确定',
            type: 'error',
        });
    }).finally(() => {
        loading.value = false;
    });
}

function onConfirm() {
    formRef.value.validate((valid) => {
        if (valid) {
            submitForm();
        }
    });
}


</script>

<style scoped>
.status-update {
    display: flex;
    justify-content: center;
    align-items: center;
}

.form {
    padding: 20px;

    width: 100%;
}
</style>