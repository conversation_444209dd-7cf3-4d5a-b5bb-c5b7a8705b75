<template>
    <div style="display: flex; flex-direction: column; height: calc(102vh - 250px);">
    <div class="tool-bar-container">
        <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button>
        <div style="margin-left: auto; display: flex; gap: 10px;">
            <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                <el-button text bg @click="handleReset">重置</el-button>
            </el-tooltip>
            <filterButton @click="onFilterStatusChange" :count="filterCount" />
            <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
        </div>
    </div>

    <div class="filter-container" v-if="showFilterContainer">
        <el-input v-model="form.name_re" placeholder="请输入名称" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
    </div>

    <el-table :data="tableData" stripe border style="width: 100%;flex: 1;" class="table-container">

        <el-table-column prop="name" label="人员名称" min-width="150" align="center"></el-table-column>

        <el-table-column prop="group" label="组" min-width="200" align="center"></el-table-column>

        <el-table-column label="角色" min-width="200" align="center">
            <template #default="{ row }">
                <el-tag v-for="role in row.roles" :key="role.id">{{ role.name }} </el-tag>
            </template>
        </el-table-column>

        <el-table-column prop="status" label="人员状态" min-width="100" align="center">
            <template #default="{ row }">
                <el-tag v-if="row.status == '正式'" type="success">{{ row.status }}</el-tag>
                <el-tag v-else-if="row.status == '试用期'" type="warning">{{ row.status }}</el-tag>
                <el-tag v-else-if="row.status == '实习生'" type="info">{{ row.status }}</el-tag>
                <el-tag v-else-if="row.status == '离职'" type="danger">{{ row.status }}</el-tag>
            </template>
        </el-table-column>

        <el-table-column label="工作年限" min-width="100" align="center">
            <template #default="{ row }">
                {{ getWorkingYears(row.work_start_date) }}
            </template>
        </el-table-column>

        <el-table-column label="操作" min-width="150" fixed="right" align="center">
            <template #default="{ row }">
                <div style="display: flex; justify-content: center; gap: 10px;">
                    <el-button type="primary" size="small" @click="handleEdit(row)" v-if="hasPermission">编辑</el-button>
                    <el-button type="primary" size="small" @click="handleGrade(row)" v-if="hasPermission || row.email == cur_email">评分</el-button>
                    <el-button type="danger" size="small" @click="handleDelete(row)" v-if="hasPermission">删除</el-button>
                </div>
            </template>
        </el-table-column>

    </el-table>
</div>

    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
            @change="onPageChange" />
    </div>

    <el-dialog v-if="dialogAddVisible" v-model="dialogAddVisible" title="添加人员" width="800"
        :close-on-click-modal="false">
        <Add @submit="onAddSuccess" @cancel="dialogAddVisible = false" />
    </el-dialog>

    <el-dialog v-if="dialogEditVisible" v-model="dialogEditVisible" title="编辑人员" width="800"
        :close-on-click-modal="false">
        <Edit @submit="onEditSuccess" @cancel="dialogEditVisible = false" :r_id="r_id" />
    </el-dialog>

</template>


<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import http from '@/utils/http/http.js';
import Add from './add.vue';
import Edit from './edit.vue';
import filterButton from '@/components/filterButton.vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/testers/list', '测试人员列表');
const tableData = ref([]);

const dialogAddVisible = ref(false);
const dialogEditVisible = ref(false);
const r_id = ref(0);

let form = reactive({
    name: '',
    number: '',
    pagesize: 15,
});

let total = ref(0);
const filterCount = ref(0);
let showFilterContainer = ref(false);
const router = useRouter();
const userStore = useUserStore();

const cur_email = computed(() => {
    return userStore.user_info.email;
});

const hasPermission = computed(() => {
    return ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"].includes(userStore.user_info.email);
});

const getWorkingYears = (start) => {
    if (!start) return '';

    const currentDate = new Date();
    const startDate = new Date(start);

    // 计算总月份差
    let totalMonths = (currentDate.getFullYear() - startDate.getFullYear()) * 12
        + (currentDate.getMonth() - startDate.getMonth());

    // 如果当前日期小于开始日期，月份减一
    if (currentDate.getDate() < startDate.getDate()) {
        totalMonths--;
    }

    const years = Math.floor(totalMonths / 12);
    const remainingMonths = totalMonths % 12;

    // 如果剩余月份大于等于6个月，按照一年计算
    if (remainingMonths >= 6) {
        return years + 1;
    } else {
        return years;
    }
};


function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    form.department_re =  '系统测试部'
    http.get('/testers', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'department_re'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleAdd() {
    dialogAddVisible.value = true;
};

function handleEdit(row) {
    r_id.value = row.id;
    dialogEditVisible.value = true;
};

function handleGrade(row) {
    router.push(`/testers/${row.id}/grade`);
};

function handleDelete(row) {
    ElMessageBox.confirm(
        '确定删除吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.delete(`/testers/${row.id}`).then(res => {
            ElMessage({
                message: '删除成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessage({
                type: 'error',
                message: err.response.data.msg
            });
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};

function onAddSuccess() {
    dialogAddVisible.value = false;
    update_table();
};

function onEditSuccess() {
    dialogEditVisible.value = false;
    update_table();
};

function handleRefresh() {
    update_table();
};

onMounted(() => {
    update_table();
});

</script>


<style scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    display: flex;
    justify-content: flex-start;
}
</style>