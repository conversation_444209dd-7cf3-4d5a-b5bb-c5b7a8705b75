<template>

    <div style="padding-top: 10px; background-color: rgb(243.9, 244.2, 244.8);width: 100%;">


        <el-scrollbar height="300px">

            <div v-for="(v, index) in model" style="padding: 20px;">
                <el-row :gutter="10">
                    <el-col :span="20">
                        <InputFile v-model="model[index]" :index="index + 1" />
                    </el-col>
                    <el-col :span="4">
                        <el-button type="danger" plain @click="handleDelete(index)" icon="Minus"></el-button>
                    </el-col>
                </el-row>
            </div>

        </el-scrollbar>

        <el-button type="primary" plain @click="handleAdd" icon="Plus" style="width: 100%">添加新源文件</el-button>

    </div>

</template>

<script setup>
import InputFile from './input_file.vue'

const model = defineModel();

function handleDelete(index) {
    if (model.value.length === 1) {
        return
    }
    model.value.splice(index, 1)
}

function handleAdd() {
    model.value.push({ identification: "", file: null, crc_vaild: "00" })
}

</script>

<style lang="scss" scoped></style>