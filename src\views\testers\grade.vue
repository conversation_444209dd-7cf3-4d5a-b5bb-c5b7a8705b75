<template>
    <div style="padding: auto;">
        <div style="display: flex;justify-content: space-between;align-items: center;">
            <h2>【{{ tester.name }}】的评分</h2>
            <div>
                <el-button @click="handleBack">返回</el-button>
                <el-button type="primary" @click="handleSave" v-if="hasPermission">保存</el-button>
            </div>
        </div>
        <el-divider style="margin: 0" />
        <el-form label-width="auto" status-icon class="base-info">
            <el-form-item label="组别：">
                <span>{{ tester.group }}</span>
            </el-form-item>
            <el-form-item label="工作年限：">
                <span>{{ workingYears }}年</span>
            </el-form-item>
            <el-form-item label="状态：">
                <el-tag v-if="tester.status == '正式'" type="success">{{ tester.status }}</el-tag>
                <el-tag v-else-if="tester.status == '试用期'" type="warning">{{ tester.status }}</el-tag>
                <el-tag v-else-if="tester.status == '实习生'" type="info">{{ tester.status }}</el-tag>
                <el-tag v-else-if="tester.status == '离职'" type="danger">{{ tester.status }}</el-tag>
            </el-form-item>
            <el-form-item label="工作经历：">
                <span>{{ tester.work_experience }}</span>
            </el-form-item>
        </el-form>

        <el-tabs v-model="activeName">
            <el-tab-pane v-for="(role, index) in tester.roles || []" :label="role.name + '评分'" :name="index.toString()">

                <el-table :data="grades[role.id.toString()] || []" stripe border style="height: calc(100vh - 420px);">
                    <el-table-column prop="skill_name" label="技能" width="300" align="center" />
                    <el-table-column prop="skill_desc" label="技能描述" width="400" align="center" />
                    <el-table-column prop="skill_max_score" label="最高分" width="100" align="center" />
                    <el-table-column label="评分" min-width="300"  align="center">
                        <template v-slot="{ row }">
                            <el-rate v-model="row.score" :max="row.skill_max_score" size="large" show-score
                                :disabled="!hasPermission" />
                        </template>
                    </el-table-column>
                </el-table>

            </el-tab-pane>
        </el-tabs>
    </div>

</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import http from '@/utils/http/http.js';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/stores/user';

const route = useRoute();
const router = useRouter();
const tester = ref({});
const grades = ref([]);
const activeName = ref('0');
const userStore = useUserStore();

const hasPermission = computed(() => {
    return ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"].includes(userStore.user_info.email);
});

const workingYears = computed(() => {
    if (!tester.value.work_start_date) return '';

    const currentDate = new Date();
    const startDate = new Date(tester.value.work_start_date);

    // 计算总月份差
    let totalMonths = (currentDate.getFullYear() - startDate.getFullYear()) * 12
        + (currentDate.getMonth() - startDate.getMonth());

    // 如果当前日期小于开始日期，月份减一
    if (currentDate.getDate() < startDate.getDate()) {
        totalMonths--;
    }

    const years = Math.floor(totalMonths / 12);
    const remainingMonths = totalMonths % 12;

    // 如果剩余月份大于等于6个月，按照一年计算
    if (remainingMonths >= 6) {
        return years + 1;
    } else {
        return years;
    }
});

const handleBack = () => {
    router.push('/testers');
};

const handleSave = () => {
    let id = route.params.id;
    let data = Object.keys(grades.value).map(roleId => {
        return grades.value[roleId].map(grade => {
            return {
                role_id: roleId,
                skill_id: grade.skill_id,
                score: grade.score,
            };
        });
    }).flat();

    http.post(`/testers/${id}/grades`, { scores: data }).then(res => {
        ElMessage.success('保存成功');
    });

};

onMounted(() => {
    let id = route.params.id;
    http.get(`/testers/${id}`).then(res => {
        let data = res.data.data;
        tester.value = data;
    });

    http.get(`/testers/${id}/grades`).then(res => {
        let data = res.data.data;
        grades.value = data;
    });

});

</script>

<style lang="scss" scoped>
.base-info {
    padding-left: 20px;
    padding-top: 10px;
    padding-bottom: 10px;
    max-width: 1000px;

}

.base-info .el-form-item {
    margin: 0px;
}

.role-info {
    padding: 20px;
}
</style>