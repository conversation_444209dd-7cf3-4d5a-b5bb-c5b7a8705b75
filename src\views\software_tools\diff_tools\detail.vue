<template>

    <el-form style="padding: 0 20px; text-align: left;" >
        <el-form-item label="项目：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 82px;"
            >{{ record.project_name }}({{ record.project_number }})</span>
        </el-form-item>
        <el-form-item label="输入文件类型：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 27px;"
            >{{ record.input_file_type }}</span>
        </el-form-item>
        <el-form-item label="基准软件包：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 40px;"
            >{{ record.old_package_name }}</span>
        </el-form-item>
        <el-form-item label="基准软件包版本号：" style="font-weight: bold;">
            <span
            style="font-weight: normal;"
            >{{ record.old_package_version }}</span>
        </el-form-item>
        <el-form-item label="基准软件包MD5：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 10px;"
            >{{ record.old_package_md5 }}</span>
        </el-form-item>
        <el-form-item label="目标软件包：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 40px;"
            >{{ record.new_package_name }}</span>
        </el-form-item>
        <el-form-item label="目标软件包版本号：" style="font-weight: bold;">
            <span
            style="font-weight: normal;"
            >{{ record.new_package_version }}</span>
        </el-form-item>
        <el-form-item label="目标软件包MD5：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 10px;"
            >{{ record.new_package_md5 }}</span>
        </el-form-item>
        <el-form-item label="差分包：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 70px;"
            >{{ record.diff_package_name }}</span>
        </el-form-item>
        <el-form-item label="差分包版本号：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 27px;"
            >{{ record.diff_package_version }}</span>
        </el-form-item>
        <el-form-item label="差分包MD5：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 40px;"
            >{{ record.diff_package_md5 }}</span>
        </el-form-item>
        <el-form-item label="创建时间：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 55px;"
            >{{ record.create_time }}</span>
        </el-form-item>
        <el-form-item label="创建人：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 70px;"
            >{{ record.operator_name }}</span>
        </el-form-item>
    </el-form>

</template>

<script setup>
import { ref, onMounted } from 'vue'
import http from '@/utils/http/http.js';

const props = defineProps({
    id: {
        type: Number,
        default: -1,
    }
})

const record = ref({});


onMounted(() => {
    http.get(`/diff_tool/records/${props.id}`).then(res => {
        let data = res.data.data;
        record.value = data;
    }).catch(err => {
        console.log(err);
    });
});

</script>

<style lang="scss" scoped></style>