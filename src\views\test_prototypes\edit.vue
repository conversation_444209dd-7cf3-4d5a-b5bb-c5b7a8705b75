<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" label-position="left" :rules="rules" status-icon ref="formRef">

            <el-form-item label="名称" prop="name">
                <el-input v-model="form.name"></el-input>
            </el-form-item>

            <el-form-item label="样件类型" prop="type">
                <el-select v-model="form.type">
                  <el-option label="总成" value="0"></el-option>
                  <el-option label="VDS" value="1"> </el-option>
                  <el-option label="主机" value="2"></el-option>
                  <el-option label="PCBA" value="3"> </el-option>
                  <el-option label="屏模组" value="4"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="样件状态" prop="status">
                <el-select v-model="form.status">
                    <el-option label="正常" value="0"></el-option>
                    <el-option label="故障" value="1"></el-option>
                    <el-option label="拆解" value="2"></el-option>
                    <el-option label="报废" value="3"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="样件阶段" prop="prototype_stage" style="margin-left: 10px">
                <el-select v-model="form.prototype_stage" style="margin-left: -10px">
                    <el-option label="DV" value="0"></el-option>
                    <el-option label="PV" value="1"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="关联项目" prop="project">
                <ProjectsComponent v-model="form.project" ref="projectRef"  :includePrefix="false"
                :includeAll="false"/>
            </el-form-item>

            <el-form-item label="维护人员" prop="user_email">
                <Orga v-model="form.user_email" ref="prototypesRef" :cache-data="cacheData" /> 
            </el-form-item>

            <el-form-item label="描述" style="margin-left: 10px">
                <el-input type="textarea" :rows="4" v-model="form.desc" style="margin-left: -10px"></el-input>
            </el-form-item>

            <div class="submit-button-container">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

import http from '@/utils/http/http.js';
import Orga from '@/components/Organization/index.vue';
import ProjectsComponent from '@/components/projects.vue';

const props = defineProps(
    {
        r_id: {
            type: Number,
            required: true,
        },
    }
);

const formRef = ref(null);
const prototypesRef = ref(null);
const cacheData = ref([]);

const form = ref({
    name: '',
    type:'',
    status:'',
    project: '',
    user_email:'',
    user_name:'',
    desc: '',
    prototype_stage: '',
});

const rules = ref({
    name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
    ],
    type: [
        { required: true, message: '请输入样件类型', trigger: 'blur' },
    ],
    status:[
        { required: true, message:'请输入样件状态', trigger: 'blur'}
    ],
    project:[
        { required: true, message:'请输入关联项目',trigger: 'blur'}
    ],
    user_email:[
        { required: true, message:'请输入维护人员',trigger: 'blur'}
    ]
});

const emit = defineEmits(['submit', 'cancel'])

const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            let prototypes = prototypesRef.value.getNode(form.value.user_email); 

            form.value.user_name = prototypes.label;

            // 提交表单数据
            http.put(`/test_prototypes/${props.r_id}`, form.value).then(res => {
                emit('submit');
                cacheData.value = null; // 提交成功后清空缓存
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                });
            });
        }
    });
};

const onCancel = () => {
    cacheData.value = { ...form.value }; // 取消时保存当前表单数据到缓存
    emit('cancel');
};

onMounted(() => {
    http.get(`/test_prototypes/${props.r_id}`).then(res => {
            form.value = {
                name: res.data.data.name,
                type: res.data.data.type?.toString(), // 确保 type 是字符串类型
                status: res.data.data.status?.toString(), // 确保 status 是字符串类型
                project: res.data.data.project,
                user_email: res.data.data.user_email,
                user_name: res.data.data.user_name,
                desc: res.data.data.desc,
                prototype_stage: res.data.data.prototype_stage?.toString(), // 确保 prototype_stage 是字符串类型
            };

            cacheData.value = [{  
                label: res.data.data.user_name                ,
                value: res.data.data.user_email,
                data: {
                    label: res.data.data.user_name,
                    value: res.data.data.user_email,
                    employeeNo: res.data.data.maintainer
                }
            }]; 
        });
});
</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>