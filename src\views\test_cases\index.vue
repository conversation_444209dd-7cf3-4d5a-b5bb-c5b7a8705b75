<template>
    <div class="container">
        <div class="search-container">
            <el-input placeholder="请输入搜索内容" class="search-input">
                <template #append>
                    <el-button icon="Search" @click="onSearch" class="search-button">搜索</el-button>
                </template>
            </el-input>
        </div>

        <div class="tool-bar-container">
            <!-- <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button> -->
            <el-button type="info" plain @click="onFilterStatusChange">
                筛选<el-icon class="el-icon--right">
                    <component :is="filterButtonIcon"></component>
                </el-icon>
            </el-button>
        </div>

        <div class="filter-container" v-if="showFilterContainer">
            <el-input v-model="form.name" size="large" placeholder="请输入名称" suffix-icon="Search" @keyup.enter="onFilter"
                clearable></el-input>
            <el-input v-model="form.number" size="large" placeholder="请输入编号" suffix-icon="Search"
                @keyup.enter="onFilter" clearable></el-input>
        </div>

        <div class="table-container">
            <el-table :data="tableData" stripe border>

                <el-table-column prop="project_number" label="项目编号" width="200" align="center"></el-table-column>
                <el-table-column prop="name" label="项目名称" width="200" align="center"></el-table-column>
                <el-table-column prop="submitter" label="提交人" width="150" align="center"></el-table-column>
                <el-table-column prop="update_time" label="提交时间" width="180" align="center"></el-table-column>
                <el-table-column prop="version" label="用例版本" width="120" align="center"></el-table-column>
                <el-table-column label="测试用例文件" width="180" align="center">
                    <template #default="{ row }">
                        <el-link :href="baer_url + row.tc_file" target="_blank">文件</el-link>
                    </template>
                </el-table-column>

                <el-table-column label="操作" min-width="150" fixed="right" align="left">
                    <template #default="{ row }">
                        <el-button type="primary" size="small" @click="handleDetail(row)">详情</el-button>
                    </template>
                </el-table-column>

            </el-table>
        </div>

        <div class="pagination-container">
            <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
                :total="total" background @change="onPaginate" />
        </div>
    </div>

</template>


<script setup>

import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import http from '@/utils/http/http.js';

const router = useRouter();

const tableData = ref([]);

// const baer_url = import.meta.env.VITE_BASE_URL;
const baer_url = "http://**********:9000";

const dialogDetailVisible = ref(false);

let form = reactive({
    name: '',
    number: '',
});

let total = ref(0);

let searchParams = {};

let showFilterContainer = ref(false);
let filterButtonIcon = ref("ArrowDown");

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
    if (showFilterContainer.value) {
        filterButtonIcon.value = "ArrowUp";
    } else {
        filterButtonIcon.value = "ArrowDown";
    }
};

function update_table(params) {
    Object.assign(searchParams, params)
    http.get('/test_m/test_cases', { params: searchParams }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });
};

function onSearch() {
    update_table({});
};

function onFilter() {
    update_table(form);
};

function onPaginate(currentPage, pageSize) {
    update_table({ page: currentPage, pagesize: pageSize });
};

function handleDetail(row) {
    router.push({ name: 'TestCaseDetail', params: { id: row.id } });
};


onMounted(() => {
    update_table();
});

</script>


<style scoped>
.container {
    display: flex;
    flex-direction: column;
    align-items: left;
}

.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}
</style>