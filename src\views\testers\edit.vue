<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="人员名称" prop="email">
                <Orga v-model="form.email" :cacheData="testerCacheData" ref="testerRef" placeholder="请选择人员"/>
            </el-form-item>

            <el-form-item label="部门">
                <el-select v-model="form.department" placeholder="请选择部门" clearable>
                    <!-- <el-option label="无" value=""></el-option> -->
                    <el-option label="系统测试部" value="系统测试部"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="组">
                <el-select v-model="form.group" placeholder="请选择组" clearable>
                    <!-- <el-option label="无" value=""></el-option> -->
                    <el-option label="系统测试一组" value="系统测试一组"></el-option>
                    <el-option label="系统测试二组" value="系统测试二组"></el-option>
                    <el-option label="软件测试组" value="软件测试组"></el-option>
                    <el-option label="自动化开发组" value="自动化开发组"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="角色" prop="role_ids">
                <el-select v-model="form.role_ids" placeholder="请选择角色" multiple clearable>
                    <el-option v-for="item in roles" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="状态" prop="status">
                <el-select v-model="form.status" placeholder="请选择状态" clearable>
                    <el-option label="正式" value="正式"></el-option>
                    <el-option label="试用期" value="试用期"></el-option>
                    <el-option label="实习生" value="实习生"></el-option>
                    <el-option label="离职" value="离职"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="开始工作日期" prop="work_start_date">
                <el-date-picker v-model="form.work_start_date" type="date" placeholder="请选择开始工作日期" value-format="YYYY-MM-DD" />
            </el-form-item>

            <el-form-item label="工作经历">
                <el-input type="textarea" :rows="6" v-model="form.work_experience"></el-input>
            </el-form-item>

            <div class="submit-button-container">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import Orga from '@/components/Organization/index.vue';

const props = defineProps(
    {
        r_id: {
            type: Number,
            required: true,
        },
    }
);

const roles = ref([]);
const formRef = ref(null);
const testerRef = ref(null);
const testerCacheData = ref([]);

const form = ref({
    email: '',
    department: '',
    group: '',
    status: '',
    role_ids: [],
    work_start_date: '',
    work_experience: '',
});

const rules = ref({
    email: [
        { required: true, message: '请输入人员名称', trigger: 'blur' },
    ],
    role_ids: [
        { required: true, message: '请选择角色', trigger: 'blur' },
    ],
    work_start_date: [
        { required: true, message: '请选择开始工作日期', trigger: 'blur' },
    ],
    status: [
        { required: true, message: '请选择状态', trigger: 'blur' },
    ],
});


const emit = defineEmits(['submit', 'cancel'])

const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            http.put(`/testers/${props.r_id}`, form.value).then(res => {
                emit('submit');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onCancel = () => {
    emit('cancel');
};

onMounted(() => {
    http.get(`/testers/${props.r_id}`).then(res => {
        form.value.name = res.data.data.name;
        form.value.email = res.data.data.email;
        form.value.department = res.data.data.department;
        form.value.group = res.data.data.group;
        form.value.role_ids = res.data.data.roles.map(item => item.id);
        form.value.work_start_date = res.data.data.work_start_date;
        form.value.work_experience = res.data.data.work_experience;
        form.value.status = res.data.data.status;

        testerCacheData.value = [{
            label: res.data.data.name,
            value: res.data.data.email,
            isLeaf: true,
        }];
    });

    http.get('/testers/roles').then(res => {
        roles.value = res.data.data.results;
    }).catch(err => {
        ElMessage({
            type: 'error',
            message: err.response.data.msg
        });
    });
});

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>