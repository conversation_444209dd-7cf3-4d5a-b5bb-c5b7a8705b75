import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

import 'normalize.css/normalize.css'

import NProgress from 'nprogress';
import 'nprogress/nprogress.css';

import '@/styles/index.scss'

import App from './App.vue'
import router from './router'

import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import "element-plus/theme-chalk/el-message-box.css";
import "element-plus/theme-chalk/el-message.css";

import 'virtual:svg-icons-register'

import { customLoading } from './directives/customLoading'

const app = createApp(App)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

app.use(pinia)
app.use(router)

app.directive('custom-loading', customLoading);


// 配置 NProgress
NProgress.configure({ showSpinner: false, speed: 1000 });

router.beforeEach((to, from, next) => {
    NProgress.start();
    next();
});

router.afterEach(() => {
    NProgress.done();
});


app.mount('#app')
