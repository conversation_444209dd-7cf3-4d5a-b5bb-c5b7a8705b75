<template>
    <div style="display: flex; flex-direction: column; height: calc(100vh - 250px);">
        <div class="tool-bar-container">

            <div style="margin-left: auto; display: flex; gap: 10px;">
                <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                    <el-button text bg @click="handleReset">重置</el-button>
                </el-tooltip>
                <filterButton @click="onFilterStatusChange" :count="filterCount" />
                <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
            </div>
        </div>

        <div class="filter-container" v-if="showFilterContainer">
            <el-input v-model="form.test_plan_name_re" placeholder="请输入测试计划名称" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
            <el-input v-model="form.test_case_name_re" placeholder="请输入测试用例名称" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
            <el-select v-model="form.result_list" placeholder="请选择测试结果" @change="onFilter" style="width: 400px;"
                multiple clearable>
                <el-option label="待判定" value="2"></el-option>
                <el-option label="PASS" value="1"></el-option>
                <el-option label="NG" value="0"></el-option>
            </el-select>
            <el-input v-model="form.test_plan_id" placeholder="请输入测试计划ID" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
        </div>

        <el-table :data="tableData" stripe border style="width: 100%; flex: 1;">
            <el-table-column prop="test_case_number" label="测试用例ID" width="200" align="center"></el-table-column>
            <el-table-column prop="test_case_name" label="测试项名称" min-width="200" align="center"></el-table-column>
            <el-table-column prop="result" label="测试结果" min-width="100" align="center">
                <template #default="{ row }">
                    <el-tag v-if="row.result === null" type="warning">待判定</el-tag>
                    <el-tag v-else-if="row.result" type="success">PASS</el-tag>
                    <el-tag v-else="row.result == 1" type="danger">NG</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="处理状态" min-width="100" align="center">
                <template #default="{ row }">
                    <el-tag v-if="row.status == 0" type="warning">未处理</el-tag>
                    <el-tag v-if="row.status == 1" type="success">已处理</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="tester_name" label="测试人" min-width="100" align="center"></el-table-column>
            <el-table-column prop="project_name" label="所属项目" min-width="200" align="center"></el-table-column>
            <el-table-column prop="project_number" label="项目编号" min-width="150" align="center"></el-table-column>
            <el-table-column prop="test_plan_name" label="计划名称" min-width="200" align="center"></el-table-column>
            <el-table-column prop="start_time" label="开始时间" min-width="200" align="center"></el-table-column>
            <el-table-column prop="end_time" label="结束时间" min-width="200" align="center"></el-table-column>

            <el-table-column label="操作" min-width="180" fixed="right" align="center">
                <template #default="{ row, $index }">
                    <div style="display: flex; justify-content: center; gap: 10px;">
                        <el-button type="primary" size="small" @click="handledetail(row, $index)">详情</el-button>
                        <el-icon v-if="collected.includes(row.id)" :size="24"
                            style="cursor: pointer; color: #ffd700; transition: all 0.3s ease;"
                            @click="handleCollect(row)">
                            <StarFilled />
                        </el-icon>
                        <el-icon v-else :size="24" style="cursor: pointer; color: #c0c4cc; transition: all 0.3s ease;"
                            @click="handleCollect(row)" @mouseenter="$event.target.style.color = '#ffd700'"
                            @mouseleave="$event.target.style.color = '#c0c4cc'">
                            <Star />
                        </el-icon>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <div class="pagination-container">
            <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
                :total="total" v-model:current-page="form.page" v-model:page-size="form.pagesize" background
                @change="onPageChange" />
        </div>
    </div>

</template>

<script setup>

import { ref, reactive, onMounted, onActivated, watch, inject } from 'vue';
import http from '@/utils/http/http.js';
import { useRouter, useRoute } from 'vue-router';
import filterButton from '@/components/filterButton.vue';
import { useProjectStore } from '@/stores/project.js';
import { Star } from '@element-plus/icons-vue';

const router = useRouter();
const route = useRoute();
const projects = ref([]);
const tableData = ref([]);
const count = ref(0);
const filterCount = ref(0);
let projectStore = useProjectStore();
const collected = ref([]);

let form = reactive({
    page: 1,
    pagesize: 10,
});

let total = ref(0);

let showFilterContainer = ref(false);

const testPlanId = inject('testPlanId');

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    form.order = "-id";
    http.get('/v2/test_records/items', { params: form, timeout: 10000, }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
        count.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'test_record_id', 'test_case_id', 'project_number', 'order'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    const projectNumber = form.project_number;
    const testRecordId = form.test_record_id;
    Object.keys(form).forEach(key => {
        delete form[key];
    });
    form.page = 1;
    form.pagesize = 10;
    form.project_number = projectNumber;
    form.test_record_id = testRecordId;
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    update_table();
};

function handledetail(row, index) {
    router.push({ path: `/test_records_v2/items/${row.id}`, query: { back_target: "/test_records_v2/list", q: JSON.stringify({ a: 1 }) } });
};

function handleRefresh() {
    update_table();
};

function handleCollect(row) {
    const isCollected = collected.value.includes(row.id);
    http.post(`/v2/test_records/items/collect`, { collected: !isCollected, test_record_item_id: row.id }).then(res => {
        if (isCollected) {
            collected.value = collected.value.filter(id => id !== row.id);
        } else {
            collected.value.push(row.id);
        }
    });
};

watch(() => projectStore.project_info, () => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

watch(() => testPlanId.value, () => {
    form.test_plan_id = testPlanId.value;
    update_table();
});

onActivated(() => {
    if (testPlanId.value) {
        form.test_plan_id = testPlanId.value;
    }
    form.project_number = projectStore.project_info.projectCode;
    update_table();

    http.get('/v2/test_records/items/collect').then(res => {
        collected.value = res.data.data;
    });
});

onMounted(() => {
    if (testPlanId.value) {
        form.test_plan_id = testPlanId.value;
    }
    form.project_number = projectStore.project_info.projectCode;
    update_table();

    http.get('/v2/test_records/items/collect').then(res => {
        collected.value = res.data.data;
    });
})

</script>


<style lang="scss" scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input,
    .el-select {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
        height: 35px;
    }

    :deep(.el-select__wrapper) {
        height: 100%;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;

    .el-select {
        width: 500px;
    }

}

.el-aside {
    width: 200px;
    height: 100% !important;

    margin-top: 20px;
}
</style>