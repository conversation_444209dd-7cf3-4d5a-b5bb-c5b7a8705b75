{"name": "atpms", "version": "1.1.3", "private": true, "type": "module", "scripts": {"dev": "vite", "test": "vite --mode test", "build": "vite build", "preview": "vite preview", "build:test": "vite build --mode test"}, "dependencies": {"@fullcalendar/core": "^6.1.11", "@fullcalendar/daygrid": "^6.1.11", "@fullcalendar/interaction": "^6.1.11", "@fullcalendar/list": "^6.1.11", "@fullcalendar/timegrid": "^6.1.11", "@fullcalendar/vue3": "^6.1.11", "@vue-flow/background": "^1.0.0", "@vue-flow/core": "^1.0.0", "@vueuse/core": "^11.1.0", "axios": "^1.7.9", "dayjs": "^1.11.11", "echarts": "^5.5.1", "element-plus": "^2.7.2", "lodash": "^4.17.21", "moment": "^2.30.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "openai": "^4.78.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "qs": "^6.12.1", "sass": "^1.75.0", "simple-mind-map": "^0.11.2", "sortablejs": "^1.15.6", "vis-data": "^7.1.9", "vis-timeline": "^7.7.3", "vue": "^3.4.21", "vue-router": "^4.3.0", "vuedraggable": "^2.24.3"}, "devDependencies": {"@iconify/json": "^2.2.209", "@iconify/vue": "^4.1.2", "@vitejs/plugin-vue": "^5.0.4", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.2.8", "vite-plugin-svg-icons": "^2.0.1", "vite-svg-loader": "^5.1.0"}}