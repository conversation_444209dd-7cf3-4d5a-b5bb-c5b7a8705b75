<template>
    <component :is="type" v-bind="linkProps">
        <slot></slot>
    </component>
</template>

<script setup>
import { computed} from 'vue';
import { isExternal } from '@/utils/validate';

let props = defineProps({
    to: {
        type: String,
        default: ''
    },
});

let isExternalLink = computed(() => isExternal(props.to));

let type = computed(() => {
    if (isExternalLink.value) {
        return 'a';
    } else {
        return 'router-link';
    }
});

let linkProps = computed(() => {
    if (isExternalLink.value) {
        return {
            href: props.to,
            target: '_blank',
            rel: 'noopener'
        };
    } else {
        return {
            to: props.to
        };
    }
});

</script>