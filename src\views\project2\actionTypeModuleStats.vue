<template>
    <el-card style="width: 100%;margin-bottom: 10px;">
        <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <h3>{{ title }}</h3>
                <el-select v-model="action_type" placeholder="所有用例活动类型" style="max-width: 400px;">
                    <el-option  label="所有用例活动类型" value=""></el-option>
                    <el-option v-for="item in action_types" :label="item.name" :value="item.number"></el-option>
                </el-select>
            </div>
        </template>
        <EchartsComponent :options="options" height="800px" />
    </el-card>
</template>

<script setup>
import { ref, onMounted, inject, watch } from 'vue';
import EchartsComponent from '@/components/echartsComponent.vue';
import http from '@/utils/http/http.js';

const action_types = ref([]);
const action_type = ref('');
const project_number = inject('project_number');

const title = ref('用例活动类型 - 功能模块自动化率');
const options = ref(
    {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow',
            },
        },
        legend: {
            orient: 'vertical',
            left: 10,
            top: 10,
        },
        grid: {
            left: '250px',
        },
        xAxis: {
            type: 'value'
        },
        yAxis: {
            type: 'category',
            data: []
        },
        series: [],
    }
)

watch([project_number, action_type], () => {
    if (project_number.value) {
        http.get('/projects/test_case_action_type_module_stats', { params: { number: project_number.value, action_type: action_type.value } }).then((res) => {
            let data = res.data.data;
            options.value.yAxis.data = data.labels;
            options.value.series = data.data.map((item, index) => ({
                name: item.name,
                type: 'bar',
                stack: '总量',
                data: item.data,
                itemStyle: {
                    borderRadius: index === data.data.length - 1 ? [0, 5, 5, 0] : 0,
                },
                label: {
                    show: true,
                    position: 'right',
                    formatter: (params) => {
                        if (item.name === '手动测试用例') {
                            const autoItem = data.data.find(d => d.name === '自动化测试用例');
                            const autoValue = autoItem ? autoItem.data[params.dataIndex] : 0;
                            const semiAutoItem = data.data.find(d => d.name === '半自动测试用例');
                            const semiAutoValue = semiAutoItem ? semiAutoItem.data[params.dataIndex] : 0;
                            const total = data.data.reduce((sum, d) => sum + d.data[params.dataIndex], 0);
                            const combinedValue = autoValue + semiAutoValue;
                            if (total > 0) {
                                const percentage = ((combinedValue / total) * 100).toFixed(0);
                                return `自动化率${percentage}%`;
                            }
                        }
                        return '';
                    },
                },
            }));
        });
    };
}, { immediate: true });

onMounted(() => {
    http.get('/test_m/test_case_types', { params: { pagesize: 1000 } }).then(res => {
        let data = res.data.data.results;
        action_types.value = data;
    }).catch(err => {
        console.log(err);
    });
});

</script>