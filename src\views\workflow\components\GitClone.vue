<template>
  <div class="git-clone-form">
    <div class="form-group">
      <label for="repositoryUrl">项目名称</label>
      <input 
        id="projectName" 
        v-model="config.projectName" 
        type="text" 
        placeholder="HiWaySDK_2.0"
      />
    </div>

    <div class="form-group">
      <label for="repositoryUrl">仓库地址</label>
      <input 
        id="repositoryUrl" 
        v-model="config.repositoryUrl" 
        type="text" 
        placeholder="10.1.1.99/mcu-team/sdk_code/hiwaysdk2.0"
      />
    </div>
    
    <div class="form-group">
      <label for="branch">分支</label>
      <input 
        id="branch" 
        v-model="config.branch" 
        type="text" 
        placeholder="dev"
      />
    </div>
    
    <div class="form-group">
      <label for="username">用户名</label>
      <input 
        id="username" 
        v-model="config.username" 
        type="text" 
        placeholder="用户名 (可不填)"
      />
    </div>
    
    <div class="form-group">
      <label for="password">密码</label>
      <input 
        id="password" 
        v-model="config.password" 
        type="password" 
        placeholder="密码 (可不填)"
      />
    </div>
    
    <div class="form-group">
      <label for="token">访问令牌</label>
      <input 
        id="token" 
        v-model="config.token" 
        type="password" 
        placeholder="输入你的访问令牌(可不填)"
      />
    </div>
    
    <div class="form-actions">
      <button class="cancel-btn" @click="$emit('cancel')">取消</button>
      <button class="save-btn" @click="save">保存</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  initialConfig: {
    type: Object,
    default: () => ({
      projectName: '',
      repositoryUrl: '',
      branch: '',
      username: '',
      password: '',
      token: '',
    })
  }
});

const emit = defineEmits(['save', 'cancel']);
const config = ref({...props.initialConfig})


const save = () => {
  console.log(config.value.projectName)
  if (config.value.projectName === '') 
  {  
     config.value.projectName =  "HiWaySDK_2.0"
  }
  console.log('上传配置被触发')
  console.log(config.value)
  emit('save', config.value);  
};
</script>

<style scoped>
.git-clone-form {
  width: 100%;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

input, select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #1f2937;
  transition: border-color 0.2s;
}

input:focus, select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 36px;
}

button {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.cancel-btn:hover {
  background-color: #e5e7eb;
}

.save-btn {
  background-color: #2563eb;
  color: white;
  border: 1px solid #2563eb;
}

.save-btn:hover {
  background-color: #1d4ed8;
}
</style>