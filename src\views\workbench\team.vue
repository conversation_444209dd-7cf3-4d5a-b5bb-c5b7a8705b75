<template>
    <el-container>
        <el-aside>
            <div style="margin-top: 100px; padding-left: 20px;">
                <h3>团队成员</h3>
            </div>
            <el-scrollbar style="height: 70vh;">

                <el-tree :data="team_members" node-key="email" :props="{ label: 'name', value: 'email' }"
                    check-on-click-node :expand-on-click-node="false" show-checkbox @check="onCheck">

                    <template #default="{ node }">
                        <span>{{ node.label }}</span>
                    </template>

                </el-tree>

            </el-scrollbar>
        </el-aside>

        <el-main>
            <div style="display: flex;flex-direction: column;height: calc(100vh - 200px);">
                <div class="tool-bar-container">

                    <div style="margin-left: auto; display: flex; gap: 10px;">
                        <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                            <el-button text bg @click="handleReset">重置</el-button>
                        </el-tooltip>
                        <filterButton @click="onFilterStatusChange" :count="filterCount" />
                        <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
                    </div>
                </div>

                <div class="filter-container" v-if="showFilterContainer">
                    <el-row :gutter="20" style="width: 100%;max-width: 1800px;margin-bottom: 10px;">
                        <!-- 所属项目搜索框 -->
                        <el-col :span="4">
                            <el-select v-model="form.project_number" placeholder="请选择所属项目" @change="onFilter"
                                style="width: 100%;" filterable clearable>
                                <el-option v-for="item in projects" :key="item.projectCode"
                                    :label="`${item.name}(${item.projectCode})`" :value="item.projectCode"></el-option>
                            </el-select>
                        </el-col>
                        <!-- 版本状态搜索框 -->
                        <el-col :span="4">
                            <el-select v-model="form.status_list" placeholder="请选择版本状态" @change="onFilter"
                                style="width: 100%;" multiple clearable>
                                <el-option label="已提测" value="SUBMITTED"></el-option>
                                <el-option label="测试中" value="TESTING"></el-option>
                                <el-option label="测试不通过" value="FAILED"></el-option>
                                <el-option label="测试通过" value="PASSED"></el-option>
                                <el-option label="已发布" value="RELEASED"></el-option>
                                <el-option label="已量产" value="PRODUCTION"></el-option>
                                <el-option label="已废弃" value="DEPRECATED"></el-option>
                            </el-select>
                        </el-col>

                    </el-row>
                </div>
                <el-table :data="tableData" stripe border style="width: 100%; flex: 1;">
                    <!-- 所属项目列 -->
                    <el-table-column label="所属项目" min-width="300" align="center">
                        <template #default="{ row }">
                            <span>{{ row.project_name }}({{ row.project_number }})</span>
                        </template>
                    </el-table-column>

                    <!-- 版本列 -->
                    <el-table-column prop="version_name" label="版本" min-width="400" align="center"></el-table-column>

                    <!-- 状态列 -->
                    <el-table-column label="状态" min-width="150" align="center">
                        <template #default="{ row }">
                            <el-tag v-if="row.status == 'SUBMITTED'" type="success">已提测</el-tag>
                            <el-tag v-else-if="row.status == 'TESTING'" type="success">测试中</el-tag>
                            <el-tag v-else-if="row.status == 'SMOKE_TEST_PASSED'" type="success">冒烟测试通过</el-tag>
                            <el-tag v-else-if="row.status == 'SPECIAL_VERSION_RELEASED'" type="success">特殊版本已发布</el-tag>
                            <el-tag v-else-if="row.status == 'FAILED'" type="warning">测试不通过</el-tag>
                            <el-tag v-else-if="row.status == 'PASSED'" type="success">测试通过</el-tag>
                            <el-tag v-else-if="row.status == 'RELEASED'" type="primary">已发布</el-tag>
                            <el-tag v-else-if="row.status == 'PRODUCTION'" type="primary">已量产</el-tag>
                            <el-tag v-else-if="row.status == 'DEPRECATED'" type="danger">已废弃</el-tag>
                            <el-tag v-else type="danger">未知</el-tag>
                        </template>
                    </el-table-column>

                    <!-- 发布日期列 -->
                    <el-table-column prop="publish_time" label="发布日期" min-width="200" align="center"></el-table-column>

                    <!-- 测试人员列 -->
                    <el-table-column prop="person_name" label="测试人员" min-width="100" align="center"></el-table-column>

                    <!-- 测试计划列 -->
                    <el-table-column label="测试计划" min-width="100" align="center">
                        <template #default="{ row }">
                            <el-link :underline="false" type="primary" @click="handleTestPlan(row)">查看</el-link>
                        </template>
                    </el-table-column>

                    <!-- 操作列 -->
                    <el-table-column label="操作" min-width="100" fixed="right" align="center">
                        <template #default="{ row }">
                            <div style="display: flex; justify-content: center; gap: 10px;">
                                <!-- 这里可以加入按钮或其他操作项 -->
                                <!-- <el-button type="primary" size="small" @click="handleDetail(row)">查看</el-button>
                <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button> -->
                            </div>
                        </template>
                    </el-table-column>

                </el-table>

                <div class="pagination-container">
                    <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]"
                        layout="prev, pager, next, jumper, total, sizes" v-model:current-page="form.page"
                        v-model:page-size="form.pagesize" :total="total" background @change="onPageChange" />
                </div>

            </div>
        </el-main>

    </el-container>

</template>


<script setup>
import { ref, reactive, onMounted, onActivated } from 'vue';
import http from '@/utils/http/http.js';
import { useRouter } from 'vue-router';
import { useTestPlanStore } from '@/stores/testPlan.js';
import { useUserStore } from '@/stores/user.js';


const userStore = useUserStore();
let testPlanStore = useTestPlanStore();
let filterCount = ref(0);
const tableData = ref([]);
const router = useRouter();
const team_members = ref([]);

let selectedValue = ref([]);
const projects = ref([]);

let form = reactive({
    page: 1,
    pagesize: 10,
    is_team: true,
    project_number: '',
});

let total = ref(0);

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/workbench/product_versions', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;

        for (var member of res.data.data.team_members) {
            if (team_members.value.findIndex(item => item.email == member.email) == -1) {
                team_members.value = res.data.data.team_members;
                break;
            }
        }
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'project_number', 'team_members', 'is_team'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};


const onCheck = (curData, checkedData) => {
    let emails = checkedData.checkedNodes.map(node => node.email);
    form.team_members = emails;
    update_table();
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
        status_list: [],
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleRefresh() {
    update_table();
};

function handleTestPlan(row) {
    testPlanStore.setSoftwareVersion(row.version_name);
    router.push({ path: "/test_plans_v2" });
};

onMounted(() => {
    http.get('/projects/p/all').then(res => {
        projects.value = res.data.data.results;
    }).catch(err => {
        console.log(err);
    });
    update_table();
});

onMounted(() => {
    update_table();

    const open_id = userStore.user_info.open_id;
    http.post('/auto_test/access_stat', {
        user_id: open_id,
        page_url: '/workbench/team',
        page_title: '团队工作台',
    })

});

onActivated(() => {
    const open_id = userStore.user_info.open_id;
    http.post('/auto_test/access_stat', {
        user_id: open_id,
        page_url: '/workbench/team',
        page_title: '团队工作台',
    })
});


</script>


<style lang="scss" scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input,
    .el-select {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;

    .el-select {
        width: 500px;
    }

}

.el-aside {
    width: 200px;
    height: 100% !important;

    margin-top: 20px;
}

.custom-html {
    white-space: pre-wrap;

    a {
        color: #3370ff !important;
    }
}

.tp .el-form-item {
    margin: 0px;
}

:deep(.el-tree-node__content) {
    height: 40px;
}
</style>