<template>
  <div class="doc-generator-form">

    <div class="form-group">
      <label>文档生成范围</label>
      <div class="radio-group">
        <div class="radio-item">
          <input 
            id="generateAll" 
            v-model="config.generateAll" 
            type="radio" 
            :value="true" 
          />
          <label for="generateAll">生成全部文档</label>
        </div>
        <div class="radio-item">
          <input 
            id="generateSelected" 
            v-model="config.generateAll" 
            type="radio" 
            :value="false" 
          />
          <label for="generateSelected">仅生成选择部分</label>
        </div>
      </div>
    </div>



    <div class="form-group">
      <label for="format">输出格式</label>
      <select id="format" v-model="config.format">
        <option value="html">json</option>
      </select>
    </div>
    
    <div class="form-actions">
      <button class="cancel-btn" @click="$emit('cancel')">取消</button>
      <button class="save-btn" @click="save">保存</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  initialConfig: {
    type: Object,
    default: () => ({
      outputDirectory: '',
      generateAll: true,
      format: 'json'
    })
  }
});

const emit = defineEmits(['save', 'cancel']);
const config = ref({
  outputDirectory: '',
  generateAll: true,
  docTypes: {
    api: true,
    userManual: true,
    devGuide: true
  },
  format: 'html',
  ...props.initialConfig
});

// 选择目录（模拟）
const browseDirectory = () => {
  // 实际环境下，这里会调用系统的目录选择对话框
  console.log('打开目录选择对话框');
  // 模拟选择目录
  config.value.outputDirectory = '/output/docs';
};

const save = () => {
  // 验证输出目录
  if (!config.value.outputDirectory) {
    config.value.outputDirectory = './docs';
  }
  
  console.log('文档生成配置被保存');
  console.log(config.value);
  emit('save', config.value);  
};
</script>

<style scoped>
.doc-generator-form {
  width: 100%;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

input[type="text"], select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #1f2937;
  transition: border-color 0.2s;
}

input:focus, select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.directory-input {
  display: flex;
  gap: 8px;
}

.directory-input input {
  flex: 1;
}

.browse-btn {
  padding: 0 16px;
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.browse-btn:hover {
  background-color: #e5e7eb;
}

.radio-group, .checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.radio-item, .checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.radio-item label, .checkbox-item label {
  margin-bottom: 0;
  font-weight: normal;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 36px;
}

button {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.cancel-btn:hover {
  background-color: #e5e7eb;
}

.save-btn {
  background-color: #2563eb;
  color: white;
  border: 1px solid #2563eb;
}

.save-btn:hover {
  background-color: #1d4ed8;
}
</style> 