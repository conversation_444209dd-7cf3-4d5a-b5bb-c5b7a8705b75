<template>

    <div style="display: flex; flex-direction: column; height: calc(100vh - 250px);">
        
        <el-table :data="tableData" stripe border style="width: 100%;flex: 1;">
            <el-table-column prop="name" label="设备名称" width="200" align="center"></el-table-column>
            <el-table-column prop="desc" label="设备描述" min-width="300" align="center"></el-table-column>
            <el-table-column prop="station_name" label="工位名称" width="200" align="center"></el-table-column>
            <el-table-column prop="device_id" label="设备ID" width="200" align="center"></el-table-column>
            <el-table-column prop="psn" label="PSN" width="200" align="center"></el-table-column>
            <el-table-column label="操作" width="200" fixed="right" align="center">
                <template #default="{ row }">
                    <div>
                        <el-button type="primary" size="small" @click="handleDetail(row)">详情</el-button>
                    </div>
                </template>
            </el-table-column>

        </el-table>
    </div>

</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();


const wsConnected = ref(false);
let ws = null;
let wsStop = false;
const tableData = ref([]);


const initWebSocket = () => {
    const wsUrl = import.meta.env.VITE_AM_BASE_WS_URL + '/monitor/devices';
    ws = new WebSocket(wsUrl);
    
    ws.onopen = () => {
        wsConnected.value = true;
    };
    
    ws.onmessage = (event) => {
        try {
            const data = JSON.parse(event.data);
            tableData.value = data;
        } catch (error) {
            console.error('处理 WebSocket 消息时出错:', error);
        }
    };
    
    ws.onclose = () => {
        if (wsStop) {
            return;
        }
        wsConnected.value = false;
        ElMessage.warning('设备监控连接已断开，正在尝试重新连接...');
        setTimeout(initWebSocket, 1000);
    };
    
    ws.onerror = (error) => {
        wsConnected.value = false;
        ElMessage.error('设备监控连接错误');
        console.error('WebSocket 错误:', error);
    };
};

function handleDetail(row) {
    let key = row.key;
    router.push({ path: '/am_devices/' + key });
}


onMounted(() => {
    initWebSocket();
});

onBeforeUnmount(() => {
    wsStop = true;
    if (ws) {
        ws.close();
        ws = null;
    }
});


</script>

<style lang="scss" scoped>
</style>