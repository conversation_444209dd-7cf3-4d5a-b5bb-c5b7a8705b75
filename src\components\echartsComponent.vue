<template>
    <div ref="chartRef" :style="{width: props.width, height: props.height}"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
import { useResizeObserver } from '@vueuse/core';

const props = defineProps({
    options: {
        type: Object,
        required: true,
    },
    width: {
        type: String,
        default: '100%',
    },
    height: {
        type: String,
        default: '100%',
    },
});

const chartRef = ref(null);
let chartInstance = null;

onMounted(() => {
    chartInstance = echarts.init(chartRef.value);
    chartInstance.setOption(props.options);

    useResizeObserver(chartRef, () => {
        if (chartInstance) {
            chartInstance.resize();
        }
    });
});

onBeforeUnmount(() => {
    if (chartInstance) {
        chartInstance.dispose();
    }
});

watch(
    () => props.options,
    (newOptions) => {
        if (chartInstance) {
            chartInstance.setOption(newOptions);
        }
    },
    { deep: true }
);
</script>

<style scoped>

</style>