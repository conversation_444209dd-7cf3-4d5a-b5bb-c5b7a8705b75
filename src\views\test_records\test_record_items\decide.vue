<template>
    <div class="button-container">
        <el-button size="large" @click="onCancel">取消</el-button>
        <el-button size="large" @click="onNg">NG</el-button>
        <el-button size="large" type="primary" @click="onPass">PASS</el-button>
    </div>

</template>

<script setup>
import { ref } from 'vue';
import http from '@/utils/http/http.js';

const props = defineProps({
    r_id: {
        type: Number,
        required: true,
    },
});

const emit = defineEmits(['pass', 'ng', 'cancel']);

const onCancel = () => {
    emit('cancel');
}

const onPass = () => {
    http.post('/test_records/items/decide', {
        id: props.r_id,
        result: 1,
    }).then(res => {
        emit('pass');
    }).catch(err => {
        ElMessageBox.alert(
            err.response.data.msg,
            '警告',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
    });
}

const onNg = () => {
    http.post('/test_records/items/decide', {
        id: props.r_id,
        result: 0,
    }).then(res => {
        emit('ng');
    }).catch(err => {
        ElMessageBox.alert(
            err.response.data.msg,
            '警告',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
    });
}

</script>

<style lang="scss" scoped>
.button-container {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    height: 150px;

    .el-button {
        margin: 0 10px;
        width: 100px;
        height: 40px;
    }
}
</style>
