@charset "UTF-8";

:export {
  menuText: #bfcbd9;
  menuActiveText: #409EFF;
  subMenuActiveText: #f4f4f5;
  menuBg: #304156;
  menuHover: #263445;
  subMenuBg: #1f2d3d;
  subMenuHover: #001528;
  sideBarWidth: 210px;
}

.app-container {
  height: 100%;
}

.nav-container {
  height: 50px;
  background: #fff;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  padding: 0;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload input[type=file] {
  display: none !important;
}

.el-upload__input {
  display: none;
}

.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.upload-container .el-upload {
  width: 100%;
}

.upload-container .el-upload .el-upload-dragger {
  width: 100%;
  height: 200px;
}

.el-dropdown-menu a {
  display: block;
}

.el-range-separator {
  box-sizing: content-box;
}

body {
  height: 100vh;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  font-size: 14px;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0;
}

.full-height {
  height: 100%;
}

.no-padding {
  padding: 0 !important;
}

.tool-bar-container {
  padding-bottom: 5px;
}

.pagination-container {
  display: flex;
  justify-content: flex-start;
  padding: 10px 0;
}

.el-select-dropdown .el-select-dropdown__footer {
  padding: 0;
}

.custom-html a {
  color: #3370ff;
  background-color: #f1f1f1;
  padding: 4px;
}

/* nprogress-custom.css */
#nprogress .bar {
  background: #409EFF;
  /* 进度条颜色 */
}

#nprogress .peg {
  box-shadow: 0 0 10px #409EFF, 0 0 5px #409EFF;
  /* 进度条阴影颜色 */
}

#nprogress .spinner-icon {
  border-top-color: #409EFF;
  /* Spinner 顶部颜色 */
  border-left-color: #409EFF;
  /* Spinner 左侧颜色 */
}

:root {
  --fc-button-bg-color: #409eff;
  --fc-button-border-color: #409eff;
  --fc-button-hover-bg-color: #79bbff;
  --fc-button-hover-border-color: #79bbff;
  --fc-button-active-bg-color: #337ecc;
  --fc-button-active-border-color: #337ecc;
}

.el-form-item .el-form-item__label-wrap .el-form-item__label {
  font-weight: 700;
}

.el-menu-vertical:not(.el-menu--collapse) {
  width: 180px;
}

.el-menu {
  border: none;
}

.el-menu-item {
  margin: 0 10px;
  border-radius: 5px;
}

.el-sub-menu__title {
  margin: 0 10px;
}

.el-menu-item .el-icon {
  color: #c6c6c6;
}

.el-menu-item.is-active {
  background-color: #ecf5ff;
}

.el-menu-item:hover:not(.is-active) {
  background-color: #ffffff;
}

.el-sub-menu .el-icon {
  color: #c6c6c6;
}

.el-sub-menu.is-active .el-sub-menu__title,
.el-sub-menu.is-active .el-icon {
  color: #409eff;
}

.el-sub-menu .el-sub-menu__title:hover {
  background-color: #ffffff;
}

.el-sub-menu:hover:not(.is-active) {
  background-color: #ffffff;
}

.el-menu--collapse .el-tooltip__trigger {
  display: flex;
  justify-content: center;
}

.el-menu-vertical {
  transition: width 0.3s ease;
}

.el-menu-item {
  transition: all 0.3s ease;
}

.el-menu-item .el-icon,
.el-sub-menu .el-icon {
  transition: transform 0.3s ease;
}

.el-menu--collapse .el-menu-item .el-icon,
.el-menu--collapse .el-sub-menu .el-icon {
  transform: scale(1.2);
}

@keyframes icon-shrink-grow {
  0% {
    transform: scale(1.2);
  }

  50% {
    transform: scale(0.5);
  }

  100% {
    transform: scale(1);
  }
}

.el-menu-vertical:not(.el-menu--collapse) .el-menu-item .el-icon,
.el-menu-vertical:not(.el-menu--collapse) .el-sub-menu__title .el-icon {
  animation: icon-shrink-grow 0.3s ease;
}

/*# sourceMappingURL=index.css.map */