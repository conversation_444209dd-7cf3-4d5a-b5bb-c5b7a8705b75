<template>

    <div style="width: 100%;padding: 20px;">
        <el-row :gutter="10">
                <el-col :span="10">
                    <div style="display: flex;justify-content: center;width: 100%;"><span>选项标签</span></div>
                </el-col>
                <el-col :span="10">
                    <div style="display: flex;justify-content: center;width: 100%"><span>选项值</span></div>
                </el-col>
            </el-row>

        <div v-for="(v, index) in model" class="option_item">
            <el-row :gutter="10">
                <el-col :span="10">
                    <el-input v-model="model[index].label"></el-input>
                </el-col>
                <el-col :span="10">
                    <el-input v-model="model[index].value"></el-input>
                </el-col>
                <el-col :span="4">
                    <el-button type="danger" plain @click="handleDelete(index)" icon="Minus"></el-button>
                </el-col>

            </el-row>
            
        </div>
        <el-button type="primary" plain @click="handleAdd" icon="Plus">添加选项</el-button>

    </div>

</template>

<script setup>
const model = defineModel();

function handleDelete(index) {
    if (model.value.length === 1) {
        return
    }
    model.value.splice(index, 1)
}

function handleAdd() {
    model.value.push({label: '', value: ''})
}

</script>

<style lang="scss" scoped>
.option_item {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.option_item:not(:last-child) {
    margin-bottom: 10px;
}
</style>