<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="机台编号" prop="m_number">
                <el-input v-model="form.m_number" placeholder="请输入机台编号" readonly></el-input>
            </el-form-item>

            <el-form-item label="机台名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入机台名称"></el-input>
            </el-form-item>

            <el-form-item label="机台类型">
                <el-input v-model="form.type" placeholder="请输入机台类型"></el-input>
            </el-form-item>

            <el-form-item label="机台资产编号">
                <el-input v-model="form.number" placeholder="请输入机台资产编号"></el-input>
            </el-form-item>

            <el-form-item label="机台位置">
                <el-input v-model="form.position" placeholder="请输入机台机台位置"></el-input>
            </el-form-item>

            <el-form-item label="机台描述">
                <el-input v-model="form.desc" placeholder="请输入机台描述"></el-input>
            </el-form-item>

            <el-form-item label="机台维护人" prop="maintainer">
                <Organizaiton v-model="form.maintainer" ref="maintainerRef" :cache-data="cacheData" />
            </el-form-item>

            <el-form-item label="关联设备">
                <el-select v-model="form.devices" multiple>
                    <el-option v-for="item in devices" :label="item.name + '(' + item.d_number + ')'"
                        :value="item.id"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="关联功能">
                <el-select v-model="form.functions" multiple>
                    <el-option v-for="item in functions" :label="item.name" :value="item.id"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="机台状态">
                <el-select v-model="form.status">
                    <el-option label="空闲中" value="0"></el-option>
                    <el-option label="使用中" value="1"></el-option>
                    <el-option label="报修中" value="2"></el-option>
                    <el-option label="维修中" value="3"></el-option>
                    <el-option label="保养中" value="4"></el-option>
                    <el-option label="报废" value="5"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="多项目共用">
                <el-switch v-model="form.is_share" size="large" inline-prompt active-text="是" inactive-text="否" />
            </el-form-item>

            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onAffirm">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';

import http from '@/utils/http/http.js';

import Organizaiton from '@/components/Organization/index.vue';

const props = defineProps({
    r_id: {
        type: Number,
        required: true,
    },
});

const emit = defineEmits(['affirm', 'cancel'])

const formRef = ref(null);

const cacheData = ref([]);

const maintainerRef = ref(null);

const form = ref({
    name: '',
    type: '',
    number: '',
    position: '',
    desc: '',
    maintainer: '',
    devices: [],
    functions: [],
    status: '0',
    is_share: false,
    m_number: '',
});

const rules = ref({
    name: [
        { required: true, message: '请输入机台名称', trigger: 'blur' },
    ],
    maintainer: [
        { required: true, message: '请选择机台维护人', trigger: 'blur' },
    ],
    m_number: [
        { required: true, message: '请输入机台编号', trigger: 'blur' },
    ],
});

let devices = ref([]);
let functions = ref([]);

const onAffirm = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            let data = {
                ...form.value,
            };
            
            data.maintainer = maintainerRef.value.getNode(form.value.maintainer).data.employeeNo;
            
            http.put(`/machines/${props.r_id}`, data).then(res => {
                emit('affirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '编辑失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

function onCancel() {
    emit('cancel');
};

onMounted(() => { 
    http.get('/machines/devices', { params: { page: 1, pagesize: 1000 } }).then(res => {
        devices.value = res.data.data.results.filter(item => item.machine_id === null || item.machine_id === props.r_id);
    });

    http.get('/machines/functions').then(res => {
        functions.value = res.data.data.results;
    });
   
    if (props.r_id) {
        http.get(`/machines/${props.r_id}`).then(res => {
            form.value.name = res.data.data.name;
            form.value.number = res.data.data.number;
            form.value.desc = res.data.data.desc;
            form.value.m_number = res.data.data.m_number;
            form.value.position = res.data.data.position;
            form.value.type = res.data.data.type;
            form.value.devices = res.data.data.devices.map(item => item.id);
            form.value.functions = res.data.data.functions.map(item => item.id);
            form.value.status = res.data.data.status.toString();
            form.value.is_share = res.data.data.is_share;
            form.value.maintainer = res.data.data.email;

            cacheData.value = [{  
                label: res.data.data.username,
                value: res.data.data.email,
                data: {employeeNo: res.data.data.maintainer}
            }]; 
        });
    };
});

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>
