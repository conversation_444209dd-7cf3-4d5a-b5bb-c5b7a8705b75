<template>

    <el-form style="padding: 0 20px; text-align: left;">
        <el-form-item label="项目：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 85px;"
            >{{ record.project_name }}({{ record.project_number }})</span>
        </el-form-item>
        <el-form-item label="零件号：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 69px;"
            >{{ record.component_code }}</span>
        </el-form-item>
        <el-form-item label="软件版本：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 55px;"
            >{{ record.software_version }}</span>
        </el-form-item>
        <el-form-item label="显示与触摸屏版本：" style="font-weight: bold;">
            <span
            style="font-weight: normal;"
            >{{ record.screen_touchpad_version }}</span>
        </el-form-item>
        <el-form-item label="打包软件版本：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 30px;"
            >{{ record.packer_version }}</span>
        </el-form-item>

        <template v-for="(item, index) in record.input_files">
            <el-form-item :label="`源文件${index + 1}标识：`" style="font-weight: bold;">
                <span
                style="font-weight: normal; margin-left: 35px;"
                >{{ item.identification }}</span>
            </el-form-item>
            <el-form-item :label="`源文件${index + 1}：`" style="font-weight: bold;">
                <span
                style="font-weight: normal; margin-left: 63px;"
                >{{ item.file }}</span>
            </el-form-item>
            <el-form-item :label="`源文件${index + 1}MD5：`" style="font-weight: bold;">
                <span
                style="font-weight: normal; margin-left: 33px;"
                >{{ item.md5 }}</span>
            </el-form-item>
        </template>

        <el-form-item label="输出文件类型：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 30px;"
            >{{ record.output_type }}</span>
        </el-form-item>
        <el-form-item label="s19格式：" v-if="record.output_type === 's19'" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 63px;"
            >s{{ record.srecord_type }}</span>
        </el-form-item>
        <el-form-item label="输出文件名：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 45px;"
            >{{ record.output_file_name }}</span>
        </el-form-item>
        <el-form-item label="输出文件MD5：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 30px;"
            >{{ record.output_file_md5 }}</span>
        </el-form-item>
        <el-form-item label="PMIC升级：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 52px;"
            >{{ record.pmic_flag ? "是" : "否" }}</span>
        </el-form-item>
        <el-form-item label="加密：" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 87px;"
            >{{ record.encrypt ? "是" : "否" }}</span>
        </el-form-item>
        <el-form-item label="加密文件：" v-if="record.encrypt" style="font-weight: bold;">
            <span
            style="font-weight: normal; margin-left: 55px;"
            >{{ record.encrypt_file }}</span>
            <span>{{ record.encrypt_file }}</span>
        </el-form-item>

    </el-form>

</template>

<script setup>
import { ref, onMounted } from 'vue'
import http from '@/utils/http/http.js';

const props = defineProps({
    id: {
        type: Number,
        default: -1,
    }
})

const record = ref({});


onMounted(() => {
    http.get(`/pack_tool/records/${props.id}`).then(res => {
        let data = res.data.data;
        record.value = data;
    }).catch(err => {
        console.log(err);
    });
});

</script>

<style lang="scss" scoped></style>