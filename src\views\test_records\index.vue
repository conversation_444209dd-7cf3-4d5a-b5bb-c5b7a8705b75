<template>

    <div class="tool-bar-container">

        <div style="margin-left: auto; display: flex; gap: 10px;">
            <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                <el-button text bg @click="handleReset">重置</el-button>
            </el-tooltip>
            <filterButton @click="onFilterStatusChange" :count="filterCount" />
            <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
        </div>
    </div>

    <div class="filter-container" v-if="showFilterContainer">
        <el-input v-model="form.test_plan_name" placeholder="请输入计划名称" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
        <el-input v-model="form.test_sub_plan_name" placeholder="请输入子计划名称" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
        <el-input v-model="form.tester_name" placeholder="请输入测试人" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
        <el-input v-model="form.machine_name" placeholder="请输入测试机台" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
        <el-input v-model="form.test_plan_id" placeholder="请输入测试计划ID" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
    </div>

    <el-table :data="tableData" stripe border style="width: 100%" class="table-container">

        <el-table-column prop="project_number" label="项目编号" width="150" align="center"></el-table-column>
        <el-table-column prop="project_name" label="所属项目" width="200" align="center"></el-table-column>
        <el-table-column prop="tester_name" label="测试人" width="100" align="center"></el-table-column>
        <el-table-column label="测试状态" width="100" align="center">
            <template #default="{ row }">
                <el-tag v-if="row.status == 0" type="warning">测试中</el-tag>
                <el-tag v-if="row.status == 1" type="success">测试完成</el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="create_time" label="开始时间" width="180" align="center"></el-table-column>
        <el-table-column prop="update_time" label="更新时间" width="180" align="center"></el-table-column>
        <el-table-column prop="test_plan_name" label="计划名称" width="200" align="center"></el-table-column>
        <el-table-column prop="test_sub_plan_name" label="子计划名称" width="200" align="center"></el-table-column>
        <el-table-column label="测试机台" width="300" align="center">
            <template #default="{ row }">
                <span>{{ row.machine_name + "(" + row.machine_number + ")" }}</span>
            </template>
        </el-table-column>
        <el-table-column label="操作" min-width="100" fixed="right" align="left">
            <template #default="{ row }">
                <el-button type="primary" size="small" @click="handledetail(row)">查看记录</el-button>
            </template>
        </el-table-column>

    </el-table>

    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
            @change="onPageChange" />
    </div>

</template>


<script setup>
import { ref, reactive, watch, onActivated, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import { useRoute, useRouter } from 'vue-router';
import { useProjectStore } from '@/stores/project.js';
import filterButton from '@/components/filterButton.vue';
import { useTestRecordStore } from '@/stores/testRecord.js';

const router = useRouter();
const route = useRoute();
let projectStore = useProjectStore();
const tableData = ref([]);
const filterCount = ref(0);
let testRecordStore = useTestRecordStore();

let form = reactive({
    project_number: '',
});

let total = ref(0);

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/test_records', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'project_number'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function onPageChange() {
    update_table();
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handledetail(row) {
    router.push({ path: "/test_records/items/", query: { id: row.id } });
};

function handleRefresh() {
    update_table();
};

watch(() => projectStore.project_info, () => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

onActivated(() => {
    if (testRecordStore.testPlanId) {
        form.test_plan_id = testRecordStore.testPlanId;
        testRecordStore.clearTestPlanId();
    }
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

</script>


<style lang="scss" scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;

    .el-select {
        width: 500px;
    }

}

.el-aside {
    width: 200px;
    height: 100% !important;

    margin-top: 20px;
}

.el-main {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);

    margin-top: 0px;

    .table-container {
        flex: 1;
        overflow: auto;
    }
}
</style>