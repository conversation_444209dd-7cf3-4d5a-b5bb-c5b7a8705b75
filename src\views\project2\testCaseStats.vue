<template>
    <el-row :gutter="20" style="margin-bottom: 10px;">
        <el-col :span="12">
            <el-card style="width: 100%;">
                <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h3>{{ title }}</h3>
                    </div>
                </template>
                <EchartsComponent :options="options" height="300px" />
            </el-card>
        </el-col>
        <el-col :span="12">
            <el-card style="width: 100%;">
                <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h3>{{ activeTypeTitle }}</h3>
                    </div>
                </template>
                <EchartsComponent :options="activeTypeOptions" height="300px" />
            </el-card>
        </el-col>
    </el-row>

   
<!-- <div class="echarts_row" style="display: flex; flex-direction: column;"> -->
<el-row :gutter="20" style="margin-bottom: 10px;">
    <el-col :span="12">
        <el-card style="width: 100%;margin-bottom: 10px;">
            <template #header>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>{{ operateTitle }}</h3>
                </div>
            </template>
            <EchartsComponent :options="operateOptions" height="300px" />
        </el-card>
    </el-col>

    <el-col :span="12">
        <el-card style="width: 100%;margin-bottom: 10px;">
            <template #header>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>{{ ATMTitle }}</h3>
                </div>
            </template>
            <EchartsComponent :options="ATMOptions" height="300px" />
        </el-card>
    </el-col>
</el-row>

<!-- </div> -->
    <!-- 调整位置到下面 -->
    <ActionTypeModuleStats />
</template>

<script setup>
import { ref, onMounted, inject, watch } from 'vue';
import EchartsComponent from '@/components/echartsComponent.vue';
import http from '@/utils/http/http.js';
import ActionTypeModuleStats from './actionTypeModuleStats.vue';

const title = ref('');
const options = ref({
    legend: {
        orient: 'vertical',
        left: 10,
        top: 10,
    },
    series: {
        type: 'pie',
        stillShowZeroSum: false,
        data: [],
    },
});

const activeTypeTitle = ref('活动类型统计');
const activeTypeOptions = ref({
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow',
        },
    },
    legend: {
        orient: 'vertical',
        left: 10,
        top: 10,
    },
    grid: {
        left: '200px',
    },
    xAxis: {
        type: 'category',
        data: []
    },
    yAxis: {
        type: 'value',
    },
    series: [],
});

const operateTitle = ref('每周用例修改统计');
const operateOptions = ref(
    {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow',
            },
        },
        legend: {
            orient: 'vertical',
            left: 10,
            top: 10,
        },
        grid: {
            left: '200px',
        },
        xAxis: {
            data: []
        },
        yAxis: {
            type: 'value',
        },
        series: [],
    }
)

const ATMTitle = ref('每周自动化率');
const ATMOptions = ref(
    {
        legend: {
            orient: 'vertical',
            left: 10,
            top: 10,
        },
        grid: {
            left: '200px',
        },
        xAxis: {
            data: []
        },
        yAxis: {
            max: 1,
            axisLabel: {
                formatter: function (value) {
                    return (value * 100).toFixed(0) + '%'; // 将值显示为百分比
                }
            }
        },
        series: []
    }
)

const project_number = inject('project_number');


watch(() => project_number.value, (newVal) => {
    if (newVal) {
        http.get('/projects/test_case_stats', { params: { number: newVal } }).then((res) => {
            let data = res.data.data;

            const autoRate = Math.round(data.auto_rate * 100);
            title.value = `用例总数: ${data.total} / 自动化率: ${autoRate}%`;
            options.value.series.data = data.auto_pie.map(item => ({
                name: `${item.name} (${item.value})`,
                value: item.value,
            }));

            activeTypeOptions.value.xAxis.data = data.action_type_bar.labels;
            activeTypeOptions.value.series = data.action_type_bar.data.map(item => ({
                name: item.name,
                type: 'bar',
                stack: '总量',
                data: item.data,
                label: {
                    show: true,
                    position: 'top',
                    formatter: (params) => {
                        if (item.name === '手动测试用例') {
                            const autoItem = data.action_type_bar.data.find(d => d.name === '自动化测试用例');
                            const autoValue = autoItem ? autoItem.data[params.dataIndex] : 0;
                            const semiAutoItem = data.action_type_bar.data.find(d => d.name === '半自动测试用例');
                            const semiAutoValue = semiAutoItem ? semiAutoItem.data[params.dataIndex] : 0;
                            const total = data.action_type_bar.data.reduce((sum, d) => sum + d.data[params.dataIndex], 0);
                            const combinedValue = autoValue + semiAutoValue;
                            if (total > 0) {
                                const percentage = ((combinedValue / total) * 100).toFixed(0);
                                return `自动化率${percentage}%`;
                            }
                        }
                        return '';
                    },
                },
            }));

            operateTitle.value = '每周用例修改统计' + `(${data.operate_bar.start_time} ~ ${data.operate_bar.end_time})`;
            operateOptions.value.xAxis.data = data.operate_bar.labels;
            operateOptions.value.series = data.operate_bar.data.map(item => ({
                name: item.name,
                type: 'bar',
                data: item.data,
            }));

            ATMOptions.value.xAxis.data = data.at_m_line.labels;
            ATMOptions.value.series = data.at_m_line.data.map(item => ({
                data: item.data,
                name: item.name,
                type: 'line',
            }));

        });
    }
}, { immediate: true });

</script>

<style scoped>
:deep(.el-card__header) {
    border-bottom: 0;
    padding: 0 10px;
}
</style>