<template>

    <div style="width: 100%;">

        <el-form-item :label="`源文件${props.index}标识`" style="margin-bottom: 20px;" :rules="[{ required: true, message: '请输入源文件标识', trigger: 'blur' }]">
            <el-input v-model="model.identification" placeholder="源文件标识"></el-input>
        </el-form-item>

        <el-form-item :label="`源文件${props.index}`" style="margin-bottom: 20px;" :rules="[{ required: true, message: '请输入源文件标识', trigger: 'blur' }]">
            <el-upload action="" :auto-upload="false" :limit="1" :on-exceed="handleFileExceed"
                :on-change="val => model.file = val" ref="fileRef" accept=".hex, .bin, .s19">
                <el-button slot="trigger" type="primary" plain>选择源文件</el-button>
                <div slot="tip" style="margin-left: 10px;">只能上传一个文件(.bin, .hex, .s19)</div>
            </el-upload>
        </el-form-item>

        <el-form-item :label="`源文件${props.index}CRC校验`" style="margin-bottom: 20px;" :rules="[{ required: true, message: '请选择CRC校验方式', trigger: 'blur' }]">
            <el-select v-model="model.crc_vaild" placeholder="请选择源文件CRC校验">
                <el-option label="无校验" value="00"></el-option>
                <el-option label="校验" value="01"></el-option>
            </el-select>
        </el-form-item>

    </div>

</template>

<script setup>
import { ref } from 'vue'
import { genFileId } from 'element-plus'

const props = defineProps(
    {
        index: Number,
    }
)

const model = defineModel();

const fileRef = ref(null)

const handleFileExceed = (files) => {
    fileRef.value.clearFiles()
    const file = files[0]
    file.uid = genFileId()
    fileRef.value.handleStart(file)
}

</script>

<style lang="scss" scoped></style>