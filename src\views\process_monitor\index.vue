<template>
    <div style="display: flex; flex-direction: column; height: calc(104vh - 250px);">

        <div class="tool-bar-container">

            <div style="margin-left: auto; display: flex; gap: 10px;">
                <div>
                    <el-button icon="Setting" text bg @click="isSettingVisible = true">设置</el-button>
                    <!-- 设置弹窗 -->
                    <el-popover v-model:visible="isSettingVisible" width="180" trigger="manual" placement="bottom">
                        <template #reference>
                            <!-- 设置按钮作为触发器 -->
                            <div style="display: inline-block;"></div>
                        </template>
                        <!-- 操作按钮 -->
                        <div class="column-popper-title">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <el-checkbox :model-value="tableColumns.every(item => item.show)"
                                    :indeterminate="tableColumns.some(item => item.show) && tableColumns.some(item => !item.show)"
                                    label="列展示" @change="selectAllColumn" />
                                <el-button text @click="resetColumns" style="margin-right: -10px;">重置</el-button>
                            </div>
                        </div>
                        <!-- 列设置内容 -->
                        <div class="column-content" style="max-height: 200px; overflow-y: auto;">
                            <div class="column-item" v-for="column in tableColumns" :key="column.key">
                                <el-checkbox v-model="column.show" :label="column.name"
                                    :disabled="column.disabled"></el-checkbox>
                            </div>
                        </div>
                    </el-popover>

                </div>
                <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                    <el-button text bg @click="handleReset">重置</el-button>
                </el-tooltip>
                <filterButton @click="onFilterStatusChange" :count="filterCount" />
                <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
            </div>
        </div>

        <div class="filter-container" v-if="showFilterContainer">

            <el-input v-model="form.run_id" placeholder="请输入运行ID" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>

            <el-input v-model="form.code" placeholder="请输入异常编号" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>

            <el-input v-model="form.name" placeholder="请输入异常" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>

            <el-input v-model="form.psn" placeholder="请输入PSN" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>

        </div>

        <el-table :data="tableData" stripe border style="width: 100%;flex: 1;" class="table-container">
            <el-table-column v-if="tableColumns[0].show" label="所属项目" width="300" align="center">
                <template #default="{ row }">
                    <span>{{ row.project_name }}({{ row.project_number }})</span>
                </template>
            </el-table-column>
            <el-table-column v-if="tableColumns[1].show" prop="test_plan_name" label="测试计划" min-width="200"
                align="center"></el-table-column>

            <!-- <el-table-column v-if="tableColumns[2].show" prop="run_id" label="运行ID" min-width="200"
                align="center"></el-table-column> -->
            <el-table-column v-if="tableColumns[3].show" prop="code" label="异常编号" min-width="200"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[4].show" prop="name" label="异常" min-width="200"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[5].show" prop="psn" label="PSN" min-width="200"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[6].show" prop="value" label="异常值" min-width="200"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[7].show" prop="cur_time" label="出现时间" min-width="200"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[8].show" prop="tester_name" label="测试人" min-width="200"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[9].show" label="额外消息" min-width="500" align="center">
                <template #default="{ row }">
                    <span>{{ row.extra_info }}</span>
                </template>
            </el-table-column>
            <el-table-column v-if="tableColumns[10].show" label="操作" min-width="90" fixed="right" align="center">
                <template #default="{ row }">
                </template>
            </el-table-column>

        </el-table>
    </div>

    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
            @change="onPageChange" />
    </div>

</template>


<script setup>
import { ref, reactive, watch, onMounted, onActivated } from 'vue';
import http from '@/utils/http/http.js';
import { useProjectStore } from '@/stores/project.js';
import { useRoute } from 'vue-router';
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/process_monitor/list', '故障记录');

const tableColumns = ref([
    { key: "project_name", name: "所属项目", show: true, disabled: true },
    { key: "test_plan_name", name: "测试计划", show: true },
    { key: "run_id", name: "运行ID", show: true },
    { key: "code", name: "异常编号", show: true, disabled: true },
    { key: "name", name: "异常", show: true },
    { key: "psn", name: "设备编号", show: true },
    { key: "value", name: "异常值", show: true },
    { key: "cur_time", name: "出现时间", show: true },
    { key: "tester", name: "测试人", show: true },
    { key: "extra_info", name: "额外消息", show: true },
    { key: "operation", name: "操作", show: true }
]);

// 设置弹窗的显示状态
const isSettingVisible = ref(false);

// 全选或取消全选逻辑
const selectAllColumn = (checked) => {
    tableColumns.value.forEach((column) => {
        if (!column.disabled) { // 跳过禁用的列
            column.show = checked;
        }
    });
};


// 重置列设置
const resetColumns = () => {
    tableColumns.value.forEach((column) => {
        if (!column.disabled) { // 跳过禁用的列
            column.show = true;
        }
    });
};

let projectStore = useProjectStore();
let filterCount = ref(0);
const tableData = ref([]);
const route = useRoute();

let form = reactive({
    page: 1,
    pagesize: 10,
    project_number: '',
});

let total = ref(0);

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/process_monitor/exps', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'project_number'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleRefresh() {
    update_table();
};

watch(() => projectStore.project_info, () => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

onMounted(() => {
    if (route.query.version_name) {
        form.version_name = route.query.version_name;
    }
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

onActivated(() => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

</script>


<style lang="scss" scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input,
    .el-select {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;

    .el-select {
        width: 500px;
    }

}

.el-aside {
    width: 200px;
    height: 100% !important;

    margin-top: 20px;
}

.el-main {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);

    margin-top: 0px;

    .table-container {
        flex: 1;
        overflow: auto;
    }
}


.custom-html {
    white-space: pre-wrap;

    a {
        color: #3370ff !important;
    }
}

.tp .el-form-item {
    margin: 0px;
}

.column-popper-title {
    border-bottom: 1px solid #ebeef5;
}

/* 自定义滚动条样式 */
.column-content::-webkit-scrollbar {
    width: 6px;
    /* 滚动条宽度 */
}

.column-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    /* 轨道背景色 */
    border-radius: 4px;
    /* 轨道圆角 */
}

/* 滚动条滑块默认样式（浅色） */
.column-content::-webkit-scrollbar-thumb {
    background: #e4e3e3;
    /* 浅色 */
    border-radius: 4px;
    /* 滑块圆角 */
}

/* 滚动条滑块悬停样式（深色） */
.column-content::-webkit-scrollbar-thumb:hover {
    background: #c7c7c7;
    /* 深色 */
}
</style>