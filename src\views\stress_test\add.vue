<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form :rules="rules">
            <el-form-item label="所属项目" prop="project_number">
             <Projects style="margin-left: 18px" v-model="form.project_number" :includePrefix="false" :includeAll="false"
                :disabled="route.query.type === 'edit'" @change="onProjectChange"
                 ref="projectRef" />
             </el-form-item>
    <!-- 背景介绍 -->
    <el-divider>
            <span style="color: #606266; font-weight: bold;">背景介绍</span>
        </el-divider>
        <el-form-item label="问题描述">
            <el-input style="margin-left: 28px" v-model="form.verification.background.problem_description"></el-input>
        </el-form-item>
        <el-form-item label="软件版本">
            <el-input style="margin-left: 28px" v-model="form.verification.background.software_version"></el-input>
        </el-form-item>
        <el-form-item label="硬件版本">
            <el-input style="margin-left: 28px" v-model="form.verification.background.hardware_version"></el-input>
        </el-form-item>
        <el-form-item label="PSN">
            <el-input style="margin-left: 55px" v-model="form.verification.background.psn"></el-input>
        </el-form-item>
        <el-form-item label="问题来源">
            <el-input style="margin-left: 28px" v-model="form.verification.background.problem_source"></el-input>
        </el-form-item>

    <!-- 测试环境说明 -->
    <el-divider>
            <span style="color: #606266; font-weight: bold;">测试环境说明</span>
        </el-divider>
        <el-form-item label="样件数量">
            <el-input style="margin-left: 28px" v-model="form.verification.test_environment.sample_count"></el-input>
        </el-form-item>
        <el-form-item label="测试环境描述">
            <el-input v-model="form.verification.test_environment.description"></el-input>
        </el-form-item>

        <!-- 验证方向 -->
        <el-divider>
            <span style="color: #606266; font-weight: bold;">验证方向</span>
            </el-divider>
        <!-- <el-form-item label="调研思维导图">
            <el-input v-model="form.verification.validation_direction.mind_map"></el-input>
        </el-form-item> -->
        <el-form-item label="鱼骨图">
            <el-input style="margin-left: 42px" v-model="form.verification.validation_direction.fishbone_diagram"></el-input>
        </el-form-item>

     <!-- 验证方案 -->
        <el-divider>
            <span style="color: #606266; font-weight: bold;">验证方案</span>
        </el-divider>
    <el-form-item label="飞书云文档">
        <el-input style="margin-left: 14px" v-model="form.verification.validation_plan.feishu_doc" placeholder="请输入飞书云文档链接"></el-input>
    </el-form-item>

</el-form>
            <!-- 提交按钮 -->
            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref , onMounted, defineEmits, watch } from 'vue';
import http from '@/utils/http/http.js';
import { number } from 'echarts';
import Projects from '@/components/projects.vue';
import { useRouter, useRoute } from 'vue-router';

const formRef = ref(null);
const route = useRoute();
const projectRef = ref(null);
const test_types = ref([]);
const picRef = ref(null);
const projectVersionV = ref(true);
// const project_number = ref('');

// 定义 props，接收来自父组件的 project_number
const props = defineProps({
    project_number: {
        type: String,
        default: ''
    }
});

// 初始化表单数据
const form = ref({
  verification: {
    background: {
      hardware_version: '',
      problem_description: '',
      problem_source: '',
      psn: '',
      software_version: ''
    },
    test_environment: {
      sample_count: '',
      description: ''
    },
    validation_direction: {
      mind_map: '',
      fishbone_diagram: ''
    },
    validation_plan: {
      feishu_doc: ''
    },
    validation_result: {
      feishu_doc: ''
    }
  },
  name: '',
  project_number: props.project_number,
});

const rules = ref({
    name: [
        { required: true, message: '请输入项目名称', trigger: 'blur' },
    ],
    project_number: [
        { required: true, message: '请输入项目编号', trigger: 'blur' },
    ],
});

const emit = defineEmits(['affirm', 'cancel']);

// 提交表单
const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            // 调用 onSave 方法，处理保存逻辑
            onSave(true, () => {
                // 将 verification 字段转换为 JSON 字符串
                const payload = {
                    ...form.value,
                    verification: JSON.stringify(form.value.verification || {}),
                };

                http.post('/stress_tests', payload).then(res => {
                    ElMessage({
                        message: '添加成功.',
                        type: 'success',
                    });
                    emit('affirm');
                }).catch(err => {
                    ElMessageBox.alert(err.response.data.detail || '提交失败', '错误', {
                        confirmButtonText: '确定',
                        type: 'error',
                    });
                });
            });
        }
    });
};

// 保存方法
const onSave = (sync = false, callback = undefined) => {
    // 获取项目信息
    let pInfo = projectRef.value.getProjectInfo(form.value.project_number);
    form.value.project_id = pInfo?.id;
    form.value.project_name = pInfo?.name;

    // 获取测试类型名称
    form.value.test_type_name = test_types.value.find(item => item.remark == form.value.test_type)?.label;

    // 如果是同步保存且有回调函数，则执行回调
    if (sync && callback) {
        callback();
    }
};

// 取消操作
function onCancel() {
    emit('cancel');
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};
function onProjectChange() {
    projectVersionV.value = false;
    nextTick(() => {
        projectVersionV.value = true;
    });
    form.value.m_version = '';
    form.value.product_version = [];
    form.value.test_type = '';
    updateTestTypes();
};
// watch(
//   () => route.query.project_number,
//   (newVal) => {
//     if (newVal) {
//       project_number.value = newVal;
//       console.log('Received project_number:', project_number.value);
//     }
//   },
//   { immediate: true }
// );
</script>

<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>
