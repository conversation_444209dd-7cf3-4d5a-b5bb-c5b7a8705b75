<template>
    <div>
        <div class="message-container">
            <div v-for="(msg, index) in messages" :key="index">
                {{ msg }}
            </div>
        </div>
        
        <div class="controls">
            <el-input v-model="message" placeholder="Enter message"></el-input>
            <el-button @click="sendMessage" :disabled="!isConnected">Send</el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const ws = ref(null)
const isConnected = ref(false)
const message = ref('')
const messages = ref([])

const connectWebSocket = () => {
    const token = sessionStorage.getItem('token')

    ws.value = new WebSocket('ws://**********:9000/system/status' + '?token=' + token)
    
    ws.value.onopen = () => {
        console.log('Connected to WebSocket')
        isConnected.value = true
    }
    
    ws.value.onmessage = (event) => {
        messages.value.push(event.data)
    }
    
    ws.value.onerror = (error) => {
        console.error('WebSocket error:', error)
    }
    
    ws.value.onclose = () => {
        console.log('Disconnected from WebSocket')
        isConnected.value = false
    }
}

const sendMessage = () => {
    if (ws.value && isConnected.value && message.value) {
        ws.value.send(message.value)
        message.value = ''
    }
}

onMounted(() => {
    connectWebSocket()
})

onUnmounted(() => {
    if (ws.value) {
        ws.value.close()
    }
})
</script>

<style scoped>
.message-container {
    height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    margin-bottom: 10px;
}

.controls {
    display: flex;
    gap: 10px;
}
</style>
