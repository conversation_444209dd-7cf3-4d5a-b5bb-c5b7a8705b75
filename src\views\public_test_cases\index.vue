<template>
    <el-container>
        <el-header>
            <el-tabs v-model="activeTabName" class="tabs" @tab-click="handleTabClick">
                <el-tab-pane label="所有测试活动类型" name=""></el-tab-pane>
                <el-tab-pane label="系统合格性测试" name="SAT"></el-tab-pane>
                <el-tab-pane label="系统集成测试" name="IST"></el-tab-pane>
                <el-tab-pane label="软件合格性测试" name="SQT"></el-tab-pane>
                <el-tab-pane label="软件集成测试" name="SIT"></el-tab-pane>
                <el-tab-pane label="软件单元测试" name="SUT"></el-tab-pane>
                <el-tab-pane label="硬件测试" name="HT"></el-tab-pane>
            </el-tabs>
        </el-header>

        <el-container>
            <el-aside>
               
                <el-scrollbar style="height: 70vh;">
                    <ModuleNav @check="onModuleCheck" />
                </el-scrollbar>

            </el-aside>

            <el-main>

                <div class="tool-bar-container">
                    <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button>

                    <div style="margin-left: auto; display: flex; gap: 10px;">
                        <div>
                    <el-button icon="Setting" text bg @click="isSettingVisible = true">设置</el-button>
                      <!-- 设置弹窗 -->
                            <el-popover
                            v-model:visible="isSettingVisible"
                            width="180"
                            trigger="manual"
                            placement="bottom"
                            >
                            <template #reference>
                            <!-- 设置按钮作为触发器 -->
                            <div style="display: inline-block;"></div>
                            </template>
                            <!-- 操作按钮 -->
                            <div class="column-popper-title">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                        <el-checkbox
                        :model-value="tableColumns.every(item => item.show)"
                            :indeterminate="tableColumns.some(item => item.show) && tableColumns.some(item => !item.show)"
                            label="列展示"
                                @change="selectAllColumn"
                            />
                        <el-button text @click="resetColumns" style="margin-right: -10px;">重置</el-button>
                        </div>
                        </div>
                        <!-- 列设置内容 -->
                            <div class="column-content" style="max-height: 200px; overflow-y: auto;">
                            <div class="column-item" v-for="column in tableColumns" :key="column.key">
                            <el-checkbox v-model="column.show" :label="column.name"  :disabled="column.disabled"></el-checkbox>
                            </div>
                        </div>
                        </el-popover>
                        
                        </div>
                        <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                            <el-button text bg @click="handleReset">重置</el-button>
                        </el-tooltip>
                        <filterButton @click="onFilterStatusChange" :count="filterCount" />
                        <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
                    </div>
                </div>

                <div class="filter-container" v-if="showFilterContainer">
                    <el-input v-model="form.name_re" placeholder="请输入用例名称" @keyup.enter="onFilter" clearable>
                        <template #append>
                            <el-button icon="Search" @click="onFilter"></el-button>
                        </template>
                    </el-input>
                    <el-select v-model="form.priority_list" placeholder="请输入优先级" @change="onFilter" multiple clearable>
                        <el-option label="高" value="HIGH"></el-option>
                        <el-option label="中" value="MIDDLE"></el-option>
                        <el-option label="低" value="LOW"></el-option>
                    </el-select>
                    <el-select v-model="form.execute_mode_list" placeholder="请选择执行方式" @change="onFilter" multiple
                        clearable>
                        <el-option label="自动化测试" value="AUTOMATED_EXECUTION"></el-option>
                        <el-option label="手动测试" value="MANUAL_EXECUTION"></el-option>
                        <el-option label="半自动化测试" value="SEMI_AUTOMATED_EXECUTION"></el-option>
                    </el-select>
                </div>

                <el-table :data="tableData" stripe border style="width: 100%" class="table-container">
                        <!-- <el-table-column prop="name" label="用例名称" width="200" align="center"></el-table-column> -->
    <el-table-column v-if="tableColumns[0].show" prop="name" label="用例名称" min-width="300" align="left">
        <template #default="{ row }">
            <el-link type="primary" @click="onDetail(row)" :underline="false">{{ row.name }}</el-link>
        </template>
    </el-table-column>

    <el-table-column v-if="tableColumns[1].show" prop="module" label="所属模块" min-width="400" align="center">
        <template #default="{ row }">
            <span>{{ moduleMap[row.module] || row.module }}</span>
            <span v-if="row.module_2level"> / {{ moduleMap[row.module + '-' + row.module_2level] || row.module_2level }}</span>
            <span v-if="row.module_3level"> / {{ moduleMap[row.module + '-' + row.module_2level + '-' + row.module_3level] || row.module_3level }}</span>
        </template>
    </el-table-column>

    <el-table-column v-if="tableColumns[2].show" prop="source" label="用例来源" min-width="150" align="center">
        <template #default="{ row }">
            <span>{{ sourceMap[row.source] || row.source }}</span>
        </template>
    </el-table-column>

    <el-table-column v-if="tableColumns[3].show" prop="type" label="用例类型" min-width="150" align="center">
        <template #default="{ row }">
            <span>{{ typeMap[row.type] || row.type }}</span>
        </template>
    </el-table-column>

    <el-table-column v-if="tableColumns[4].show" label="用例活动类型" min-width="200" align="center">
        <template #default="{ row }">
            <span>{{ actionTypeMap[row.action_type] || row.action_type }}</span>
        </template>
    </el-table-column>

    <el-table-column v-if="tableColumns[5].show" label="执行方式" min-width="150" align="center">
        <template #default="{ row }">
            <el-tag v-if="row.execute_mode == 'AUTOMATED_EXECUTION'" type="success">自动化测试</el-tag>
            <el-tag v-else-if="row.execute_mode == 'MANUAL_EXECUTION'" type="success">手动测试</el-tag>
            <el-tag v-else-if="row.execute_mode == 'SEMI_AUTOMATED_EXECUTION'" type="success">半自动化测试</el-tag>
            <el-tag v-else type="danger">未知</el-tag>
        </template>
    </el-table-column>

    <el-table-column v-if="tableColumns[6].show" prop="creator_name" label="创建人" min-width="100" align="center"></el-table-column>

    <el-table-column v-if="tableColumns[7].show" label="操作" min-width="220" fixed="right" align="center">
        <template #default="{ row }">
            <div style="display: flex; justify-content: center; gap: 10px;">
                <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button type="primary" size="small" @click="handleCopy(row)">复制</el-button>
                <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
            </div>
        </template>
    </el-table-column>
</el-table>

                <div class="pagination-container">
                    <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]"
                        layout="prev, pager, next, jumper, total, sizes" :total="total" background
                        v-model:current-page="form.page" v-model:page-size="form.pagesize" @change="onPageChange" />
                </div>
            </el-main>

        </el-container>
    </el-container>


    <el-drawer v-model="drawerDetailVisible" :with-header="false" size="60%" :destroy-on-close="true">
        <TestCaseDetail :id="r_id" />
    </el-drawer>

</template>


<script setup>
import { ref, reactive, onMounted, onActivated } from 'vue';
import http from '@/utils/http/http.js';
import { useRouter } from 'vue-router';
import ModuleNav from './moduleNav.vue';
import TestCaseDetail from '@/components/test_case2.vue';
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/public_test_cases/list', '公共用例列表');

const tableColumns = ref([
  { key: "name", name: "用例名称", show: true, disabled: true },
  { key: "module", name: "所属模块", show: true, disabled: true },
  { key: "source", name: "用例来源", show: true },
  { key: "type", name: "用例类型", show: true },
  { key: "action_type", name: "用例活动类型", show: true },
  { key: "execute_mode", name: "执行方式", show: true },
  { key: "creator_name", name: "创建人", show: true },
  { key: "operation", name: "操作", show: true }
]);

// 设置弹窗的显示状态
const isSettingVisible = ref(false);

// 全选或取消全选逻辑
const selectAllColumn = (checked) => {
  tableColumns.value.forEach((column) => {
    if (!column.disabled) { // 跳过禁用的列
      column.show = checked;
    }
  });
};


// 重置列设置
const resetColumns = () => {
  tableColumns.value.forEach((column) => {
    if (!column.disabled) { // 跳过禁用的列
      column.show = true;
    }
  });
};
 
const router = useRouter();
const projects = ref([]);
const tableData = ref([]);
const activeTabName = ref('');
// add
const drawerDetailVisible = ref(false);
let r_id = ref(0);

let moduleMap = ref({});
let actionTypeMap = ref({});
let filterCount = ref(0);

let form = reactive({
    page: 1,
    pagesize: 15,
    name: '',
    number: '',
    project_number: '',
});

let typeMap = ref({
    "DURABLE_TEST": "耐久测试",
    "PERFORMANCE_TEST": "性能测试",
    "FUNCTION_TEST": "功能测试",
    "PROTOCOL_STACK_TEST": "协议栈测试"
});

let sourceMap = ref({
    "TASK_CHANGE": "用例库沿用",
    "TASK_AFFIRM": "需求分析",
    "NEW_PROJECT": "需求变更",
    "HORIZONTAL_SCALING": "横向扩展"
});

let total = ref(0);

let showFilterContainer = ref(false);

// add  
function onDetail(row) {
    r_id.value = row.id;
    drawerDetailVisible.value = true;
  console.log("row:",row);
};

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/test_cases/public', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'module_list', 'module_2level_list', 'module_3level_list', 'action_type'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        module_list: form.module_list,
        action_type: form.action_type,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleAdd() {
    router.push({ path: '/test_cases2/add', query: { type: 'p_add' } });
};

function handleEdit(row) {
    router.push({ path: '/test_cases2/add', query: { type: 'p_edit', id: row.id } });
};

function handleCopy(row) {
    router.push({ path: '/test_cases2/add', query: { type: 'p_copy', id: row.id } });
};

function handleDelete(row) {
    ElMessageBox.confirm(
        '确定删除吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.delete(`/test_cases/public/${row.id}`).then(res => {
            ElMessage({
                message: '删除成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            console.log(err);
            ElMessageBox.alert(err.response.data.msg, '警告', {
                confirmButtonText: 'OK',
                type: 'warning',
            })
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};

function handleTabClick(tab) {
    form.action_type = tab.paneName;
    update_table();
};

function onModuleCheck(module1, module2, module3) {
    form.module_list = module1;
    form.module_2level_list = module2;
    form.module_3level_list = module3;
    update_table();
};

function handleRefresh() {
    update_table();
};

onMounted(() => {
    update_table();

    http.get('/projects/p', { params: { pagesize: 10000 } }).then(res => {
        let data = res.data.data.results;
        projects.value = data;
    }).catch(err => {
        console.log(err);
    });

    http.get('/functions').then(res => {
        let items = res.data.data.results;

        items.forEach(item => {
            moduleMap.value[item.number] = item.name;
            if (item.children) {
                item.children.forEach(item2 => {
                    moduleMap.value[item.number + '-' + item2.number] = item2.name;
                    if (item2.children) {
                        item2.children.forEach(item3 => {
                            moduleMap.value[item.number + '-' + item2.number + '-' + item3.number] = item3.name;
                        });
                    }
                });
            }
        });
    });

    http.get('/test_m/test_case_types', { params: { pagesize: 10000 } }).then(res => {
        res.data.data.results.forEach(item => {
            actionTypeMap.value[item.number] = item.name;
        });

    });

});

onActivated(() => {
    update_table();
});

</script>


<style lang="scss" scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input,
    .el-select {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
        height: 35px;
    }

    :deep(.el-select__wrapper) {
        height: 100%;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;

    .el-select {
        width: 500px;
        margin-left: 10px;
    }

}

.el-aside {
    width: 200px;
    height: 100% !important;

    margin-top: 20px;
}

.el-main {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);

    margin-top: 0px;

    .table-container {
        flex: 1;
        overflow: auto;
    }
}

.column-popper-title {
  border-bottom: 1px solid #ebeef5;
}

/* 自定义滚动条样式 */
.column-content::-webkit-scrollbar {
  width: 6px; /* 滚动条宽度 */
}

.column-content::-webkit-scrollbar-track {
  background: #f1f1f1; /* 轨道背景色 */
  border-radius: 4px; /* 轨道圆角 */
}

/* 滚动条滑块默认样式（浅色） */
.column-content::-webkit-scrollbar-thumb {
  background: #e4e3e3; /* 浅色 */
  border-radius: 4px; /* 滑块圆角 */
}

/* 滚动条滑块悬停样式（深色） */
.column-content::-webkit-scrollbar-thumb:hover {
  background: #c7c7c7; /* 深色 */
}
</style>