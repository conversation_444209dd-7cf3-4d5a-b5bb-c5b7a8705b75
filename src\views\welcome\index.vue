<template>
    <div v-custom-loading="timelineLoading" style="width: 100%; height: 100%;">
        <div class="tool-bar-container">
            <div style="display: flex; gap: 10px;">
                <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                    <el-button text bg @click="handleReset">重置</el-button>
                </el-tooltip>
                <filterButton @click="onFilterStatusChange" :count="filterCount" />
            </div>

            <div class="right-p">
                <div class="c-marker">
                    <div class="c-marker-item">
                        <div class="c-marker-item-s" style="background-color: #CCCCCC;"></div> <span
                            class="c-marker-item-t">机台测试结束</span>
                    </div>
                    <div class="c-marker-item">
                        <div class="c-marker-item-s" style="background-color: #87CEFA;"></div> <span
                            class="c-marker-item-t">机台测试中</span>
                    </div>
                    <div class="c-marker-item">
                        <div class="c-marker-item-s" style="background-color: #34a650;"></div> <span
                            class="c-marker-item-t">机台已预约</span>
                    </div>
                </div>
            </div>

            <div style="display: flex; align-items: center;">
                <el-button-group style="margin-right: 20px;">
                    <el-button icon="ArrowLeftBold" @click="onLeft" />
                    <el-button icon="ArrowRightBold" @click="onRight" />
                </el-button-group>

                <el-radio-group v-model="windowType" @change="onWindowTypeChange">
                    <el-radio-button label="月" value="month" />
                    <el-radio-button label="周" value="week" />
                    <el-radio-button label="日" value="day" />
                </el-radio-group>
            </div>      
        </div>

        <div class="filter-container" v-if="showFilterContainer">
            <el-input v-model="form.machine_name_re" placeholder="请输入机台名称" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>

            <el-input v-model="form.machine_number_re" placeholder="请输入机台编号" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
        </div>
   
        <div class="timeline-container">
            <div ref="timelineRef" id="timeline" style="height: calc(100vh - 250px);"></div>
        </div>

        <el-pagination :page-sizes="[10, 20, 30, 40]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="machine_number" background
            @change="onPageChange" class="paginater" />
    </div>

</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import "vis-timeline/styles/vis-timeline-graph2d.min.css";
import { DataSet } from 'vis-data';
import { Timeline } from 'vis-timeline';
import moment from 'moment';
import "moment/dist/locale/zh-cn.js";
import http from '@/utils/http/http.js';

const timelineLoading = ref(true);
let showFilterContainer = ref(false);
let filterCount = ref(0);
let timeline = null;
let windowType = ref("week");

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

let timelineRef = ref(null);

let form = reactive({
    page: 1,
    pagesize: 30,
});

let machine_number = ref(0);

let options = {
    moment: function (date) {
        return moment(date).locale('zh-cn');
    },
    start: moment().startOf("isoWeek").format('YYYY-MM-DD'),
    end: moment().startOf("isoWeek").add(7, 'days').format('YYYY-MM-DD'),
    orientation: 'top',
    stack: true,
    showCurrentTime: false,
    groupHeightMode: 'fixed',
    height: '95%',
    width: '100%',
    verticalScroll: true,
    margin: {
        item: 0,
    },
    padding: {
        item: 0,
    },
    moveable: false,
    format: {
        minorLabels: {
            millisecond: 'SSS',
            second: 's',
            minute: 'HH:mm',
            hour: 'HH:mm',
            weekday: 'D ddd',
            day: 'D',
            week: 'w',
            month: 'MMM',
            year: 'YYYY'
        },
        majorLabels: {
            millisecond: 'HH:mm:ss',
            second: 'MMMM D HH:mm',
            minute: 'MMMM D ddd',
            hour: 'YYYY年 MMMM D ddd',
            weekday: 'YYYY年 MMMM',
            day: 'YYYY年 MMMM',
            week: 'YYYY年 MMMM',
            month: 'YYYY年',
            year: ''
        }
    }
};

let items = new DataSet();
let groups = new DataSet();

function bg_color(start, end) {
    start = moment(start);
    end = moment(end);
    let now = moment();
    if (end < now) {
        return "#CCCCCC";
    } else if (end >= now && start <= now) {
        return "#87CEFA";
    } else if (start > now) {
        return "#34a650";
    } else {
        return "grey";
    }
};

function updateTimeline() {
    http.get('/machines/reservations/timeline',
        {
            params: form,
        }
    ).then(res => {
        items.clear();
        groups.clear();

        machine_number.value = res.data.data.machines.count;

        for (let i in res.data.data.machines.results) {
            let machine = res.data.data.machines.results[i];
            groups.add({
                id: machine.id,
                // content: machine.name,
                content: machine.name + "【" + machine.m_number + "】",

            });
        };

        for (let i in res.data.data.machine_reservations.results) {
            let reservation = res.data.data.machine_reservations.results[i];
            items.add({
                stack: true,
                id: reservation.id,
                group: reservation.machine_id,
                start: reservation.start_time,
                end: reservation.end_time,
                title: reservation.user + " | " + reservation.project + " | " + reservation.content,
                style: "background-color: " + bg_color(reservation.start_time, reservation.end_time) + ";"
            });
        };
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'start_time', 'end_time'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
}

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 30,
        start_time: form.start_time,
        end_time: form.end_time,
    });
    updateTimeline();
};

function onPageChange() {
    updateTimeline();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    updateTimeline();
};

function onCurrentMonth() {
    let start = moment().startOf("month").format('YYYY-MM-DD');
    let end = moment().endOf("month").add(1, 'days').format('YYYY-MM-DD');
    timeline.setWindow(start, end);
    form.start_time = start;
    form.end_time = end;
    updateTimeline();
};

function onCurrentWeek() {
    let start = moment().startOf("isoWeek").format('YYYY-MM-DD');
    let end = moment().startOf("isoWeek").add(7, 'days').format('YYYY-MM-DD');
    timeline.setWindow(start, end);
    form.start_time = start;
    form.end_time = end;
    updateTimeline();
};

function onCurrentDay() {
    let start = moment().startOf("day").format('YYYY-MM-DD');
    let end = moment().endOf("day").add(1, 'days').format('YYYY-MM-DD');
    timeline.setWindow(start, end);
    form.start_time = start;
    form.end_time = end;
    updateTimeline();
};

function onWindowTypeChange() {
    switch (windowType.value) {
        case "month":
            onCurrentMonth();
            break;
        case "week":
            onCurrentWeek();
            break;
        case "day":
            onCurrentDay();
            break;
    }
};

function onLeft() {
    let start = moment(timeline.getWindow().start).subtract(1, windowType.value).format('YYYY-MM-DD');
    let end = moment(timeline.getWindow().end).subtract(1, windowType.value).format('YYYY-MM-DD');
    timeline.setWindow(start, end);
    form.start_time = start;
    form.end_time = end;
    updateTimeline();
};

function onRight() {
    let start = moment(timeline.getWindow().start).add(1, windowType.value).format('YYYY-MM-DD');
    let end = moment(timeline.getWindow().end).add(1, windowType.value).format('YYYY-MM-DD');
    timeline.setWindow(start, end);
    form.start_time = start;
    form.end_time = end;
    updateTimeline();
};

onMounted(() => {
    timeline = new Timeline(timelineRef.value, items, groups, options);

    timeline.on('rangechanged', function (properties) {
        if (properties.byUser) {
            form.start_time = moment(properties.start).format('YYYY-MM-DD');
            form.end_time = moment(properties.end).add(1, 'days').format('YYYY-MM-DD');
            updateTimeline();
        }
    });

    timeline.on('currentTimeTick', function () {
        items.forEach(element => {
            element.style = "background-color: " + bg_color(element.start_time, element.end_time) + ";"
        });
    });

    form.start_time = moment().startOf("isoWeek").format('YYYY-MM-DD');
    form.end_time = moment().startOf("isoWeek").add(7, 'days').format('YYYY-MM-DD');
    updateTimeline();

    setTimeout(() => {
        timelineLoading.value = false;
    }, 1000);
});

</script>


<style lang="scss" scoped>

:deep(.vis-current-time) {
    background-color: rgb(160, 158, 158);
}

:deep(.vis-item) {
    border-radius: 5px;

}

.paginater {
    margin-top: 20px;
}

.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: space-between;
}

:deep(.vis-item) {
    height: 100%;
}

.right-p {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-left: 10%;
}

.c-marker {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-right: 20px;

    .c-marker-item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-right: 15px;
        font-size: 14px;
        color: rgb(96, 98, 102);

        .c-marker-item-s {
            width: 40px;
            height: 20px;
            border-radius: 5px;
            margin-right: 3px;
        }
    }
}

:deep(.vis-label) {
    color: rgba(42, 42, 42, 0.7);
}

:deep(.vis-text) {
    color: rgba(42, 42, 42, 0.7);
}
</style>