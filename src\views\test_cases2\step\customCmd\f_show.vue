<template>
    <el-form>
        <el-form-item label="指令：">
            <span>{{ params.cmd }}</span>
        </el-form-item>
        <template v-if="currentCmd && currentCmd.params != undefined && currentCmd.params != null && currentCmd.params != ''">
            <el-form-item v-for="(param, index) in currentCmd.params" :label="param.param_name + ':'">
                <span>{{ params.params[index] }}</span>
            </el-form-item>
        </template>
    </el-form>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import http from '@/utils/http/http.js';

const props = defineProps(
    {
        params: {
            type: Object,
            required: true,
        },
    }
);

const custom_cmds = ref([]);
const currentCmd = computed(() => custom_cmds.value.find((item) => item.value === props.params.cmd));
const params = computed(() => props.params);

onMounted(() => {
    http.get('/custom_cmds', { params: { pagesize: 100000 } }).then(res => {
        custom_cmds.value = res.data.data.results.map(item => {
            return {
                label: item.cmd_name,
                value: item.cmd_code,
                params: item.params,
            };
        });
    });
});

</script>

<style scoped>
.el-form-item {
    margin-bottom: 0;
}
</style>