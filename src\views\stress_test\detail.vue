<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" status-icon ref="formRef">

            <el-form>
        <!-- 背景介绍 -->
        <el-divider>
            <span style="color: #606266; font-weight: bold;">背景介绍</span>
        </el-divider>
        <el-form-item label="问题描述:" style="font-weight: bold;">
            <span 
            style="margin-left: 28px ; font-weight: normal; color: #606266" 
            >{{form.verification.background.problem_description}}</span>
        </el-form-item>
        <el-form-item label="软件版本:" style="font-weight: bold;">
            <span
             style="margin-left: 28px ; font-weight: normal; color: #606266" 
             >{{form.verification.background.software_version}}</span>
        </el-form-item>
        <el-form-item label="硬件版本:" style="font-weight: bold;">
            <span
             style="margin-left: 28px ; font-weight: normal; color: #606266" 
             >{{form.verification.background.hardware_version}}</span>
        </el-form-item>
        <el-form-item label="PSN:" style="font-weight: bold;">
            <span 
            style="margin-left: 55px ; font-weight: normal; color: #606266" 
            >{{form.verification.background.psn}}</span>
        </el-form-item>
        <el-form-item label="问题来源:" style="font-weight: bold;">
            <span style="margin-left: 28px ; font-weight: normal; color: #606266" 
            >{{form.verification.background.problem_source}}</span>
        </el-form-item>

        <!-- 测试环境说明 -->
        <el-divider>
            <span style="color: #606266; font-weight: bold;">测试环境说明</span>
        </el-divider>
        <el-form-item label="样件数量:" style="font-weight: bold;">
            <span style="margin-left: 28px ; font-weight: normal; color: #606266" 
            >{{form.verification.test_environment.sample_count}}</span>
        </el-form-item>
        <el-form-item label="测试环境描述:" style="font-weight: bold;">
            <span style="font-weight: normal; color: #606266" 
             >{{form.verification.test_environment.description}}</span>
        </el-form-item>

        <!-- 验证方向 -->
        <el-divider>
            <span style="color: #606266; font-weight: bold;">验证方向</span>
            </el-divider>
        <!-- <el-form-item label="调研思维导图:" style="font-weight: bold;">
            <span  style="font-weight: normal; color: #606266" 
             >{{form.verification.validation_direction.mind_map}}</span>
        </el-form-item> -->
        <el-form-item label="鱼骨图:" style="font-weight: bold;">
            <span style="margin-left: 42px ; font-weight: normal; color: #606266" 
            >{{form.verification.validation_direction.fishbone_diagram}}</span>
        </el-form-item>

        <!-- 验证方案 -->
        <el-divider>
            <span style="color: #606266; font-weight: bold;">验证方案</span>
        </el-divider>
            <el-form-item label="飞书云文档:" style="font-weight: bold;">
                <span style="margin-left: 14px ; font-weight: normal; color: #606266" 
                >{{form.verification.validation_plan.feishu_doc}}</span>
                <div v-if="form.verification.validation_plan.feishu_doc">
                    <iframe
                        :src="form.verification.validation_plan.feishu_doc"
                        width="1600px"
                        height="1000px"
                        frameborder="0"
                        @load="onIframeLoad('plan')"
                        @error="onIframeError('plan')"
                    ></iframe>
                </div>
            </el-form-item>

            </el-form>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http/http.js';

const props = defineProps({
    r_id: {
        type: Number,
        required: true,
    },
});

const emit = defineEmits(['affirm', 'cancel']);

const formRef = ref(null);

// 初始化表单数据
const form = ref({
    verification: {
        background: {
            problem_description: '',
            software_version: '',
            hardware_version: '',
            psn: '',
            problem_source: '',
        },
        test_environment: {
            sample_count: '',
            description: '',
        },
        validation_direction: {
            mind_map: '',
            fishbone_diagram: '',
        },
        validation_plan: {
            feishu_doc: '',
        },
        validation_result: {
            feishu_doc: '',
        },
    },
});


// 取消操作
function onCancel() {
    emit('cancel');
};

// 加载数据
onMounted(() => {
    console.log('onMounted', props.r_id);
    if (props.r_id) {
        http.get(`/stress_tests/${props.r_id}`).then(res => {
            const data = res.data.data;
            // 解析 verification 字段
            if (data.verification) {
                form.value.verification = JSON.parse(data.verification);
            }
        });
    }
});

// 飞书云文档输入处理
const onFeishuDocInput = (type) => {
    // 可以在这里添加对链接的验证逻辑
    if (type === 'plan' && form.value.verification.validation_plan.feishu_doc) {
        validateFeishuDocLink(form.value.verification.validation_plan.feishu_doc);
    } else if (type === 'result' && form.value.verification.validation_result.feishu_doc) {
        validateFeishuDocLink(form.value.verification.validation_result.feishu_doc);
    }
};

// 验证飞书云文档链接
const validateFeishuDocLink = (link) => {
    // 这里可以添加链接验证逻辑，例如检查是否包含特定域名
    if (!link.includes('feishu.cn')) {
        ElMessage.error('请输入有效的飞书云文档链接');
        // 清空无效链接
        if (link === 'plan') {
            form.value.verification.validation_plan.feishu_doc = '';
        } else if (link === 'result') {
            form.value.verification.validation_result.feishu_doc = '';
        }
    }
};

// iframe 加载成功
const onIframeLoad = (type) => {
    ElMessage.success(`${'验证方案'} 飞书云文档加载成功`);
};

// iframe 加载失败
const onIframeError = (type) => {
    ElMessage.error(`${'验证方案'} 飞书云文档加载失败，请检查链接是否有效`);
};
</script>

<style lang="scss" scoped>
.submit-button-container {
    display: flex;
    justify-content: flex-end;
}

.add-container {
    padding: 0 20px;
    max-height: 800px;  // 设置最大高度
    overflow-y: auto;   // 垂直滚动条
    overflow-x: hidden; // 隐藏水平滚动条
}

// 如果需要表单区域整体滚动，还可以给表单外层容器添加：
.el-form {
    height: 100%;
}

</style>
