<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="设备编号">
                <el-input v-model="form.d_number" readonly></el-input>
            </el-form-item>

            <el-form-item label="名称" prop="name">
                <el-input v-model="form.name"></el-input>
            </el-form-item>

            <el-form-item label="类型" prop="type">
                <el-select v-model="form.type">
                    <el-scrollbar max-height="200px">
                        <el-option v-for="item in type_options" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-scrollbar>
                </el-select>
            </el-form-item>

            <el-form-item label="资产编号">
                <el-input v-model="form.number"></el-input>
            </el-form-item>

            <el-form-item label="型号">
                <el-input v-model="form.model"></el-input>
            </el-form-item>

            <el-form-item label="描述">
                <el-input v-model="form.desc"></el-input>
            </el-form-item>

            <el-form-item label="维护人工号" prop="maintainer">
                <Organizaiton v-model="form.maintainer" ref="maintainerRef" :cache-data="cacheData" />
            </el-form-item>

            <el-form-item label="状态">
                <el-select v-model="form.status">
                    <el-option label="空闲中" value="0"></el-option>
                    <el-option label="使用中" value="1"></el-option>
                    <el-option label="报修中" value="2"></el-option>
                    <el-option label="维修中" value="3"></el-option>
                    <el-option label="保养中" value="4"></el-option>
                    <el-option label="报废" value="5"></el-option>
                </el-select>
            </el-form-item>


            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

import http from '@/utils/http/http.js';

import Organizaiton from '@/components/Organization/index.vue';

const props = defineProps({
    r_id: {
        type: Number,
        required: true,
    },
});

const formRef = ref(null);

const maintainerRef = ref(null);

const cacheData = ref([]);

let type_options = ref([]);

const form = ref({
    name: '',
    number: '',
    desc: '',
    status: '0',
    d_number: '',
});

const rules = ref({
    name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
    ],
    type: [
        { required: true, message: '请输入类型', trigger: 'blur' },
    ],
    maintainer: [
        { required: true, message: '请输入维护人工号', trigger: 'blur' },
    ],
});

const emit = defineEmits(['submit', 'cancel'])

const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            let data = {
                ...form.value,
            };

            data.maintainer = maintainerRef.value.getNode(form.value.maintainer).data.employeeNo;

            http.put(`/machines/devices/${props.r_id}`, data).then(res => {
                emit('submit');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '编辑失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onCancel = () => {
    emit('cancel');
};

onMounted(() => {

    http.get('/machines/device_types', { params: { page: 1, pagesize: 1000 } }).then(res => {
        type_options.value = res.data.data.results.map(item => {
            return {
                label: item.name,
                value: item.name,
            };
        });
    });

    if (props.r_id) {
        http.get(`/machines/devices/${props.r_id}`).then(res => {
            form.value = res.data.data;
            form.value.status = form.value.status.toString();

            form.value.maintainer = res.data.data.email;

            cacheData.value = [{
                employeeNo: res.data.data.maintainer,
                label: res.data.data.username,
                value: res.data.data.email,
            }]; 
        });
    };
});

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>