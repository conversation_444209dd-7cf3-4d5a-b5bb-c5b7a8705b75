<template>

    <div style="display: flex; flex-direction: column; height: calc(101.7vh - 250px);">
        <div class="tool-bar-container">

            <div style="margin-left: auto; display: flex; gap: 10px;">
                <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                    <el-button text bg @click="handleReset">重置</el-button>
                </el-tooltip>
                <filterButton @click="onFilterStatusChange" :count="filterCount" />
                <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
            </div>
        </div>

        <div class="filter-container" v-if="showFilterContainer">
            <el-input v-model="form.test_plan_name" placeholder="请输入计划名称" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
            <el-input v-model="form.tester_name" placeholder="请输入测试人" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
            <el-input v-model="form.machine_name" placeholder="请输入测试机台" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
            <el-input v-model="form.test_plan_id" placeholder="请输入测试计划ID" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
        </div>

        <el-table :data="tableData" stripe border style="width: 100%;flex: 1;" class="table-container">
            <el-table-column prop="project_name" label="项目" min-width="200" align="center"></el-table-column>
            <el-table-column prop="test_plan_name" label="计划名称" min-width="200" align="center"></el-table-column>

            <el-table-column label="测试机台" min-width="300" align="center">
                <template #default="{ row }">
                    <span>{{ row.machine_name + "(" + row.machine_number + ")" }}</span>
                </template>
            </el-table-column>

            <el-table-column prop="tester_name" label="测试人" min-width="100" align="center"></el-table-column>
            <el-table-column prop="create_time" label="开始时间" min-width="180" align="center"></el-table-column>
            <el-table-column prop="update_time" label="结束时间" min-width="180" align="center"></el-table-column>

            <el-table-column label="操作" min-width="100" fixed="right" align="center">
                <template #default="{ row }">
                    <div style="display: flex; justify-content: center; gap: 10px;">
                        <el-button type="primary" size="small" @click="handledetail(row)">查看记录</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <div class="pagination-container">
            <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
                v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
                @change="onPageChange" />
        </div>
    </div>

</template>


<script setup>
import { ref, reactive, watch, onActivated, onMounted, inject} from 'vue';
import http from '@/utils/http/http.js';
import { useRoute, useRouter } from 'vue-router';
import { useProjectStore } from '@/stores/project.js';
import filterButton from '@/components/filterButton.vue';

const router = useRouter();
const route = useRoute();
let projectStore = useProjectStore();
const tableData = ref([]);
const filterCount = ref(0);

let form = reactive({
    project_number: '',
    pagesize: 15,
});

let total = ref(0);

const testPlanId = inject('testPlanId')

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/v2/test_records', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'project_number'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function onPageChange() {
    update_table();
};

function handleReset() {
    const projectNumber = form.project_number;
    Object.keys(form).forEach(key => {
        delete form[key];
    });
    form.page = 1;
    form.pagesize = 10;
    form.project_number = projectNumber;
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handledetail(row) {
    router.push({ path: "/test_records_v2/items/", query: { id: row.id, f: Date.now()} });
};

function handleRefresh() {
    update_table();
};

watch(() => projectStore.project_info, () => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

watch(() => testPlanId.value, () => {
    form.test_plan_id = testPlanId.value;
    update_table();
});

onActivated(() => {
    if (testPlanId.value) {
        form.test_plan_id = testPlanId.value;
    }
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

onMounted(() => {
    if (testPlanId.value) {
        form.test_plan_id = testPlanId.value;
    }
    form.project_number = projectStore.project_info.projectCode;
    update_table();
})

</script>


<style lang="scss" scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;

    .el-select {
        width: 500px;
    }

}
</style>