<template>
  <!-- 提示弹框 -->
  <TipDialog v-if="showTipDialog" />
  <div v-else v-custom-loading="loading">
        <!-- 顶部工具栏组件 -->
        <Toolbar
        :sdk-version="SdkVersion"
        :form="form"
        :space-options="spaceOptions"
        :branch-options="branchOptions"
        :loading="loading"
        :has-edit-permission="hasEditPermission"
        @update:form="(newForm) => Object.assign(form, newForm)"
        @gitlab-click="handleGitlabClick"
        @download="handleDownload"
        @save="handleSave"
        @publish="handlePublish"
        @merge-test="mergetest"
        @confirm-action="confirmAction"
        />
       <div class="layout-container">
        <!-- 左侧容器 -->

            <div class="left-container">

                <!-- 第一行：芯片配置和芯片预览 -->
                <div class="left-top-row">
                    <!-- 芯片配置 -->
                    <div class="chip-config-section" ref="leftColumnRef">
                    <div class="card">
                        <h2 class="card-title">
                        <el-icon  style="color: #ccc; font-size: 14px; margin-right: 5px;"><Setting /></el-icon>
                        芯片配置 </h2>
                        <el-form :model="chipForm" label-position="left" label-width="120px" class="form-left-align">
                        <el-form-item label="芯片型号">
                            <el-select v-model="chipForm.chipModel" placeholder="chipForm.chipModel">
                            <el-option :key="chipForm.chipModel" :label="chipForm.chipModel" :value="chipForm.chipModel" ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="VCC 电压(V)">
                            <el-input-number v-model="chipForm.vccVoltage" :min="0" :max="5" :step="0.1" :precision="1"></el-input-number>
                        </el-form-item>
                        <el-form-item label="时钟频率(MHz)">
                            <el-input-number v-model="chipForm.clockFreq" :min="0" :max="200" :step="1"></el-input-number>
                        </el-form-item>
                        </el-form>
                    </div>
                    </div>

                
                
                </div>

            </div>

     
        </div>
    </div>

  
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue';
import http from '@/utils/http/http';


// 引入缓存
import { useProjectStore } from '@/stores/project.js';
import TipDialog from '@/views/code_management/None.vue';
import Toolbar from '@/views/code_management/Toolbar.vue'


// 控制空数据页面显示
const showTipDialog = ref(true);
// 定义顶部工具栏变量
const SdkVersion = ref('');
const form = reactive({
  gitlab: '',
  project_branch: ''
});

// 使用缓存
const projectStore = useProjectStore();

const spaceOptions = ref([]);
const branchOptions = ref([]);
const branch_create = ref(true);

// 初始化时从全局状态恢复数据
const initializeFromStore = () => {
  const codeManagement = projectStore.codeManagement;
  form.gitlab = codeManagement.gitlab || '';
  form.project_branch = codeManagement.project_branch || '';
  spaceOptions.value = codeManagement.spaceOptions || [];
  branchOptions.value = codeManagement.branchOptions || [];
  SdkVersion.value = codeManagement.sdkVersion || '';
};
// 加载效果
const loading = ref(false);

// 权限控制 - 基于branch_create状态
const hasEditPermission = computed(() => branch_create.value);

// 单击仓库
const handleGitlabClick = (visible) => {
  // 判断有无存在的ElMessage， 存在则关闭
  if (!project_code) {
    messageManager.warning('请选择项目！')
  }
}





// 组件挂载时初始化数据
onMounted(() => {
  try {
    // 从全局状态初始化数据
    initializeFromStore();

    // 监控项目信息变化
    const info = projectStore.project_info || {};
    project_code = info.projectCode || '';
    project_name = info.name || '';

    console.log('project_code:', project_code);
    console.log('project_name:', project_name);
    console.log('从全局状态恢复的仓库信息:', form.gitlab);
    console.log('从全局状态恢复的分支信息:', form.project_branch);

    if (project_code=="" && project_name=="") {
      console.info('项目信息为空，请先选择项目');
      showTipDialog.value = true;
    } else {
      console.info('项目信息已选择，开始初始化');
      showTipDialog.value = false;
      // 如果没有仓库信息，则获取仓库列表
      if (!form.gitlab || spaceOptions.value.length === 0) {
        get_space();
      } else {
        // 如果有仓库信息但没有分支信息，则获取分支列表
        if (!form.project_branch || branchOptions.value.length === 0) {
          get_branch();
        } else {
          // 如果都有，直接提交分支信息
          submit_branch_info();
        }
      }
    }


    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);

  } catch (error) {
    console.error('组件初始化错误:', error);
    messageManager.error('组件初始化失败，请刷新页面重试');
  }
});


// 监控项目信息变化
watch(() => projectStore.project_info, (newval, oldval) => {
  if (newval) {
    const newProjectCode = newval.projectCode || '';
    const newProjectName = newval.name || '';

    // 只有当项目代码真正发生变化时才重新获取仓库信息
    if (newProjectCode !== project_code) {
      console.log('项目切换:', project_code, '->', newProjectCode);

      project_code = newProjectCode;
      project_name = newProjectName;

      // 检查项目是否选择
      if (project_code=="" && project_name=="") {
        showTipDialog.value = true;
      } else {
        showTipDialog.value = false;
        // 清空全局状态和本地状态
        projectStore.clearCodeManagement();
        form.gitlab = '';
        form.project_branch = '';
        spaceOptions.value = [];
        branchOptions.value = [];
        get_space();
      }
    }
  }
});

// 监控仓库信息变化
watch(() => form.gitlab, ( newval, oldval) => {
  if (newval !== oldval) {
    // 同步更新全局状态
    projectStore.updateCodeManagement({ gitlab: newval });
    form.project_branch = '';
    get_branch();
  }
});

// 监控分支信息变化
watch(() => form.project_branch, ( newval, oldval) => {
  if (newval !== oldval) {
    // 同步更新全局状态
    projectStore.updateCodeManagement({ project_branch: newval });
    submit_branch_info();
  }
});



// 功能按钮处理函数
// download 操作
const handleDownload = () => {
  console.log('开始下载配置');
  messageManager.success('配置下载成功');
};

// commit 操作
const handleSave = async () => {
  ElMessageBox.prompt('请输入 commit 信息', '保存配置', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    inputErrorMessage: '请输入 commit 信息',
    inputValidator: (value) => value != null && value.trim() !== ''
  }).then(({ value }) => {
    console.log('Commit信息:', value);
    messageManager.success('配置已保存');
  }).catch(() => {
    console.log('取消保存');
  });
};

// push 操作
const handlePublish = () => {
  ElMessageBox.confirm(
    '确定要发布当前配置吗？发布后将生效并覆盖现有配置。',
    '发布确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    console.log('确认发布');
    messageManager.success('配置已发布');
  }).catch(() => {
    console.log('取消发布');
  });
};

// merge 确认操作
const confirmAction = (mergeBranch) => {
  ElMessageBox.confirm(
    '确定要merge当前分支到目标分支吗？',
    '发布确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    console.log('确认合并到:', mergeBranch);
    messageManager.success('分支合并成功');
  }).catch(() => {
    console.log('取消合并');
  });
};

</script>