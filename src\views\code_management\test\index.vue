<template>
  <!-- 提示弹框 -->
  <TipDialog v-if="showTipDialog" />
  <div v-else v-custom-loading="loading">
        <!-- 顶部工具栏组件 -->
        <Toolbar
        @data-loaded="handleDataLoaded"
        @project-changed="handleProjectChanged"
        />
       <div class="layout-container">
        <!-- 左侧容器 -->

            <div class="left-container">

                <!-- 第一行：芯片配置和芯片预览 -->
                <div class="left-top-row">
                    <!-- 芯片配置 -->
                    <div class="chip-config-section" ref="leftColumnRef">
                    <div class="card">
                        <h2 class="card-title">
                        <el-icon  style="color: #ccc; font-size: 14px; margin-right: 5px;"><Setting /></el-icon>
                        芯片配置 </h2>
                        <el-form :model="chipForm" label-position="left" label-width="120px" class="form-left-align">
                        <el-form-item label="芯片型号">
                            <el-select v-model="chipForm.chipModel" placeholder="请选择芯片型号">
                            <el-option :key="chipForm.chipModel" :label="chipForm.chipModel" :value="chipForm.chipModel" ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="VCC 电压(V)">
                            <el-input-number v-model="chipForm.vccVoltage" :min="0" :max="5" :step="0.1" :precision="1"></el-input-number>
                        </el-form-item>
                        <el-form-item label="时钟频率(MHz)">
                            <el-input-number v-model="chipForm.clockFreq" :min="0" :max="200" :step="1"></el-input-number>
                        </el-form-item>
                        </el-form>
                    </div>
                    </div>

                
                
                </div>

            </div>

     
        </div>
    </div>

  
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { Setting } from '@element-plus/icons-vue';
import messageManager from '@/utils/messageManager';

import TipDialog from '@/views/code_management/test/None.vue';
import Toolbar from '@/views/code_management/test/Toolbar.vue'


// 控制空数据页面显示
const showTipDialog = ref(true);

// 芯片配置表单数据
const chipForm = reactive({
  chipModel: 'KF32A158',
  vccVoltage: 3.3,
  clockFreq: 160
});

// 模板引用
const leftColumnRef = ref(null);

// 加载效果
const loading = ref(false);

// 处理 Toolbar 数据加载完成事件
const handleDataLoaded = (data) => {
  console.log('主组件接收到工具栏数据:', data);
  showTipDialog.value = false;
  // 可以在这里处理其他需要在数据加载完成后执行的逻辑
};

// 处理项目变更事件
const handleProjectChanged = (projectInfo) => {
  console.log('主组件接收到项目变更:', projectInfo);
  // 可以在这里处理项目变更后的逻辑
};

// 窗口大小变化处理
const handleResize = () => {
  // 处理窗口大小变化逻辑
  console.log('窗口大小发生变化');
};





// 组件挂载时初始化数据
onMounted(() => {
  try {
    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);
  } catch (error) {
    console.error('组件初始化错误:', error);
    messageManager.error('组件初始化失败，请刷新页面重试');
  }
});

// 组件卸载时清理
onUnmounted(() => {
  // 移除事件监听器
  window.removeEventListener('resize', handleResize);
});




</script>