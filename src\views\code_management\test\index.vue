<template>
  <!-- 提示弹框 -->
  <TipDialog v-if="showTipDialog" />
  <div v-else v-custom-loading="loading">
    <!-- 顶部工具栏组件 -->
    <Toolbar
      :sdk-version="SdkVersion"
      :form="form"
      :space-options="spaceOptions"
      :branch-options="branchOptions"
      :loading="loading"
      :has-edit-permission="hasEditPermission"
      @update:form="(newForm) => Object.assign(form, newForm)"
      @gitlab-click="handleGitlabClick"
      @download="handleDownload"
      @save="handleSave"
      @publish="handlePublish"
      @merge-test="mergetest"
      @confirm-action="confirmAction"
    />
      <div class="layout-container">
        <!-- 左侧容器 -->
        <div class="left-container">
          <!-- 第一行：芯片配置和芯片预览 -->
          <div class="left-top-row">
            <!-- 芯片配置 -->
            <div class="chip-config-section" ref="leftColumnRef">
              <div class="card">
                <h2 class="card-title">
                  <el-icon  style="color: #ccc; font-size: 14px; margin-right: 5px;"><Setting /></el-icon>
                  芯片配置 </h2>
                <el-form :model="chipForm" label-position="left" label-width="120px" class="form-left-align">
                  <el-form-item label="芯片型号">
                    <el-select v-model="chipForm.chipModel" placeholder="chipForm.chipModel">
                      <el-option :key="chipForm.chipModel" :label="chipForm.chipModel" :value="chipForm.chipModel" ></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="VCC 电压(V)">
                    <el-input-number v-model="chipForm.vccVoltage" :min="0" :max="5" :step="0.1" :precision="1"></el-input-number>
                  </el-form-item>
                  <el-form-item label="时钟频率(MHz)">
                    <el-input-number v-model="chipForm.clockFreq" :min="0" :max="200" :step="1"></el-input-number>
                  </el-form-item>
                </el-form>
              </div>
            </div>

            <!-- 芯片预览 -->
            <div class="chip-preview-section" ref="middleColumnRef">
              <div class="card">
                <h2 class="title">
                  <el-icon style="color: #ccc; font-size: 14px; margin-right: 5px;"><View /></el-icon>
                  芯片预览 ({{ getCurrentChipInfo().GpioNumber }}引脚)
                </h2>
                <!-- 传递引脚数据到 ChipPinDiagram 组件 -->
                <ChipPinDiagram
                  :pinCount="getCurrentChipInfo().GpioNumber"
                  :chipModel="chipForm.chipModel"
                  :pins="pinTableData"
                  :typeStyleConfig="typeStyleConfig"
                  :highlightedPin="currentPin"
                  @pinClick="highlightPin"
                  @pinEdit="handlePinEdit"
                />
              </div>
            </div>
          </div>

          <!-- 第二行：引脚配置表 -->
          <div class="left-bottom-row" ref="tableRowRef">
            <div class="card">
              <h2 class="card-title">
                <el-icon style="color: #ccc; font-size: 14px; margin-right: 5px;"><Operation /></el-icon>
                引脚配置表
              </h2>
              <div class="table-container">
                <div class="table-toolbar">
                  <div class="search-section">
                    <el-input v-model="searchKey" placeholder="搜索引脚编号/名称..."  class="search-input"></el-input>
                  </div>
                  <div class="io-type-section">
                    <el-form-item  class="io-type-form-item">
                      <el-checkbox-group v-model="chipForm.ioConfig">
                        <el-checkbox
                          v-for="ioType in availableIoTypes"
                          :key="ioType.value"
                          :value="ioType.value"
                          :title="ioType.description"
                        >
                          {{ ioType.label }}
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                  </div>
                </div>
                <el-table
                  :data="filteredPinTableData"
                  border
                  stripe
                  style="width: 100%;"
                  :header-cell-style="{ background: '#f5f7fa', color: '#606266', textAlign: 'center', fontSize: '16px', height: '50px' }"
                  :cell-style="{ textAlign: 'center', fontSize: '14px', height: '45px' }"
                  @row-click="handleRowClick"
                  highlight-current-row
                  size="small"
                  fit
                  stretch>
                  <el-table-column prop="pinId" label="引脚编号" sortable></el-table-column>
                  <el-table-column prop="pinName" label="引脚名称" sortable></el-table-column>
                  <el-table-column label="类型" min-width="150">
                    <template #default="scope">
                      <div class="type-tags">
                        <el-tag
                          v-if="scope.row.displayType"
                          v-bind="getTypeTagStyle(scope.row.displayType) ? { type: getTypeTagStyle(scope.row.displayType) } : {}"
                          size="small"
                          class="type-tag"
                        >
                          {{ scope.row.displayType }}
                        </el-tag>
                        <span v-else class="no-type-text">未配置</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态">
                    <template #default="scope">
                      <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作">
                    <template #default="scope">
                      <el-button type="primary" size="small" @click="handleEdit(scope.row)">
                        <i class="el-icon-edit"></i> 编辑
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage"
                  :page-sizes="[10, 20, 50, 100]"
                  :page-size="pageSize"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="filteredTotal"
                  class="pagination"
                  small
                ></el-pagination>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧容器：引脚编辑器 -->
        <div class="right-container" ref="rightColumnRef">
          <div class="pin-editor-panel" :class="{ 'collapsed': isPinEditorCollapsed }">
            <!-- <div class="panel-header" @click="togglePinEditor"> -->
              <!-- <h3 class="panel-title">
                <el-icon style="color: #ccc; font-size: 14px; margin-right: 5px;"><EditPen /></el-icon>
                引脚编辑器
              </h3> -->
              <!-- <el-icon class="collapse-icon" :class="{ 'rotated': isPinEditorCollapsed }">
                <ArrowDown />
              </el-icon> -->
            <!-- </div> -->
            <div class="panel-content" v-show="!isPinEditorCollapsed">
              <PinEditor
                ref="pinEditorRef"
                :pin-data="editPinForm"
                :key="editPinForm.pinId"
                :available-io-types="availableIoTypes"
                :repository-info="repositoryInfo"
                :pin-function-list="editPinForm.functionList || []"
                @save="handlePinSave"
                @reset="handlePinReset"
                @clear="handlePinClear"
                @change="handlePinChange"
              />
            </div>
          </div>
        </div>
      </div>
  </div>

  
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue';
import ChipPinDiagram from '@/views/code_management/gpio/ChipPinDiagram.vue';
import PinEditor from '@/views/code_management/gpio/DynamicPinEditor.vue';
import Toolbar from '@/views/code_management/Toolbar.vue';
import http from '@/utils/http/http';
import { useRoute, useRouter } from 'vue-router';
import { ElMessageBox } from 'element-plus';
import messageManager from '@/utils/messageManager';
import { useProjectStore } from '@/stores/project.js';
import { useUserStore } from '@/stores/user.js';
import TipDialog from '../None.vue';
import {
  View,
  Operation,
  Setting

} from '@element-plus/icons-vue';


import {chipConfigData} from  './chip.js'
const route = useRoute();
const router = useRouter();
const projectStore = useProjectStore();
const userStore = useUserStore();

// 项目相关变量
let project_code = '';
let project_name = '';

const form = reactive({
  gitlab: '',
  project_branch: ''
});
const spaceOptions = ref([]);
const branchOptions = ref([]);
const branch_create = ref(true);


// 初始化时从全局状态恢复数据
const initializeFromStore = () => {
  const codeManagement = projectStore.codeManagement;
  form.gitlab = codeManagement.gitlab || '';
  form.project_branch = codeManagement.project_branch || '';
  spaceOptions.value = codeManagement.spaceOptions || [];
  branchOptions.value = codeManagement.branchOptions || [];
  SdkVersion.value = codeManagement.sdkVersion || '';
};

const pick_tree_info = ref([]);
const workspace = ref('');
const branch_status = ref('');
const sdk_version = ref('');
const treeData = ref([]);
const SdkVersion = ref('');
const loading = ref(false);
const showTipDialog = ref(true);

// 权限控制 - 基于branch_create状态
const hasEditPermission = computed(() => branch_create.value);

// KF32A158芯片配置数据





// 单击仓库
const handleGitlabClick = (visible) => {
  // 判断有无存在的ElMessage， 存在则关闭
  if (!project_code) {
    messageManager.warning('请选择项目！')
  }
}





// 基于Bind_rule动态生成typeMap
const generateTypeMap = (bindRule) => {
  const typeMap = {};

  // 根据Bind_rule的键动态生成类型映射
  Object.keys(bindRule).forEach(key => {
    // 根据键名推断类型信息
    if (key === 'gpio') {
      typeMap[key] = { label: 'GPIO', description: '通用输入输出' };
    } else if (key === 'adc') {
      typeMap[key] = { label: 'ADC', description: '模拟数字转换' };
    } else if (key === 'pwm') {
      typeMap[key] = { label: 'PWM', description: '脉宽调制输出' };
    } else if (key.startsWith('iic_')) {
      typeMap[key] = { label: 'IIC', description: 'I2C总线通信' };
    } else if (key.startsWith('spi_')) {
      typeMap[key] = { label: 'SPI', description: 'SPI总线通信' };
    } else if (key.startsWith('uart_')) {
      typeMap[key] = { label: 'UART', description: '串口通信' };
    } else if (key === 'exit') {
      typeMap[key] = { label: 'EXIT', description: '外部中断输入' };
    } else {
      // 对于未知的键，尝试根据名称推断
      const upperKey = key.toUpperCase();
      typeMap[key] = { label: upperKey, description: `${upperKey}功能` };
    }
  });

  return typeMap;
};

// 从chipConfigData动态获取芯片型号
const getChipModel = () => {
  const chipKeys = Object.keys(chipConfigData);
  return chipKeys.length > 0 ? chipKeys[0] : 'Unknown';
};

// 获取当前芯片的配置信息
const getCurrentChipInfo = () => {
  const chipModel = getChipModel();
  return chipConfigData[chipModel]?.Chip_Info || {};
};

// 基于chipConfigData动态生成IO类型定义
const generateIoTypeDefinitions = () => {
  const chipInfo = getCurrentChipInfo();
  const bindRule = chipInfo.Bind_rule || {};
  const typeMap = generateTypeMap(bindRule);

  // 收集所有类型和对应的PIN_MODE
  const typeGroups = {};
  Object.entries(bindRule).forEach(([key, pinMode]) => {
    const typeInfo = typeMap[key];
    if (typeInfo) {
      if (!typeGroups[typeInfo.label]) {
        typeGroups[typeInfo.label] = {
          value: typeInfo.label,
          label: typeInfo.label,
          description: typeInfo.description,
          pinModes: []
        };
      }
      typeGroups[typeInfo.label].pinModes.push(pinMode);
    }
  });

  return Object.values(typeGroups);
};

const ioTypeDefinitions = generateIoTypeDefinitions();

// 基于动态生成的typeMap和Bind_rule生成统一的类型样式配置
const generateTypeStyleConfig = (bindRule) => {
  const typeMap = generateTypeMap(bindRule);
  const baseConfig = {};

  // 基于typeMap动态生成样式配置
  Object.values(typeMap).forEach(typeInfo => {
    const label = typeInfo.label;

    // 根据类型标签设置样式
    switch (label) {
      //  Power: '#F28B82',
      //   GPIO: '#81C784',
      //   UART: '#FFB74D',
      //   SPI: '#BAA0FF',
      //   IIC: '#80D8FF',
      //   ADC: '#FF8A65',
      //   PWM: '#B388FF',
      //   EXIT: '#80DEEA',
      //   CAN: '#A1887F',
      //   DEFAULT: '#B0BEC5',
      case 'GPIO':
        baseConfig[label] = { color: '#A5D6A7', tagStyle: 'success', description: typeInfo.description };
        break;
      case 'ADC':
        baseConfig[label] = { color: '#FFAB91', tagStyle: 'warning', description: typeInfo.description };
        break;
      case 'PWM':
        baseConfig[label] = { color: '#B39DDB', tagStyle: '', description: typeInfo.description };
        break;
      case 'IIC':
        baseConfig[label] = { color: '#90CAF9', tagStyle: 'info', description: typeInfo.description };
        break;
      case 'SPI':
        baseConfig[label] = { color: '#CE93D8', tagStyle: '', description: typeInfo.description };
        break;
      case 'UART':
        baseConfig[label] = { color: '#FFCC80', tagStyle: 'warning', description: typeInfo.description };
        break;
      case 'EXIT':
        baseConfig[label] = { color: '#FFE08299', tagStyle: 'warning', description: typeInfo.description };
        break;
      default:
        // 对于未知类型，使用默认样式
        baseConfig[label] = { color: '#CFD8DC', tagStyle: '', description: typeInfo.description };
        break;
    }
  });

  // 添加特殊类型配置
  baseConfig.Power = { color: '#EF9A9A', tagStyle: 'danger', description: '电源/地线' };
  baseConfig.CAN = { color: '#BCAAA4', tagStyle: 'info', description: 'CAN总线通信' };
  baseConfig.DEFAULT = { color: '#CFD8DC', tagStyle: '', description: '默认' };

  return baseConfig;
};

const typeStyleConfig = generateTypeStyleConfig(getCurrentChipInfo().Bind_rule || {});

// 动态获取可用的IO类型列表
const availableIoTypes = computed(() => {
  return ioTypeDefinitions.map(type => ({
    value: type.value,
    label: type.label,
    description: type.description
  }));
});

// 根据实际引脚数据获取使用中的IO类型
const usedIoTypes = computed(() => {
  if (!pinTableData.value.length) return [];

  const typesUsed = new Set(pinTableData.value.map(pin => pin.pinType));
  return ioTypeDefinitions.filter(type => typesUsed.has(type.value));
});

// 获取IO类型的统计信息
const ioTypeStats = computed(() => {
  if (!pinTableData.value.length) return {};

  const stats = {};
  ioTypeDefinitions.forEach(type => {
    stats[type.value] = {
      count: pinTableData.value.filter(pin => pin.pinType === type.value).length,
      enabled: pinTableData.value.filter(pin => pin.pinType === type.value && pin.status === '已配置').length
    };
  });
  return stats;
});

// 芯片配置表单数据 - 从chipConfigData动态获取
const chipForm = reactive({
  workspace: 'default', // 工作空间
  branchStatus: 'main', // 分支状态
  chipModel: getChipModel(), // 动态获取芯片型号
  vccVoltage: 3.3,
  clockFreq: 160,
  ioConfig: [] // 初始为空数组，显示所有引脚类型
});

// 仓库信息计算属性，用于传递给PinEditor组件
const repositoryInfo = computed(() => ({
  gitlab: form.gitlab,
  project_branch: form.project_branch,
  workspace: workspace.value,
  branch_status: branch_status.value
}));

// 编辑引脚配置表单数据
const editPinForm = ref({
  pinId: '',
  pinName: '',
  pinType: '',
  desc: '',
  status: '可用',
  electricalType: 'TTL',
  dynamicConfig: {} // 动态配置字段
});

// 布局相关的响应式变量
const leftColumnRef = ref(null);
const middleColumnRef = ref(null);
const rightColumnRef = ref(null);
const pinEditorRef = ref(null);
const tableRowRef = ref(null);

// 引脚编辑器折叠状态
const isPinEditorCollapsed = ref(false);

// 切换引脚编辑器折叠状态
const togglePinEditor = () => {
  isPinEditorCollapsed.value = !isPinEditorCollapsed.value;
};



// 判断是否为只读模式（当类型为OTHER时）
const isReadOnlyMode = computed(() => {
  return editPinForm.value.pinType === 'Power' || editPinForm.value.pinType === 'OTHER';
});

// 引脚配置表数据
const pinTableData = ref([]);
const searchKey = ref('');
const currentPage = ref(1);
const pageSize = ref(10);

// 当前选中的引脚编号
const currentPin = ref(null);



// 根据引脚编号获取类型（与表格数据同步）
const getPinType = (pinId) => {
  if (!pinId) return '无';
  
  const pin = pinTableData.value.find(item => item.pinId === pinId.toString());
  return pin ? pin.pinType : '未知';
};

// 通用筛选函数
const getFilteredData = () => {
  const key = searchKey.value.trim().toLowerCase();

  return pinTableData.value.filter(item => {
    // 搜索关键字筛选：检查引脚编号和名称
    const searchMatch = !key ||
      item.pinId.toLowerCase().includes(key) ||
      item.pinName.toLowerCase().includes(key);

    // IO类型筛选：检查引脚的displayType是否匹配选中的类型
    const typeMatch = chipForm.ioConfig.length === 0 ||
      chipForm.ioConfig.some(selectedType => {
        if (selectedType === 'OTHER') {
          // OTHER类型匹配未配置的引脚（displayType为null或undefined）
          return !item.displayType;
        } else {
          // 其他类型正常匹配displayType
          return item.displayType === selectedType;
        }
      });

    return searchMatch && typeMatch;
  });
};

// 引脚点击高亮事件
const highlightPin = (pinId) => {
  currentPin.value = pinId;

  // 在筛选后的数据中查找引脚位置
  const filteredData = getFilteredData();
  const pinIndex = filteredData.findIndex(item => item.pinId === pinId.toString());

  if (pinIndex !== -1) {
    // 基于筛选后数据的位置计算页码
    currentPage.value = Math.floor(pinIndex / pageSize.value) + 1;
  }
  // 如果引脚不在当前筛选结果中，不改变页码，只设置高亮
};

// 状态样式映射
const getStatusType = (status) => {
  return status === '已配置' ? 'success' :
         status === '保留' ? 'warning' :
         status === '故障' ? 'danger' : 'info';
};

// 类型标签样式映射 - 基于typeStyleConfig
const getTypeTagStyle = (type) => {
  const tagStyle = typeStyleConfig[type]?.tagStyle || typeStyleConfig.DEFAULT?.tagStyle;
  // 如果tagStyle为空字符串，返回undefined，这样el-tag就不会设置type属性
  return tagStyle === '' ? undefined : tagStyle;
};

// 获取类型颜色 - 基于typeStyleConfig
const getTypeColor = (type) => {
  return typeStyleConfig[type]?.color || typeStyleConfig.DEFAULT.color;
};

// PIN_MODE到简化类型的映射 - 基于动态生成的ioTypeDefinitions
const pinModeToType = (pinMode) => {
  if (!pinMode) return 'GPIO';

  // 在ioTypeDefinitions中查找匹配的类型
  for (const typeDef of ioTypeDefinitions) {
    if (typeDef.pinModes.includes(pinMode)) {
      return typeDef.value;
    }
  }

  // 特殊处理一些常见情况
  if (pinMode.includes('MCAN')) return 'CAN';
  if (pinMode.includes('OTHERS')) return 'Power';

  return 'GPIO'; // 默认返回GPIO
};

// 初始化表格数据 - 从chipConfigData动态获取
const initTableData = () => {
  try {
    const chipInfo = getCurrentChipInfo();
    const pinConfigs = chipInfo.Pin_Config || [];

  const pins = pinConfigs.map(pinConfig => {
    // 处理function_list字符串，转换为数组
    const functionList = typeof pinConfig.function_list === 'string'
      ? pinConfig.function_list.split(',').map(item => item.trim())
      : pinConfig.function_list || [];

    // 确定引脚类型 - 严格使用default字段
    let defaultMode = pinConfig.default;
    let pinType = null; // 默认为null，表示不显示类型标签
    let displayType = null; // 用于表格显示的类型

    if (defaultMode && defaultMode !== null && defaultMode !== "None" && defaultMode.trim() !== "") {
      // 只有当default字段有有效值时，才确定并显示类型
      pinType = pinModeToType(defaultMode);
      displayType = pinType;
    }

    // 计算所有支持的类型 - 基于function_list
    const supportedTypes = functionList.map(pinMode => pinModeToType(pinMode))
      .filter((type, index, array) => array.indexOf(type) === index) // 去重
      .filter(type => type && type.trim() !== ''); // 过滤掉空值

    // 确定状态 - 基于enable字段
    const status = pinConfig.enable ? '已配置' : '未配置';

    // 确定电气类型
    const electricalType = pinType === 'Power' ? 'Power' : 'TTL';

    return {
      pinId: pinConfig.id.toString(),
      pinName: pinConfig.name, // 使用Pin_Config中的name
      pinType: pinType, // 主要类型（用于筛选）
      displayType: displayType, // 用于表格显示的类型（基于default字段）
      supportedTypes: supportedTypes, // 所有支持的类型（用于编辑器选项）
      status: status, // enable: true为已配置，false为未配置
      electricalType: electricalType,
      functionList: functionList, // 原始PIN_MODE列表
      // 保存原始配置数据以便后续使用
      originalConfig: pinConfig
    };
  });

  pinTableData.value = pins;
  } catch (error) {
    console.error('初始化表格数据错误:', error);
    messageManager.error('初始化引脚数据失败');
    pinTableData.value = [];
  }
};

// 表格分页方法
const handleSizeChange = (val) => {
  pageSize.value = val;
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
};

// 行点击事件
const handleRowClick = (row) => {
  handleEdit(row);
  // handleEdit已经设置了currentPin，不需要再调用highlightPin
};

// 编辑按钮点击事件
const handleEdit = (row) => {
  // 清除历史标红，设置当前引脚高亮
  currentPin.value = row.pinId;

  // 直接替换整个对象来触发响应式更新
  editPinForm.value = { ...row, dynamicConfig: {} };
};

// 处理芯片图引脚点击编辑事件
const handlePinEdit = (pinData) => {
  // 清除历史标红，设置当前引脚高亮
  currentPin.value = pinData.pinId;

  // 直接替换整个对象来触发响应式更新
  editPinForm.value = { ...pinData, dynamicConfig: {} };

  // 滚动到表格中对应的引脚行
  const filteredData = getFilteredData();
  const pinIndex = filteredData.findIndex(item => item.pinId === pinData.pinId.toString());

  if (pinIndex !== -1) {
    // 基于筛选后数据的位置计算页码
    currentPage.value = Math.floor(pinIndex / pageSize.value) + 1;
  }
};

// 计算属性：根据搜索关键字和IO配置过滤表格数据
const filteredPinTableData = computed(() => {
  const filteredData = getFilteredData();

  return filteredData.slice(
    (currentPage.value - 1) * pageSize.value,
    currentPage.value * pageSize.value
  );
});

// 计算属性：过滤后的总记录数
const filteredTotal = computed(() => {
  return getFilteredData().length;
});

// PinEditor组件事件处理
const handlePinSave = (pinData) => {
  // 更新表格数据
  const index = pinTableData.value.findIndex(item => item.pinId === pinData.pinId);
  if (index !== -1) {
    Object.assign(pinTableData.value[index], pinData);
    messageManager.success('引脚配置已保存');
  }
};

const handlePinReset = (pinData) => {
  // 从原始数据重置
  const originalPin = pinTableData.value.find(item => item.pinId === pinData.pinId);
  if (originalPin) {
    Object.assign(editPinForm, originalPin);
  }
};

const handlePinClear = (pinData) => {
  // 清空配置但保留引脚ID
  Object.assign(editPinForm, pinData);
};

const handlePinChange = (pinData) => {
  // 只更新必要的字段，避免触发子组件重新初始化
  // 这里不需要更新editPinForm，因为子组件已经管理自己的状态
};

// 重置表单
const resetPinConfig = () => {
  if (!editPinForm.pinId) return;

  const row = pinTableData.value.find(item => item.pinId === editPinForm.pinId);
  if (row) {
    // 保留引脚ID，重置其他字段
    const pinId = editPinForm.pinId;
    Object.assign(editPinForm, row);
    editPinForm.pinId = pinId;
  }
};

// 刷新表格
const refreshTable = () => {
  initTableData();
  alert('表格数据已刷新');
};

// 导出表格
const exportTable = () => {
  alert('导出功能开发中...');
};

// 导入表格
const importTable = () => {
  alert('导入功能开发中...');
};









// 监听editPinForm变化
watch(() => editPinForm.value, (newForm) => {
  // 可以在这里添加必要的业务逻辑
}, { deep: true });

// 监听窗口大小变化（保留用于未来扩展）
const handleResize = () => {
  // 可以在这里添加响应式布局逻辑
};

// 组件挂载时初始化数据
onMounted(() => {
  try {
    // 从全局状态初始化数据
    initializeFromStore();

    // 监控项目信息变化
    const info = projectStore.project_info || {};
    project_code = info.projectCode || '';
    project_name = info.name || '';

    console.log('project_code:', project_code);
    console.log('project_name:', project_name);
    console.log('从全局状态恢复的仓库信息:', form.gitlab);
    console.log('从全局状态恢复的分支信息:', form.project_branch);

    if (project_code=="" && project_name=="") {
      console.info('项目信息为空，请先选择项目');
      showTipDialog.value = true;
    } else {
      console.info('项目信息已选择，开始初始化');
      showTipDialog.value = false;
      // 如果没有仓库信息，则获取仓库列表
      if (!form.gitlab || spaceOptions.value.length === 0) {
        get_space();
      } else {
        // 如果有仓库信息但没有分支信息，则获取分支列表
        if (!form.project_branch || branchOptions.value.length === 0) {
          get_branch();
        } else {
          // 如果都有，直接提交分支信息
          submit_branch_info();
        }
      }
    }
   

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);

  } catch (error) {
    console.error('组件初始化错误:', error);
    messageManager.error('组件初始化失败，请刷新页面重试');
  }
});

// 监控项目信息变化
watch(() => projectStore.project_info, (newval, oldval) => {
  if (newval) {
    const newProjectCode = newval.projectCode || '';
    const newProjectName = newval.name || '';

    // 只有当项目代码真正发生变化时才重新获取仓库信息
    if (newProjectCode !== project_code) {
      console.log('项目切换:', project_code, '->', newProjectCode);

      project_code = newProjectCode;
      project_name = newProjectName;

      // 检查项目是否选择
      if (project_code=="" && project_name=="") {
        showTipDialog.value = true;
      } else {
        showTipDialog.value = false;
        // 清空全局状态和本地状态
        projectStore.clearCodeManagement();
        form.gitlab = '';
        form.project_branch = '';
        spaceOptions.value = [];
        branchOptions.value = [];
        get_space();
      }
    }
  }
});

// 监控仓库信息变化
watch(() => form.gitlab, ( newval, oldval) => {
  if (newval !== oldval) {
    // 同步更新全局状态
    projectStore.updateCodeManagement({ gitlab: newval });
    form.project_branch = '';
    get_branch();
  }
});

// 监控分支信息变化
watch(() => form.project_branch, ( newval, oldval) => {
  if (newval !== oldval) {
    // 同步更新全局状态
    projectStore.updateCodeManagement({ project_branch: newval });
    submit_branch_info();
  }
});


// 清理监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});


// 获取仓库列表
const get_space = async () => {
  try {
    loading.value = true;
    console.log('🔄 调用get_space - 开始获取仓库信息，项目代码:', project_code);
    console.trace('get_space调用堆栈:'); // 添加调用堆栈跟踪

    const response = await http.get('/code_management/space_options', {
      params: {
        project_code: project_code
      }
    });

    console.log("获取已创建的仓库信息:", response.data);

    if (response.data.status === 1) {
      spaceOptions.value = response.data.space_options || [];
      console.log('仓库列表:', spaceOptions.value);

      // 同步更新全局状态
      projectStore.setSpaceOptions(spaceOptions.value);

      // 设置默认仓库为第一个
      if (spaceOptions.value.length > 0 && !form.gitlab) {
        form.gitlab = spaceOptions.value[0];
        console.log('设置默认仓库:', form.gitlab);
        // 注意：这里不需要手动调用get_branch()，因为watch监听器会自动触发
      } else if (spaceOptions.value.length === 0) {
        messageManager.warning('该项目暂无可用仓库');
        showTipDialog.value = true;
      }
    } else {
      console.error('获取仓库信息失败:', response.data.message);
      messageManager.error(response.data.message || '获取仓库信息失败');
    }
  } catch (error) {
    console.error('获取仓库信息失败:', error.message);
    messageManager.error('获取仓库信息失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 获取分支列表
const get_branch = async () => {
  if (!form.gitlab) {
    console.warn('仓库地址为空，无法获取分支');
    return;
  }

  try {
    console.log('🔄 调用get_branch - 开始获取分支信息，仓库:', form.gitlab);
    console.trace('get_branch调用堆栈:'); // 添加调用堆栈跟踪

    const response = await http.get('/code_management/branch_options', {
      params: {
        project_code: project_code,
        project_gitlab: form.gitlab
      }
    });

    if (response.data.status === 1) {
      branchOptions.value = response.data.branch_options || [];
      console.log('分支列表:', branchOptions.value);

      // 同步更新全局状态
      projectStore.setBranchOptions(branchOptions.value);

      // 设置默认分支为第一个
      if (branchOptions.value.length > 0 && !form.project_branch) {
        form.project_branch = branchOptions.value[0];
        console.log('设置默认分支:', form.project_branch);
        // 注意：这里不需要手动调用submit_branch_info()，因为watch监听器会自动触发
      } else if (branchOptions.value.length === 0) {
        messageManager.warning('该仓库暂无可用分支');
        showTipDialog.value = true;
      }
    } else {
      console.error('获取分支信息失败:', response.data.message);
      messageManager.error(response.data.message || '获取分支信息失败');
    }
  } catch (error) {
    console.error('获取分支信息失败:', error.message);
    messageManager.error('获取分支信息失败: ' + error.message);
  }
};

// 提交分支信息并获取SDK版本
const submit_branch_info = async () => {
  if (!form.gitlab || !form.project_branch) {
    console.warn('仓库或分支信息不完整，无法提交');
    return;
  }

  try {
    loading.value = true;
    console.log('🔄 调用submit_branch_info - 提交分支信息:', {
      project_code,
      project_name,
      gitlab: form.gitlab,
      branch: form.project_branch
    });
    console.trace('submit_branch_info调用堆栈:'); // 添加调用堆栈跟踪

    const response = await http.get('/code_management/branch_submit', {
      params: {
        project_code: project_code,
        project_name: project_name,
        project_gitlab: form.gitlab,
        project_branch: form.project_branch
      },
      timeout: 60000
    });

    console.log("分支提交响应:", response.data);

    if (response.data.config_status === 1) {
        SdkVersion.value = response.data.sdk_version;
        console.log('SDK版本:', SdkVersion.value);

        // 同步更新全局状态
        projectStore.setSdkVersion(SdkVersion.value);

        // 更新分支创建权限状态
        branch_create.value = response.data.branch_create === true || response.data.branch_create === 'true';
        console.log('分支创建权限:', branch_create.value);

        // 可以在这里添加其他成功后的操作
        messageManager.success('项目配置加载成功');
         // 获取gpio芯片信息及引脚信息
        initTableData();

    } else {
      const errorMsg = response.data.message || '分支提交失败';
      console.error('分支提交失败:', errorMsg);
      messageManager.error(errorMsg);
    }
  } catch (error) {
    console.error('分支提交失败:', error.message);
    messageManager.error('分支提交失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 功能按钮处理函数
// download 操作
const handleDownload = () => {
  console.log('开始下载配置');
  messageManager.success('配置下载成功');
};

// commit 操作
const handleSave = async () => {
  ElMessageBox.prompt('请输入 commit 信息', '保存配置', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    inputErrorMessage: '请输入 commit 信息',
    inputValidator: (value) => value != null && value.trim() !== ''
  }).then(({ value }) => {
    console.log('Commit信息:', value);
    messageManager.success('配置已保存');
  }).catch(() => {
    console.log('取消保存');
  });
};

// push 操作
const handlePublish = () => {
  ElMessageBox.confirm(
    '确定要发布当前配置吗？发布后将生效并覆盖现有配置。',
    '发布确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    console.log('确认发布');
    messageManager.success('配置已发布');
  }).catch(() => {
    console.log('取消发布');
  });
};

// merge 确认操作
const confirmAction = (mergeBranch) => {
  ElMessageBox.confirm(
    '确定要merge当前分支到目标分支吗？',
    '发布确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    console.log('确认合并到:', mergeBranch);
    messageManager.success('分支合并成功');
  }).catch(() => {
    console.log('取消合并');
  });
};

// 测试merge功能（由工具栏组件处理弹窗）
const mergetest = () => {
  // 弹窗逻辑现在在Toolbar组件中处理
  console.log('准备合并操作');
};

</script>

<style scoped>
/* 顶部工具栏 */

.config-selects {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
  font-weight: normal;
}

.toolbar-form-item {
  margin-bottom: 0 !important;
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-form-item .el-select {
  width: 140px;
}

.toolbar-form-item .el-form-item__label {
  margin-bottom: 0;
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
  font-weight: normal;
}

.toolbar-form-item .el-form-item__content {
  margin-left: 0 !important;
}

.table-toolbar {
  display: flex;
  align-items: center;
  padding: 15px 30px;
  height: 60px;
  background: #f5f7fa;
  color: #606266;
  border-bottom: 1px solid #e4e7ed;
  margin: 0px;
  gap: 10px; /* 搜索框和IO类型之间的间距 */
}

.search-section {
  flex-shrink: 0;
}

.io-type-section {
  flex: 1;
  display: flex;
  justify-content: flex-start;
}

.io-type-form-item {
  margin-bottom: 0 !important;
}

.io-type-form-item .el-form-item__label {
  /* margin-right: 15px; */
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
  font-weight: normal;
}

.io-type-form-item .el-form-item__content {
  margin-left: 0 !important;
}

.io-type-form-item .el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.button-group {
  display: flex;
  gap: 8px;
}


.toolbar-right {
  display: flex;
  align-items: center;
  gap: 20px;
}


.layout-container {
  padding: 20px 0px;
  /* margin: 0 auto; */
  display: flex; /* 改为flex布局以支持百分比宽度 */
  gap: 20px;
  width: 100%;
  align-items: flex-start; /* 顶部对齐 */
}

/* 左侧容器 */
.left-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 60%; /* 左侧容器占60%宽度 */
  flex-shrink: 0; /* 防止收缩 */
}

/* 左侧第一行：芯片配置和芯片预览 */
.left-top-row {
  display: grid;
  grid-template-columns: 1fr 1.5fr; /* 芯片配置1份，芯片预览1.5份 */
  gap: 20px;
  align-items: start;
  height: 600px;
}

.chip-config-section {
  display: flex;
  flex-direction: column;
  height: 100%; /* 确保占满父容器高度 */
}

.chip-preview-section {
  display: flex;
  flex-direction: column;
  height: 100%; /* 确保占满父容器高度 */
}

/* 左侧第二行：引脚配置表 */
.left-bottom-row {
  display: flex;
  flex-direction: column;
}

/* 右侧容器：引脚编辑器 */
.right-container {
  display: flex;
  flex-direction: column;
  position: relative;
  height: fit-content; /* 改为自适应高度 */
  width: 40%;
}

/* 引脚编辑器面板 */
.pin-editor-panel {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.pin-editor-panel.collapsed {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* 面板头部 */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.panel-header:hover {
  background: #e9ecef;
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
}

.collapse-icon {
  font-size: 16px;
  color: #666;
  transition: transform 0.3s ease;
}

.collapse-icon.rotated {
  transform: rotate(-180deg);
}

/* 面板内容 */
.panel-content {
  padding: 0;
  transition: all 0.3s ease;
  overflow: hidden;
}

/* 折叠状态下隐藏内容 */
.pin-editor-panel.collapsed .panel-content {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

/* 展开状态下显示内容 */
.pin-editor-panel:not(.collapsed) .panel-content {
  max-height: none;
  opacity: 1;
  transform: translateY(0);
}

/* 确保PinEditor组件在面板中正确显示 */
.panel-content .pin-editor {
  border: none;
  box-shadow: none;
  border-radius: 0;
}

/* 确保卡片高度一致 */
.chip-config-section .card,
.chip-preview-section .card {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}









/* 响应式布局调整 */
@media (max-width: 1200px) {
  .left-container {
    width: 58%; /* 中等屏幕下调整左侧宽度 */
  }

  .right-container {
    width: 42%; /* 中等屏幕下调整右侧宽度 */
  }

  .left-top-row {
    grid-template-columns: 1fr 1.3fr; /* 调整芯片配置和预览的比例 */
  }
}

@media (max-width: 768px) {
  .layout-container {
    flex-direction: column; /* 垂直排列 */
  }

  .left-container {
    width: 100%; /* 小屏幕下占满宽度 */
  }

  .right-container {
    width: 100%; /* 小屏幕下占满宽度 */
  }

  .left-top-row {
    grid-template-columns: 1fr; /* 芯片配置和预览垂直排列 */
  }


}
.card {
  background: #fff;
  padding: 20px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内容溢出 */
}
.card-title,
.title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebedf0;
  position: relative;
  z-index: 10; /* 确保标题在芯片图之上 */
  background: #fff; /* 添加背景色 */
  flex-shrink: 0; /* 防止标题被压缩 */
}

/* 确保卡片内容区域能够正确分配剩余空间 */
.card > *:not(.card-title):not(.title) {
  flex: 1;
  overflow: hidden;
}
/* 表单内容滚动支持 */
.form-left-align {
  height: 100%;
  overflow-y: auto; /* 允许垂直滚动 */
  padding-right: 5px; /* 为滚动条留出空间 */
}

/* 使用更强的选择器确保标签宽度生效 */
.form-left-align.el-form .el-form-item .el-form-item__label {
  text-align: left !important;
  padding-right: 10px !important;
  width: 120px !important;
  min-width: 120px !important;
  flex-shrink: 0 !important;
  display: inline-block !important;
}

.form-left-align.el-form .el-form-item .el-form-item__content {
  flex: 1 !important;
  margin-left: 0 !important;
}

/* 顶部两个下拉框的布局 */
.top-row {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.top-form-item {
  width: 100%;
  /* flex: 1; */
  /* margin-bottom: 0 !important; */
}

.top-form-item .el-form-item__label {
  width: auto !important;
  margin-right: 8px;
  font-size: 14px;
  color: #606266;
}

.top-form-item .el-form-item__content {
  flex: 1;
}

/* 确保表单项布局一致 */
.form-left-align.el-form .el-form-item {
  display: flex !important;
  align-items: center !important;
  margin-bottom: 20px !important;
}

/* 确保所有表单控件宽度一致 */
.form-left-align .el-select,
.form-left-align .el-input-number {
  width: 100%;
}

.form-left-align .el-input-number .el-input__inner {
  width: 100%;
}

/* 芯片预览区域样式 */
.chip-preview-box {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%; /* 在正常模式下占满父容器高度 */
  overflow: hidden; /* 防止内容溢出 */
}
.slider-with-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.value-text {
  margin-left: 10px;
  min-width: 40px;
  text-align: right;
}

.pin-info {
  text-align: center;
  font-size: 14px;
}
.current-pin {
  font-weight: bold;
  color: #409eff;
}
/* 表格工具栏样式已移至上方的.table-toolbar */
.search-input {
  flex: 1;
  max-width: 300px;
}
.pagination {
  margin: 0px;
  padding: 15px 30px;
  text-align: right;
  background: #f5f7fa;
  border-top: 1px solid #e4e7ed;
}

/* 表格区域整体样式 - 将工具栏、表格、分页器连成一块 */
.left-bottom-row .card {
  padding: 20px 0px 0px 0px; /* 移除左右内边距，让表格区域占满 */
}

.left-bottom-row .card .card-title {
  padding: 0px 20px 10px 20px; /* 只给标题添加左右内边距 */
  margin-bottom: 15px;
}

/* 表格容器样式 */
.table-container {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin: 5px 20px;
  overflow: hidden;
}

/* 确保表格边框与容器无缝连接 */
.table-container .el-table {
  border: none !important;
  border-radius: 0 !important;
}

.table-container .el-table::before {
  display: none; /* 隐藏表格的默认边框线 */
}

.table-container .el-table .el-table__border-left-patch {
  display: none;
}

.table-container .el-table .el-table__border-right-patch {
  display: none;
}

/* 芯片预览区域的额外样式 */
.chip-preview-box {
  background-color: #f8fafc;
  border-radius: 4px;
  margin-bottom: 8px;
  position: relative;
  z-index: 1; /* 确保芯片图在标题之下 */
  padding: 20px 10px; /* 减少上下内边距，让高度更统一 */
}
.pin-highlight {
  fill: #409eff !important;
  stroke: #2c7be5;
  stroke-width: 1.5;
  cursor: pointer;
}
.pin-info {
  text-align: center;
  font-size: 14px;
  margin-top: 5px;
}
.current-pin {
  font-weight: bold;
  color: #409eff;
}
.pin-type {
  font-weight: bold;
  color: #67c23a;
}

/* 类型标签样式 */
.type-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.type-tag {
  margin: 1px;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  white-space: nowrap;
}


/* 顶部导航样式 */
.top-navigation {
  background: #fff;
  border-bottom: 2px solid #e4e7ed;
  padding: 0 20px;
  margin-bottom: 0;
}

.nav-tabs {
  display: flex;
  gap: 0;
}

.nav-tab {
  padding: 16px 24px;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.nav-tab:hover {
  color: #409eff;
  background-color: #f5f7fa;
}

.nav-tab.active {
  color: #409eff;
  border-bottom-color: #409eff;
  background-color: #ecf5ff;
}

.nav-tab i {
  font-size: 16px;
}

/* Merge弹窗样式 */
.merge-dialog {
  border-radius: 8px;
}

/* 修复弹窗定位问题 - 覆盖全局样式 */
.merge-dialog.el-dialog {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
  z-index: 2000 !important;
}

/* 确保弹窗遮罩层正常显示 */
.el-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 1999 !important;
}

.merge-dialog .el-dialog__header {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

.merge-dialog .el-dialog__body {
  padding: 20px;
}

.dialog-content {
  text-align: center;
  padding: 10px 0;
}

.dialog-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 16px;
}

.dialog-description {
  font-size: 16px;
  color: #606266;
  margin-bottom: 20px;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 10px 0;
}

.dialog-footer .el-button {
  min-width: 80px;
}

.no-type-text {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}



</style>