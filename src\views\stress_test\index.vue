<template>
    <div style="display: flex; flex-direction: column; height: calc(104.9vh - 250px);">
        <div class="tool-bar-container">
            <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button>
            <div style="margin-left: auto; display: flex; gap: 10px;">
                <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                    <el-button text bg @click="handleReset">重置</el-button>
                </el-tooltip>
                <filterButton @click="onFilterStatusChange" :count="filterCount" />
                <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
            </div>
        </div>

        <div class="filter-container" v-if="showFilterContainer">
            <el-input v-model="form.name" placeholder="请输入项目名称" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
            <el-input v-model="form.project_number" placeholder="请输入项目编号" @keyup.enter="onFilter" clearable>
                <template #append>
                    <el-button icon="Search" @click="onFilter"></el-button>
                </template>
            </el-input>
            <el-select v-model="form.status" placeholder="问题状态" style="width: 400px;" clearable @change="onFilter">
                <el-option label="待压测" value="0"></el-option>
                <el-option label="压测中" value="1"></el-option>
                <el-option label="已复现" value="2"></el-option>
                <el-option label="已解决" value="3"></el-option>
                <el-option label="已关闭" value="4"></el-option>
                <el-option label="未复现" value="5"></el-option>
            </el-select>

        </div>

        <el-table :data="tableData" stripe border style="width: 100%;flex: 1;" class="table-container">
            <el-table-column label="所属项目" min-width="150" align="center">
                <template #default="{ row }">
                    <div style="text-align: center;">
                        <span>{{ row.project_name }}({{ row.project_number }})</span>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="问题状态" width="100" align="center">
            <template #default="{ row }">
                <el-tag v-if="row.status == 0 || !row.status" type="info">待压测</el-tag>
                <el-tag v-if="row.status == 1" type="warning">压测中</el-tag>
                <el-tag v-if="row.status == 2" type="success">已复现</el-tag>
                <el-tag v-if="row.status == 3" type="success">已解决</el-tag>       
                <el-tag v-if="row.status == 4" type="success">已关闭</el-tag>
                <el-tag v-if="row.status == 5" type="danger">未复现</el-tag>
            </template>
        </el-table-column>
            <el-table-column label="背景介绍" min-width="200" align="center">
                <template #default="{ row }">
                    <div style="text-align: left;">
                        <p><span
                            style="font-weight: bold; color: #606266" 
                            >问题描述:</span> {{ row.verification?.background?.problem_description}}</p>
                        <p><span
                            style="font-weight: bold; color: #606266" 
                            >软件版本:</span> {{ row.verification?.background?.software_version}}</p>
                        <p><span
                            style="font-weight: bold; color: #606266" 
                            >硬件版本:</span> {{ row.verification?.background?.hardware_version}}</p>
                        <p><span
                            style="font-weight: bold; color: #606266" 
                            >PSN:</span> {{ row.verification?.background?.psn}}</p>
                        <p><span
                            style="font-weight: bold; color: #606266" 
                            >问题来源:</span> {{ row.verification?.background?.problem_source}}</p>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="测试环境说明" min-width="200" align="center">
                <template #default="{ row }">
                    <div style="text-align: left;">
                        <p><span style="font-weight: bold; color: #606266">样件数量:</span> {{ row.verification?.test_environment?.sample_count}}</p>
                        <p><span style="font-weight: bold; color: #606266">测试环境:</span> {{ row.verification?.test_environment?.description}}</p>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="验证方向" min-width="200" align="center">
                <template #default="{ row }">
                    <div style="text-align: left;">
                        <!-- <p><span>调研思维导图:</span> {{ row.verification?.validation_direction?.mind_map}}</p> -->
                        <p><span style="font-weight: bold; color: #606266">鱼骨图:</span> {{ row.verification?.validation_direction?.fishbone_diagram}}</p>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="验证方案" min-width="200" align="center">
                <template #default="{ row }">
                    <div style="text-align: left;">
                        <span style="font-weight: bold; color: #606266">飞书云文档:</span>
                        <el-link class="link-hover" style="color: #409EFF;" type="primary" :underline="false" :href="row.verification?.validation_plan?.feishu_doc" target="_blank">
                            {{ row.verification?.validation_plan?.feishu_doc }}
                        </el-link>
                    </div>
                </template>
            </el-table-column>

            <el-table-column label="操作" min-width="300" fixed="right" align="center">
                <template #default="{ row }">
                    <div style="display: flex; justify-content: center; gap: 10px;">
                        <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                        <el-button type="primary" size="small" @click="handleUpdateStatus(row)">状态变更</el-button>
                        <el-button type="primary" size="small" @click="handleDetail(row)">详情</el-button>
                        <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>

    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize"
            :total="total" background @change="onPageChange" />
    </div>

    <el-dialog v-if="dialogAddVisible" v-model="dialogAddVisible" title="添加项目" width="800"
        :close-on-click-modal="false">
        <ProjectAdd :project_number="form.project_number" @affirm="onAddAffirm" @cancel="onAddCancel" />
    </el-dialog>

    <el-dialog v-if="dialogEditVisible" v-model="dialogEditVisible" title="编辑项目" width="1800"
        :close-on-click-modal="false">
        <ProjectEdit @affirm="onEditAffirm" @cancel="onEditCancel" :r_id="r_id" />
    </el-dialog>

    <el-dialog v-if="dialogVisible" v-model="dialogVisible" title="状态变更" width="600px">
            <StatusUpdate :versionInfo="versionInfo" @cancel="dialogVisible = false" @confirm="onUpdateStatusConfirm" />
        </el-dialog>
    
    <el-drawer v-model="drawerDetailVisible" :with-header="false" size="95%" :destroy-on-close="true">
        <ProjectDetail :r_id="r_id" />
    </el-drawer>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import http from '@/utils/http/http.js';
import ProjectAdd from './add.vue';
import ProjectEdit from './edit.vue';
import ProjectDetail from './detail.vue';
import { useRouter, useRoute } from 'vue-router';
import { useProjectStore } from '@/stores/project.js';
import StatusUpdate from './status_update.vue';
import filterButton from '@/components/filterButton.vue';
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/test_reports_v2/list', '测试报告列表');

const dialogAddVisible = ref(false);
const tableData = ref([]);
const dialogEditVisible = ref(false);
const drawerDetailVisible = ref(false);
const router = useRouter();
const filterCount = ref(0);

let r_id = ref(0);
let dialogVisible = ref(false);
let projectStore = useProjectStore();
let versionInfo = ref(null);
let form = reactive({
    page: 1,
    pagesize: 15,
    project_number: '',
});
let total = ref(0);

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};
function handleUpdateStatus(row) {
    versionInfo.value = row;
    dialogVisible.value = true;
};
function update_table() {
    http.get('/stress_tests', { params: form })
      .then(res => {
            tableData.value = res.data.data.results.map(item => {
                try {
                    item.verification = JSON.parse(item.verification || "{}");
                } catch (e) {
                    console.error("Failed to parse verification:", e);
                    item.verification = {};
                }
                return item;
            });
            total.value = res.data.data.count;
        })

        filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'project_number'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};


function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function onUpdateStatusConfirm() {
    dialogVisible.value = false;
    update_table();
};
console.log("form.project_number",form);
function handleAdd() {
    dialogAddVisible.value = true;
};

function handleEdit(row) {
    r_id.value = row.id;
    dialogEditVisible.value = true;
};

function handleDetail(row) {
    r_id.value = row.id;
    drawerDetailVisible.value = true;
};

function handleDelete(row) {
    ElMessageBox.confirm(
        '确定删除吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.delete(`/stress_tests/${row.id}`).then(res => {
            ElMessage({
                message: '删除成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessage({
                type: 'error',
                message: err.response.data.msg
            });
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};

function onAddAffirm() {
    dialogAddVisible.value = false;
    update_table();
};

function onAddCancel() {
    dialogAddVisible.value = false;
};

function onEditAffirm() {
    dialogEditVisible.value = false;
    update_table();
};

function onEditCancel() {
    dialogEditVisible.value = false;
};

function handleRefresh() {
    update_table();
};

onMounted(() => {
    form.project_number = projectStore.project_info.projectCode; // 确保初始加载时设置 project_number
    update_table();
});


watch(() => projectStore.project_info, () => {
    form.project_number = projectStore.project_info.projectCode; // 确保 projectStore 更新时同步更新 form.project_number
    update_table();
});

</script>

<style scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
  border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    display: flex;
    justify-content: flex-start;
}

/* 添加样式确保文本左对齐 */
.el-table .cell {
    text-align: left;
}

.link-hover:hover {
    color: #7ab8fc !important; /* 悬浮时的颜色 */
    cursor: pointer !important; /* 鼠标悬浮时显示为手型 */
}
</style>