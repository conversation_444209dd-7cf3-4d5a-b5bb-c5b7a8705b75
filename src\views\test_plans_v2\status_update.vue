<template>
    <div>

        <div class="status-update">
            <el-form label-width="auto" class="form">
                <el-form-item label="当前状态:">
                    <span>{{ statusMap[status] || status }}</span>
                    <el-input style="display: none;" />
                </el-form-item>
                <el-form-item label="目标状态:">
                    <el-select v-model="targetStatus" placeholder="请选择状态">
                        <template v-if="plan_type == 0">
                            <template v-if="status == 'APPROVED'">
                                <el-option label="执行中" value="RUNNING"></el-option>
                            </template>
                            <template v-else-if="status == 'RUNNING'">
                                <el-option label="已完成" value="COMPLETED"></el-option>
                            </template>
                        </template>
                        <template v-else>
                            <template v-if="status == 'DEBUGGING'">
                                <el-option label="评审通过" value="APPROVED"></el-option>
                            </template>
                            <template v-if="status == 'APPROVED'">
                                <el-option label="执行中" value="RUNNING"></el-option>
                            </template>
                            <template v-else-if="status == 'RUNNING'">
                                <el-option label="已完成" value="COMPLETED"></el-option>
                            </template>
                        </template>
                    </el-select>
                </el-form-item>
            </el-form>
        </div>

        <div style="display: flex; justify-content: end;">
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onConfirm">确认</el-button>
        </div>
    </div>

</template>


<script setup>
import { ref, computed } from 'vue';
import http from '@/utils/http/http.js';
import { ElMessageBox } from 'element-plus';

const props = defineProps({
    versionInfo: {
        type: Object,
        required: true,
    },
});

const statusMap = {
    'DEBUGGING': '调试中',
    'REVIEWING': '评审中',
    'APPROVED': '评审通过',
    'REJECTED': '评审不通过',
    'RUNNING': '执行中',
    'COMPLETED': '已完成',
};

const status = computed(() => props.versionInfo.status);
const plan_type = computed(() => props.versionInfo.plan_type);
const targetStatus = ref('');

const emit = defineEmits(['confirm', 'cancel']);

function onCancel() {
    emit('cancel');
};

function onConfirm() {
    http.post('/v2/test_plans/update_status', { id: props.versionInfo.id, status: targetStatus.value }).then(res => {
        emit('confirm');
    }).catch(err => {
        ElMessageBox.alert(err.response.data.msg, '错误', {
            confirmButtonText: '确定',
            type: 'error',
        });
    });
};

</script>

<style scoped>
.status-update {
    display: flex;
    justify-content: center;
    align-items: center;
}

.form {
    padding: 20px;

    width: 100%;
}
</style>