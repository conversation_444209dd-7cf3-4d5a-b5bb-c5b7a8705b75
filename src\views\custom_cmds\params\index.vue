<template>
    <div style="width: 100%;">
        <el-button icon="Plus" plain type="primary" @click="addItem">添加参数</el-button>
        <el-table :data="model" style="width: 100%">
            <el-table-column label="参数类型" prop="param_type" align="center">
                <template #default="{ row }">
                    <span v-if="row.param_type === 'el-input'">字符串</span>
                    <span v-else-if="row.param_type === 'el-input-number'">数字</span>
                    <span v-else-if="row.param_type === 'el-select'">选项</span>
                    <span v-else-if="row.param_type === 'el-color-picker'">颜色</span>
                </template>
            </el-table-column>
            <el-table-column label="参数名称" prop="param_name" align="center"></el-table-column>
            <el-table-column label="参数默认值" prop="param_value" align="center"></el-table-column>
            <el-table-column label="操作" align="center">
                <template #default="{ row }">
                    <el-button type="primary" size="small" @click="() => editItem(row)">编辑</el-button>
                    <el-button type="primary" size="small" @click="() => copyItem(row)">复制</el-button>
                    <el-button type="danger" size="small" @click="() => removeItem(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>

    <el-dialog v-if="dialogVisible" v-model="dialogVisible" title="添加参数" width="600" :close-on-click-modal="false">
        <Item @submit="onSubmit" @cancel="dialogVisible = false" />
    </el-dialog>

    <el-dialog v-if="dialogEditVisible" v-model="dialogEditVisible" title="编辑参数" width="600" :close-on-click-modal="false">
        <Item :param="cur_param" @submit="onEditSubmit" @cancel="dialogEditVisible = false"/>
    </el-dialog>

</template>

<script setup>
import { ref } from 'vue';
import Item from './item.vue';

const model = defineModel();

const cur_param = ref({});
const dialogVisible = ref(false);
const dialogEditVisible = ref(false);

const addItem = () => {
    dialogVisible.value = true;
};

const removeItem = (row) => {
    model.value = model.value.filter(item => item !== row);
};

const editItem = (row) => {
    cur_param.value = row;
    dialogEditVisible.value = true;
};

const copyItem = (row) => {
    const copiedRow = JSON.parse(JSON.stringify(row));
    model.value.push(copiedRow);
};

const onSubmit = (data) => {
    model.value.push(data);
    dialogVisible.value = false;
};

const onEditSubmit = (data) => {
    const index = model.value.findIndex(item => item === cur_param.value);
    model.value.splice(index, 1, data);
    dialogEditVisible.value = false;
};

</script>

<style lang="scss" scoped>
</style>