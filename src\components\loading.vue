<template>
    <div class="custom-loading" :style="{ zIndex }">
        <div class="loading-content"> </div>
        <div class="loading-tip">{{ text }}</div>
    </div>
</template>


<script setup>
defineProps({
    text: {
        type: String,
        default: "加载中..."
    },
    zIndex: {
        type: Number,
        default: 10000
    }
});
</script>


<style lang="scss" scoped>
.custom-loading {
  position: absolute;
  display: flex;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background-color: #fff9;
}

.loading-tip {
  color: #000;
}

.loading-content {
  width: 90px;
  height: 90px;
  background-size: auto 100%;
  background-repeat: no-repeat;
  background-image: url(@/assets/images/loading.svg);
  animation-duration: 2s;
  animation-name: loading-animation;
  animation-iteration-count: infinite;
  animation-timing-function: steps(24);
}

@keyframes loading-animation {
  0% {
    background-position-x: 0;
  }

  to {
    background-position-x: 100%;
  }
}
</style>