<template>
  <div>
    <h2>{{ requirement.name }}</h2>
    <el-tabs v-model="activeName">
      <el-tab-pane label="基本属性" name="1">
        <div style="padding: 20px">
          <el-form label-width="auto">
            <el-form-item label="所属模块：">
              <span>{{ requirement.moduleName }}</span>
            </el-form-item>
            <el-form-item label="需求属性：">
              <span>{{ propertyMap[requirement.property] || requirement.property }}</span>
            </el-form-item>
            <el-form-item label="优先级：">
              <span>{{ requirement.priority }}</span>
            </el-form-item>
            <el-form-item label="需求来源：">
              <span>{{ sourceMap[requirement.source] || requirement.source }}</span>
            </el-form-item>
            <el-form-item label="需求状态：">
              <span>{{ stateMap[requirement.state] || requirement.state }}</span>
            </el-form-item>
            <el-form-item label="特性等级：">
              <span>{{ peculiarityLevelMap[requirement.peculiarityLevel] || requirement.peculiarityLevel }}</span>
            </el-form-item>
            <el-form-item label="状态：">
              <span>{{ statusMap[requirement.status] || requirement.status }}</span>
            </el-form-item>
            <el-form-item label="需求类别：">
              <span>{{ categoryMap[requirement.category] || requirement.category }}</span>
            </el-form-item>
            <el-form-item label="文档名称：">
              <span>{{ requirement.documentName }}</span>
            </el-form-item>
            <el-form-item label="文档章节：">
              <span>{{ requirement.documentChapter }}</span>
            </el-form-item>
            <el-form-item label="创建人：">
              <span>{{ requirement.creatorUser?.realName }}</span>
            </el-form-item>
            <el-form-item label="责任人：">
              <span>{{ requirement.updateUser?.realName }}</span>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
      <el-tab-pane label="详细信息" name="2">
        <el-form label-width="auto" label-position="top">
          <el-form-item label="需求描述：">
            <div v-html="descConverter(requirement.describe)"></div>
          </el-form-item>
          <el-form-item label="验证准则：">
            <span>{{ requirement.verifyCriterion }}</span>
          </el-form-item>
          <el-form-item label="备注：">
            <span>{{ requirement.remark }}</span>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import http from '@/utils/http/http.js';

const activeName = ref('2')

const props = defineProps({
  requirement: {
    type: Object,
    required: true
  },
  requirementId: {
    type: String,
    default: ''
  },
})

const requirement = ref({})

const propertyMap = {
  "PRODUCT": '产品需求',
  "TITLE": '需求标题',
  "FUNCTION_SAFE": '功能安全需求',
  "NON_FUNCTION_SAFE": '非功能安全需求',
};

const reviewStatusMap = {
  "CANCEL": '已撤销',
  "INIT": '初始状态',
  "APPROVE": '审批中',
  "ACCEPT": '已评审',
  "REJECT": '已驳回',
};

const multiReviewStatusMap = {
  "CANCEL": '已撤销',
  "INIT": '未评审',
  "APPROVE": '审批中',
  "ACCEPT": '已评审',
  "REJECT": '已驳回',
};

const stateMap = {
  "NOT_START": '未开始',
  "DOING": '进行中',
  "REALIZE": '已实现',
}

const sourceMap = {
  "CLIENT": '客户需求',
  "INNER": '内部需求',
}

const peculiarityLevelMap = {
  "NORMAL": '一般特性',
  "KEY": '关键特性',
  "IMPORT": '重要特性',
}

const categoryMap = {
  "FUNCTION": '功能性需求',
  "NOT_FUNCTION": '非功能性需求',
}

const statusMap = {
  "ACCEPT": "接受",
}

function descConverter(desc) {
  if (!desc) return desc;
  return desc.replace('ipd.hiwaytech.com:56289', 'ipd.hiway.com:56289');
}

onMounted(() => {
  if (props.requirementId) {
    http.get(`/requirements/${props.requirementId}`).then(res => {
      let data = res.data.data;
      requirement.value = data;
    }).catch(err => {
      console.log(err);
    });
  } else {
    requirement.value = props.requirement;
  }
})

</script>

<style lang="scss" scoped>
:deep(.el-form-item__label) {
  font-weight: bold;
}
</style>