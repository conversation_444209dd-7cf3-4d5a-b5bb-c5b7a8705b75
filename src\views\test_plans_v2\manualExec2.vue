<template>
    <div>
        <el-form :model="form" :rules="rules" label-width="auto" label-position="left" status-icon ref="formRef" style="padding: 20px;">
            <el-form-item label="测试值" prop="value">
                <el-input v-model="form.value" placeholder="请输入测试值"></el-input>
            </el-form-item>
            <el-form-item label="测试结果" prop="result_two" >
            <el-select v-model="form.result_two" placeholder="请选择测试结果">
                <el-option label="PASS" value="1"></el-option>
                <el-option label="NG" value="0"></el-option>
                <el-option label="NA" value="2"></el-option>
                <el-option label="NT" value="3"></el-option>
            </el-select>
        </el-form-item>
            <el-form-item label="备注" style="margin-left: 10px">
                <el-input style="margin-left: -10px" type="textarea" :rows="3" v-model="form.remark" placeholder="请输入备注"></el-input>
            </el-form-item>
            <el-form-item label="提示" style="margin-left: 10px">
            <span style="margin-left: -10px">PASS：通过、NG：不通过、NA：不适用、NT：未测试</span>
        </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end;">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="confirm">确 定</el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import { ElMessage, ElMessageBox } from 'element-plus';

const props = defineProps({
    manualExecContext: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(['confirm', 'cancel']);

const formRef = ref(null);

const form = ref({
    test_plan_id: props.manualExecContext.test_plan_id,
    test_case_id: props.manualExecContext.test_case_id,
    test_case_version: props.manualExecContext.test_case_version,
    value: props.manualExecContext.value,
    result_two: props.manualExecContext.result_two, 
    remark: props.manualExecContext.remark,
    generation_mode: '1',
});

const rules = ref({
    value: [
        { required: true, message: '请输入测试值', trigger: 'blur' }
    ],
    result_two: [
        { required: true, message: '请选择测试结果', trigger: 'change' }
    ]
});

const cancel = () => {
    emit('cancel');
}

const confirm = () => {
    formRef.value.validate((valid) => {
        if (valid) {
            http.post('/v2/test_plans/test_case_exec', form.value).then(res => {
                ElMessage({
                    message: '添加成功.',
                    type: 'success',
                });
                emit('confirm', form.value);
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            })
        } else {
            return false;
        }
    });
}

 // 确保 result_two 的值是字符串类型，与 el-option 的 value 类型一致
 form.value.result_two = form.value.result_two?.toString();
</script>


<style scoped></style>