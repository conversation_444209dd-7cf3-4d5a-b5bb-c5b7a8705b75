<template>
    <div class="mindMapDemo">
        <div id="mindMapContainer"></div>
        <div class="button"  style="margin-left: auto;">
            <router-link to="/functions2/list" >
                <el-button type="primary" text bg style="color: #606266;" >列表视图</el-button>
            </router-link>
        </div>

    </div>

</template>

<script setup>
import { ref, onMounted, shallowRef } from 'vue'
import MindMap from "simple-mind-map"
import { createUid } from 'simple-mind-map/src/utils'
import http from '@/utils/http/http.js'

const tableData = ref([])

function filterItems(items) {
    items = items.filter(item => item.deprecated === false);
    
    items.forEach(item => {
        if (item.children && item.children.length > 0) {
            item.children = filterItems(item.children);
        }
    });

    return items;
};

function updata_data() {
    http.get('/functions', { params: { name: '', number: '' } })

        .then(res => {
            // tableData.value = res.data.data.results;
            let items = res.data.data.results;
            items = filterItems(items);
            tableData.value = items;
            parse_data()
        });
}

const name = []
let dataTemplate = {
    data: { text: '功能模块' },
    children: [

    ]
}
let childrenArray = []
let mindMap = null;
let childrenArray2 = []
const new_data = null


function parse_data() {
    tableData.value.forEach(item => {
        
        const rootNode = {
            data: { text: item.name,
                
             },
            children: []
        };
        
        // 如果当前节点有子节点，递归调用处理子节点
        if (item.children && Array.isArray(item.children)) {
            rootNode.children = printObjectProperties(item.children);
        }
        dataTemplate.children.push(rootNode);
    });
    
    mindMap.updateData(dataTemplate);
    
   
}


function printObjectProperties(new_data) {
    let childNodes = []
    for (let j = 0; j < new_data.length; j++) {
        const item = new_data[j]
        let childNode = {
            data: { text: item.name,
                // 这里调节二级子节点的样式
             },
            children: []
        };
        if (item.children && Array.isArray(item.children)) {
            childNode.children = printObjectProperties(item.children)
        }
        childNodes.push(childNode);
    }
    return childNodes
}





function instance() {
    mindMap = new MindMap({
        el: document.getElementById('mindMapContainer'),
        data: dataTemplate,


        initRootNodePosition: ['3%', '50%'],
      
        

    })

}

onMounted(() => {
    updata_data()

    instance();
})
</script>



<style>
.mindMapDemo {
    width: 100%;
    
    position: relative;
}

#mindMapContainer {

    margin-left: -20px;
    margin-top: -20px;
    margin-bottom: -22px;
    margin-right: -20px;
    min-width: 1070px;
    min-height: 91.75vh;
   
}

#mindMapContainer * {
    margin: 0;
    padding: 0;
    /* display: flex; */
}

.button {
    position: absolute;
    right: 0;
    top: 20px;
 
    
}

/* 正常屏幕 */
@media (max-width: 1200px) {
    #mindMapContainer {
        min-height: 370vh; 
    }
}
 
/* 针对更小屏幕的样式（如手机） */
@media (max-width: 768px) {
    #mindMapContainer {
        min-height: 150vh;
    }
}

</style>