<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="名称" prop="name">
                <el-input v-model="form.name"></el-input>
            </el-form-item>

            <el-form-item label="图片文件" prop="thumbnail">
                <el-upload
                    class="upload-demo"
                    action=""
                    :auto-upload="false"
                    :file-list="imageList"
                    :on-change="handleFileChange('image')"
                    :before-upload="beforeUploadImage"
                    accept=".jpg,.jpeg,.png,.gif,.bmp"
                    list-type="picture-card"
                    v-model="form.thumbnail"
                    style="display: flex; align-items: center;"
                >
                <i slot="trigger" class="el-icon-plus" style="font-size: 30px; cursor: pointer;"></i>
                <div slot="tip" class="el-upload__tip" style="margin-left: 10px; font-size: 12px; color: #999;">
                    只能上传图片文件（.jpg, .jpeg, .png, .gif, .bmp）
                </div>
                </el-upload>
            </el-form-item>

            <el-form-item label="描述">
                <el-input type="textarea" :rows="4" v-model="form.desc"></el-input>
            </el-form-item>

          

            <div class="submit-button-container">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref } from 'vue';

import http from '@/utils/http/http.js';

const formRef = ref(null);

const form = ref({
    name: '',
    type: 0,
    image: null,
    rgb: '',
    desc: '',
});

const rules = ref({
    name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
    ],
});

const imageList = ref([]);

const handleFileChange = (field) => (file) => {
  form.value[field] = file.raw;
  if (field === 'image') {
    imageList.value = [file];
  }
};

const beforeUploadImage = (file) => {
  return false; 
};

const emit = defineEmits(['submit', 'cancel'])

const onSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {

            const formData = new FormData();
            formData.append('name', form.value.name);
            formData.append('number', form.value.type);
            formData.append('rgb', form.value.rgb);
            formData.append('desc', form.value.desc);
            formData.append('image', form.value.image);


            http.post('/image_resources', formData).then(res => {
                emit('submit');
              

            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onCancel = () => {
    emit('cancel');
};

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>