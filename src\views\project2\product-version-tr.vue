<template>

    <div style="padding: 20px;">
        <el-form>

            <el-form-item label="版本提测总数：">
                <span>{{ pvInfo.total }}</span>
            </el-form-item>
            <el-form-item label="版本废弃数：">
                <span>{{ pvInfo.deprecated }}</span>
            </el-form-item>
            <el-form-item label="版本提测率：">
                <span>{{ tr }}%</span>
            </el-form-item>

        </el-form>
    </div>

</template>

<script setup>
import { ref, computed, watch } from 'vue'
import http from '@/utils/http/http.js';
import { useProjectStore } from '@/stores/project.js';


const projectStore = useProjectStore()

const pvInfo = ref({})

const tr = computed(() => {
    if (pvInfo.value.total) {
        return ((pvInfo.value.total - pvInfo.value.deprecated) / pvInfo.value.total * 100).toFixed(2);
    } else {
        return 0;
    }
})


watch(() => projectStore.project_info.projectCode, () => {
    if (projectStore.project_info?.projectCode) {
        http.get('/projects/pv_tr', { params: { project_number: projectStore.project_info.projectCode } }).then(res => {
            pvInfo.value = res.data.data;
        });
    }
}, { immediate: true })


</script>

<style lang="scss" scoped>
.el-form-item {
    margin-bottom: 0;
}
</style>