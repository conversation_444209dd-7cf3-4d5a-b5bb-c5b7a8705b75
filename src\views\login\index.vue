<template>
    <div class="login-container">
        <div class="content-container">
            <el-form :model="form" label-width="auto">
                <el-form-item label="用户名">
                    <el-input v-model="form.username"></el-input>
                </el-form-item>
                <el-form-item label="密码">
                    <el-input v-model="form.password" type="password"></el-input>
                </el-form-item>

                <el-button type="primary" @click="login" style="width: 100%;">登录</el-button>

            </el-form>
        </div>
    </div>
</template>


<script setup>

import { reactive } from 'vue';
import axios from 'axios';
import { useUserStore } from '@/stores/user.js';
import { useRouter } from 'vue-router';
import { useSiderStore } from '@/stores/sider.js';

let userStore = useUserStore();

const sider = useSiderStore();

const form = reactive({
    username: '',
    password: ''
});

let router = useRouter();


function login() {
    axios.post(import.meta.env.VITE_BASE_URL + '/users/login2', form).then(res => {
        let data = res.data.data;
        sessionStorage.setItem('token', data.access_token);
        userStore.update(data.user_info || {});

        let redirectUrl = localStorage.getItem('redirectUrl');
        if (redirectUrl) {
            router.push(redirectUrl);
            localStorage.removeItem('redirectUrl');
        } else {
            router.push('/');
        }
    }).catch(err => {
        console.log(err);
    });
}

</script>

<style lang="scss" scoped>
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;

    background: url(@/assets/images/loginbg.jpg) 0% 0% / cover no-repeat;
}

.content-container {
    background: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 5px;
    width: 400px;
}
</style>