<template>
    <el-divider />
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item v-if="props.parent?.id" label="父项目">
                <el-input :value="`${props.parent.name}(${props.parent.number})`" readonly></el-input>
            </el-form-item>

            <el-form-item label="模块名称" prop="name">
                <el-input v-model="form.name"></el-input>
            </el-form-item>

            <el-form-item label="模块编号" prop="number">
                <el-input v-model="form.number" @input="toUpperCase"></el-input>
            </el-form-item>

            <el-form-item label="模块描述">
                <el-input type="textarea" :rows="4" v-model="form.desc"></el-input>
            </el-form-item>

            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSubmit">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref } from 'vue';

import http from '@/utils/http/http.js';

const props = defineProps(
    {
        parent: {
            type: Object,
            default: null,
        }
    }
);

const formRef = ref(null);

const form = ref({
    name: '',
    number: '',
    desc: '',
});

const rules = ref({
    name: [
        { required: true, message: '请输入模块名称', trigger: 'blur' },
    ],
    number: [
        { required: true, message: '请输入模块编号', trigger: 'blur' },
    ],
});

const emit = defineEmits(['confirm', 'cancel']);

const toUpperCase = (value) => {
    form.value.number = value.toUpperCase();
};

const onSubmit = () => {
    let data = {
        ...form.value,
    }

    if (props.parent?.id) {
        data.parent_id = props.parent.id;
    }

    formRef.value.validate(async (valid) => {
        if (valid) {
            http.post('/functions', data).then(res => {
                ElMessage({
                    message: '添加成功.',
                    type: 'success',
                });
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

function onCancel() {
    emit('cancel');
};

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>