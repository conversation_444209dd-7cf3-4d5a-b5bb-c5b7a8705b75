<template>

    <div style="display: flex; flex-direction: column; height: calc(100vh - 300px);padding: 10px;">
        <div style="margin-bottom: 5px;display: flex;justify-content: end;">
            <el-button type="primary" :loading="saveLoading" @click="handleSave">保存</el-button>
            <el-button icon="Refresh" @click="handleRefresh">刷新</el-button>
        </div>

        <el-table :data="tableData" stripe border row-key="id" ref="tableRef" :tree-props="{
            children: 'children',
            hasChildren: 'hasChildren',
            checkStrictly: true
        }" @select="handleSelect" style="flex: 1;">

            <el-table-column type="selection" min-width="55" align="center" />
            <el-table-column prop="name" label="名称" min-width="200"></el-table-column>
            <el-table-column prop="number" label="编号" min-width="200"></el-table-column>
            <el-table-column prop="desc" label="描述" min-width="500"></el-table-column>
            <!-- <el-table-column label="操作" min-width="100" fixed="right"></el-table-column> -->

        </el-table>
    </div>

</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import http from '@/utils/http/http.js';
import { useProjectStore } from '@/stores/project.js';
import { ElMessage, ElMessageBox } from 'element-plus';

const tableData = ref([])
const tableRef = ref(null)
const saveLoading = ref(false)
const projectStore = useProjectStore()

const findRowById = (id) => {
    if (!id) return null

    const find = (data) => {
        for (const node of data) {
            if (node.id === id) {
                return node
            }
            if (node.children) {
                const found = find(node.children)
                if (found) return found
            }
        }
        return null
    }
    return find(tableData.value)
}

function toggleRowParentSelection(row) {
    if (!row || row.parent_id === null) return

    try {
        const parentRow = findRowById(row.parent_id)
        if (parentRow && tableRef.value) {
            tableRef.value.toggleRowSelection(parentRow, true)
            toggleRowParentSelection(parentRow)
        }
    } catch (error) {
        console.error('Error toggling row selection:', error)
    }
}

function unToggleRowChildrenSelection(row) {
    if (!row || !row.children) return

    try {
        row.children.forEach(child => {
            tableRef.value.toggleRowSelection(child, false)
            unToggleRowChildrenSelection(child)
        })
    } catch (error) {
        console.error('Error untoggling row selection:', error)
    }
}

const handleSelect = (selection, row) => {
    // 取消选择其他行
    const selectedRows = this.$refs.tableRef.selection;
    selectedRows.forEach(sr => {
        if (sr !== row) {
            this.$refs.tableRef.toggleRowSelection(sr, false);
        }
    });

    // 选择当前行
    this.$refs.tableRef.toggleRowSelection(row, true);
}

const handleSave = () => {
    const selectedRows = tableRef.value.getSelectionRows();
    const ids = selectedRows.map(row => row.id);

    saveLoading.value = true;
    http.post('/projects/modules', { project_number: projectStore.project_info.projectCode, module_ids: ids })
        .then(res => {
            ElMessage.success('保存成功');

            // 保存成功后，根据返回的 ids 重新设置选中状态
            const savedIds = res.data.data.savedIds || []; // 假设后端返回保存后的 ids
            tableRef.value.clearSelection(); // 清除当前选中状态
            toggleRowSelectionByIds(tableData.value, savedIds); // 根据保存后的 ids 重新设置选中状态
        })
        .catch((err) => {
            ElMessageBox.alert(err.response.data.msg, '保存失败', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error',
            });
        })
        .finally(() => {
            saveLoading.value = false;
        });
};

const handleRefresh = () => {
    http.get('/functions').then(res => {
        let items = res.data.data.results;
        tableData.value = items;
    });
}

function toggleRowSelectionByIds(rows, ids) {

    rows.forEach(row => {
        if (ids.includes(row.id)) {
            tableRef.value.toggleRowSelection(row, true)
        }
        if (row.children) {
            toggleRowSelectionByIds(row.children, ids)
        }
    })
}

watch([() => projectStore.project_info.projectCode, tableData], () => {
    if (projectStore.project_info.projectCode) {
        http.get('/projects/modules', { params: { project_number: projectStore.project_info.projectCode } }).then(res => {
            let ids = res.data.data.ids;
            tableRef.value.clearSelection();
            toggleRowSelectionByIds(tableData.value, ids);
        });
    }
}, { immediate: true })

onMounted(() => {
    http.get('/functions').then(res => {
        let items = res.data.data.results;
        tableData.value = items;
    });
});

</script>

<style lang="scss" scoped></style>