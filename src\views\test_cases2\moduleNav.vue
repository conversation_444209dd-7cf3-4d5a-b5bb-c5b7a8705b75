<template>
    <el-tree :data="treeData" :props="{ label: 'name', value: 'number' }" show-checkbox @check="onCheck" check-strictly
        ref="treeRef" check-on-click-node :expand-on-click-node="false">

        <template #default="{ node, data }">
            <span>{{ node.label }} <span v-if="moduleTestCaseCount[data.number2]">({{
                moduleTestCaseCount[data.number2] }})</span> </span>
        </template>

    </el-tree>
</template>

<script setup>
import { ref, watch } from 'vue';
import http from '@/utils/http/http.js';
import { useProjectStore } from '@/stores/project.js';

const props = defineProps({
    deprecatedHide: {
        type: <PERSON>olean,
        default: false,
    },
    action_type: {
        type: String,
        default: null,
    },
});

const treeData = ref([]);
let projectStore = useProjectStore();
const treeRef = ref(null);
const moduleTestCaseCount = ref({});



const emit = defineEmits(['check']);

const onCheck = (curData, checkedData) => {
    let module1 = checkedData.checkedNodes.filter(node => node.level == 1).map(node => node.number2);
    let module2 = checkedData.checkedNodes.filter(node => node.level == 2).map(node => node.number2);
    let module3 = checkedData.checkedNodes.filter(node => node.level == 3).map(node => node.number2);

    emit('check', module1, module2, module3);
};

function filterItems(items) {
    items = items.filter(item => item.deprecated === false);

    items.forEach(item => {
        if (item.children && item.children.length > 0) {
            item.children = filterItems(item.children);
        }
    });

    return items;
};

function updateModuleTestCaseCount() {
    let project_number = projectStore.project_info.projectCode || '';
    let action_type = props.action_type || '';

    http.get('/functions/test_case_stat', { params: { project_number: project_number, action_type: action_type } }).then(res => {
        moduleTestCaseCount.value = res.data.data;
    });
}

watch(() => projectStore.project_info.projectCode, () => {
    let project_number = projectStore.project_info.projectCode || '';

    if (project_number == "") {
        http.get('/functions').then(res => {
            let items = res.data.data.results;
            if (props.deprecatedHide) {
                items = filterItems(items);
            }
            treeData.value = items;
        });
    } else {
        http.get('/projects/modules', { params: { project_number: project_number } }).then(res => {
            let items = res.data.data.results;
            if (props.deprecatedHide) {
                items = filterItems(items);
            }
            treeData.value = items;
        });
    }

    updateModuleTestCaseCount();

}, { immediate: true });

watch(() => props.action_type, () => {
    updateModuleTestCaseCount()
});

</script>

<style scoped>
:deep(.el-tree-node__content) {
    height: 40px;
}
</style>