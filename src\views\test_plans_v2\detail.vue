<template>
    <div>
        <div class="top-tool-container">

            <h2>
                {{ testPlan?.name }}
                <el-tag v-if="planStatus == 'DEBUGGING'" type="info" round>调试中</el-tag>
                <el-tag v-else-if="planStatus == 'REVIEWING'" type="warning" round>评审中</el-tag>
                <el-tag v-else-if="planStatus == 'APPROVED'" type="success" round>评审通过</el-tag>
                <el-tag v-else-if="planStatus == 'REJECTED'" type="danger" round>评审不通过</el-tag>
                <el-tag v-else-if="planStatus == 'RUNNING'" type="success" round>执行中</el-tag>
                <el-tag v-else-if="planStatus == 'COMPLETED'" type="primary" round>已完成</el-tag>
            </h2>
            <div class="tool-container">
                <el-button text bg icon="Edit" @click="onEdit">编辑</el-button>
            </div>
        </div>

        <el-tabs v-model="activeName">
            <el-tab-pane label="基本信息" name="1">

                <el-form style="text-align: left;">
                    <el-form-item label="计划名称:" style="font-weight: bold;">
                        <span style="font-weight: normal; margin-left: 27px;">{{ testPlan?.name }}</span>
                    </el-form-item>
                    <el-form-item label="所属项目:" style="font-weight: bold;">
                        <span style="font-weight: normal; margin-left: 27px;">{{ testPlan?.project_name }}({{
                            testPlan?.project_number }})</span>
                    </el-form-item>
                    <el-form-item label="测试版本:" style="font-weight: bold;">
                        <el-tag style="font-weight: normal; margin-left: 27px;">{{ testPlan?.m_version }}</el-tag>
                    </el-form-item>
                    <el-form-item label="关联版本:" style="font-weight: bold;">
                        <el-tag style="font-weight: normal; margin-left: 27px;"
                            v-for="pv in testPlan?.product_version || []">{{ pv.name }}</el-tag>
                    </el-form-item>
                    <el-form-item label="测试类型:" style="font-weight: bold;">
                        <span style="font-weight: normal; margin-left: 27px;">{{ test_type_map[testPlan?.test_type] ||
                            testPlan?.test_type }}</span>
                    </el-form-item>
                    <el-form-item label="计划用途:" style="font-weight: bold;">
                        <el-tag style="margin-left: 27px;" v-if="testPlan?.plan_use == 'FULL_FUNCTIONALITY_TEST'"
                            type="success">全功能测试</el-tag>
                        <el-tag style="margin-left: 27px;" v-else-if="testPlan?.plan_use == 'VERSION_REGRESSION_TEST'"
                            type="success">版本回归测试</el-tag>
                        <el-tag style="margin-left: 27px;" v-else-if="testPlan?.plan_use == 'SPECIFIC_VALIDATION_TEST'"
                            type="success">专项验证测试</el-tag>
                        <el-tag style="margin-left: 27px;" v-else-if="testPlan?.plan_use == 'PROBLEM_VALIDATION_TEST'"
                            type="success">问题验证测试</el-tag>
                        <el-tag style="margin-left: 27px;" v-else-if="testPlan?.plan_use == 'DURABILITY_TEST'"
                            type="success">耐久测试</el-tag>
                    </el-form-item>
                    <el-form-item label="计划类型:" style="font-weight: bold;">
                        <el-tag style="margin-left: 27px;" v-if="testPlan?.plan_type == 0" type="success">对外发布</el-tag>
                        <el-tag style="margin-left: 27px;" v-else type="primary">内部验证</el-tag>
                    </el-form-item>
                    <el-form-item label="是否异常停止:" prop="abnormal_stop" style="font-weight: bold;">
                        <el-tag v-if="testPlan?.abnormal_stop == '1'" type="danger">是</el-tag>
                        <el-tag v-else type="success">否</el-tag>
                    </el-form-item>
                    <el-form-item label="负责人:" style="font-weight: bold;">
                        <span style="font-weight: normal; margin-left: 42px;">{{ testPlan?.pic_name }}</span>
                    </el-form-item>
                    <el-form-item label="预期计划时间:" style="font-weight: bold;">
                        <span style="font-weight: normal;">{{ testPlan?.p_start_time }} ~ {{ testPlan?.p_end_time
                            }}</span>
                    </el-form-item>
                    <el-form-item label="样品信息:" style="font-weight: bold;">
                        <span style="font-weight: normal; margin-left: 27px;">{{ testPlan?.sample_information }}</span>
                    </el-form-item>
                    <el-form-item label="计划描述:" style="font-weight: bold;">
                        <span style="font-weight: normal; margin-left: 27px;">{{ testPlan?.desc }}</span>
                    </el-form-item>
                    <el-form-item label="计划进度:" style="font-weight: bold;">
                        <span style="font-weight: normal; margin-left: 27px;">{{ testPlan?.progress }}</span>
                    </el-form-item>
                    <el-form-item label="测试产品:" style="font-weight: bold;">
                        <span style="font-weight: normal; margin-left: 27px;">{{ testPlan?.test_product?.name }}</span>
                    </el-form-item>
                </el-form>

            </el-tab-pane>

            <el-tab-pane label="测试用例" name="2">
                <div>
                    <el-table ref="tableRef" :data="testCaseData" stripe
                        style="width: 100%; height: calc(100vh - 200px)" class="table-container" row-key="id">
                        <el-table-column label="序号" width="60" align="center">
                            <template #default="{ row, $index }">
                                {{ $index + 1 }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" label="用例名称" width="400" align="center">
                            <template #default="{ row }">
                                <el-link type="primary" @click="onDetail(row)" :underline="false">{{ row.name
                                }}</el-link>
                            </template>
                        </el-table-column>
                        <el-table-column label="用例活动类型" width="200" align="center">
                            <template #default="{ row }">
                                <span>{{ actionTypeMap[row.action_type] || row.action_type }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="用例版本" width="100" align="center">
                            <template #default="{ row }">
                                V{{ row.version }}.0
                            </template>
                        </el-table-column>
                        <el-table-column label="用例状态" width="120" align="center">
                            <template #default="{ row }">
                                <el-tag v-if="row.status == 'PENDING'" type="primary">待评审</el-tag>
                                <el-tag v-else-if="row.status == 'REVIEWING'" type="warning">评审中</el-tag>
                                <el-tag v-else-if="row.status == 'APPROVED'" type="success">评审通过</el-tag>
                                <el-tag v-else-if="row.status == 'REJECTED'" type="danger">评审不通过</el-tag>
                                <el-tag v-else-if="row.status == 'DEPRECATED'" type="info">废弃</el-tag>
                                <el-tag v-else type="danger">未知</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="执行方式" width="120" align="center">
                            <template #default="{ row }">
                                <el-tag v-if="row.execute_mode == 'AUTOMATED_EXECUTION'" type="success">自动化测试</el-tag>
                                <el-tag v-else-if="row.execute_mode == 'MANUAL_EXECUTION'" type="success">手动测试</el-tag>
                                <el-tag v-else-if="row.execute_mode == 'SEMI_AUTOMATED_EXECUTION'"
                                    type="success">半自动化测试</el-tag>
                                <el-tag v-else type="danger">未知</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="测试值" width="100" align="center">
                            <template #default="{ row }">
                                <span>{{ row.result?.value || "" }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="测试结果" width="100" align="center">
                            <template #default="{ row }">
                                <template v-if="row.result?.result_two == null">
                                    <el-tag v-if="row.result?.id" type="warning">待判定</el-tag>
                                    <el-tag v-else type="info">待执行</el-tag>
                                </template>
                                <el-tag v-else-if="row.result?.result_two" type="success">PASS</el-tag>
                                <el-tag v-else type="danger">NG</el-tag>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-tab-pane>
        </el-tabs>

        <el-drawer v-model="drawerDetailVisible" :with-header="false" size="60%" :destroy-on-close="true">
            <TestCaseDetail :testCaseData="testCase" />
        </el-drawer>
    </div>

</template>


<script setup>
import { ref, onMounted } from 'vue'
import http from '@/utils/http/http.js';
import TestCaseDetail from '@/components/test_case.vue';
import { useRouter } from 'vue-router';

const props = defineProps(
    {
        id: {
            type: Number,
            default: 0
        }
    }
);

const activeName = ref('1');
const testCaseData = ref([]);
const drawerDetailVisible = ref(false);
const testCase = ref({});
const tableRef = ref(null);
const planStatus = ref(null);
let actionTypeMap = ref({});
const testPlan = ref(null);
const test_type_map = ref({});

const router = useRouter();

function onDetail(row) {
    testCase.value = row;
    drawerDetailVisible.value = true;
};

function onEdit() {
    router.push({ path: '/test_plans_v2/add', query: { type: 'edit', id: testPlan.value.id } });
}

onMounted(() => {
    let id = props.id;
    http.get(`/v2/test_plans/${id}`).then(res => {
        let data = res.data.data;
        testPlan.value = data;
        testCaseData.value = data.test_cases;

        planStatus.value = data.status;
        http.get('/test_cases/test_types', { params: { project_number: data.project_number } }).then(res => {
            res.data.data.forEach(item => {
                test_type_map.value[item.remark] = item.label;
            });
        });

    }).catch(err => {
        console.log(err);
    });

    http.get('/test_m/test_case_types', { params: { pagesize: 10000 } }).then(res => {
        res.data.data.results.forEach(item => {
            actionTypeMap.value[item.number] = item.name;
        });

    });
})

</script>


<style lang="scss" scoped>
.top-tool-container {}

.tool-container {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
}

.tool-bar-container {
    display: flex;
    justify-content: start;
    align-items: center;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input,
    .el-select {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

:deep(.el-collapse-item__arrow) {
    order: -1;
    margin: 0 8px 0 0;
}

:deep(.el-collapse-item__header) {
    font-size: 16px;
}
</style>