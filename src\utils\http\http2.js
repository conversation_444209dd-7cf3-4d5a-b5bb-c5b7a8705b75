import axios from 'axios';
import router from '@/router';
import qs from 'qs';

const http = axios.create({
    baseURL: import.meta.env.VITE_AM_BASE_URL,
    timeout: 5000,
    paramsSerializer: params => qs.stringify(params, {arrayFormat: 'repeat'})
});


http.interceptors.request.use(  
    config => {
        let token = sessionStorage.getItem('token');
        if (token) {
            config.headers['Authorization'] = 'Bearer ' + token;
        } else {   
            localStorage.setItem('redirectUrl', router.currentRoute.fullPath);
            router.push('/login');
        }
        return config;
    },
    error => {
        return Promise.reject(error);
    }
);

http.interceptors.response.use(
    response => {
        return response;
    },
    error => {
        if (error.response && error.response.status === 401) {
            localStorage.setItem('redirectUrl', router.currentRoute.fullPath);
            router.push('/login');
        }
        return Promise.reject(error);
    }
);


export default http;