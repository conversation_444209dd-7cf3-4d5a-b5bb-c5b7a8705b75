<template>
    <div style="display: flex; flex-direction: column; height: calc(102vh - 250px);">
    <div class="tool-bar-container">
        <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button>
        <div style="margin-left: auto; display: flex; gap: 10px;">
            <div>
                    <el-button icon="Setting" text bg @click="isSettingVisible = true">设置</el-button>
                      <!-- 设置弹窗 -->
                            <el-popover
                            v-model:visible="isSettingVisible"
                            width="180"
                            trigger="manual"
                            placement="bottom"
                            >
                            <template #reference>
                            <!-- 设置按钮作为触发器 -->
                            <div style="display: inline-block;"></div>
                            </template>
                            <!-- 操作按钮 -->
                            <div class="column-popper-title">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                        <el-checkbox
                        :model-value="tableColumns.every(item => item.show)"
                            :indeterminate="tableColumns.some(item => item.show) && tableColumns.some(item => !item.show)"
                            label="列展示"
                                @change="selectAllColumn"
                            />
                        <el-button text @click="resetColumns" style="margin-right: -10px;">重置</el-button>
                        </div>
                        </div>
                        <!-- 列设置内容 -->
                            <div class="column-content" style="max-height: 200px; overflow-y: auto;">
                            <div class="column-item" v-for="column in tableColumns" :key="column.key">
                            <el-checkbox v-model="column.show" :label="column.name"  :disabled="column.disabled"></el-checkbox>
                            </div>
                        </div>
                        </el-popover>
                        
                        </div>
            <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                <el-button text bg @click="handleReset">重置</el-button>
            </el-tooltip>
            <filterButton @click="onFilterStatusChange" :count="filterCount" />
            <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
        </div>
    </div>
    <div class="filter-container" v-if="showFilterContainer">
        <el-input v-model="form.name_re" placeholder="请输入名称" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
        <el-input v-model="form.number_re" placeholder="请输入编号" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
    </div>

  
    <el-table :data="tableData" stripe border style="width: 100%;flex: 1;" class="table-container">

        <el-table-column v-if="tableColumns[0].show" prop="name" label="名称" min-width="200" align="center"></el-table-column>
        
        <el-table-column v-if="tableColumns[1].show" prop="number" label="编号" min-width="200" align="center"></el-table-column>

        <el-table-column v-if="tableColumns[2].show" prop="desc" label="样件描述" min-width="400" align="center"></el-table-column>
        
        <el-table-column v-if="tableColumns[3].show" prop="status" label="样件状态" min-width="200" align="center" :formatter="statusFormatter"></el-table-column>
        
        <el-table-column v-if="tableColumns[4].show" prop="type" label="样件类型" min-width="200" align="center" :formatter="typeFormatter"></el-table-column>

        <el-table-column v-if="tableColumns[5].show" prop="prototype_stage" label="样件阶段" min-width="200" align="center" :formatter="stageFormatter"></el-table-column>

        <el-table-column v-if="tableColumns[6].show" prop="project" label="关联项目" min-width="250" align="center"></el-table-column>
        
        <el-table-column v-if="tableColumns[7].show" prop="user_name" label="维护人" min-width="200" align="center"></el-table-column>
        
        <el-table-column v-if="tableColumns[8].show" label="操作" min-width="135" fixed="right" align="center">
            <template #default="{ row }">
                <div style="display: flex; justify-content: center; gap: 10px;">
                    <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                    <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
                </div>
            </template>
        </el-table-column>

    </el-table>
</div>
 
    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            v-model:current-page="form.page" v-model:page-size="form.pagesize" :total="total" background
            @change="onPageChange" />
    </div>

    <el-dialog v-if="dialogAddVisible" v-model="dialogAddVisible" title="添加样件" width="800"
        :close-on-click-modal="false">
        <Add @submit="onAddSuccess" @cancel="dialogAddVisible = false" />
    </el-dialog>
  
    <el-dialog v-if="dialogEditVisible" v-model="dialogEditVisible" title="编辑样件" width="800"
        :close-on-click-modal="false">
        <Edit @submit="onEditSuccess" @cancel="dialogEditVisible = false" :r_id="r_id" />
    </el-dialog>

</template>


<script setup>
import { ref, reactive, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import Add from './add.vue';
import Edit from './edit.vue';
import filterButton from '@/components/filterButton.vue';
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/test_prototypes/index', '样件管理列表');

const tableColumns = ref([
  { key: "name", name: "名称", show: true, disabled: true },
  { key: "number", name: "编号", show: true, disabled: true },
  { key: "desc", name: "样件描述", show: true },
  { key: "status", name: "样件状态", show: true },
  { key: "type", name: "样件类型", show: true },
  { key: "prototype_stage", name: "样件阶段", show: true },
  { key: "project", name: "关联项目", show: true },
  { key: "user_name", name: "维护人", show: true },
  { key: "operation", name: "操作", show: true }
]);

// 设置弹窗的显示状态
const isSettingVisible = ref(false);

// 全选或取消全选逻辑
const selectAllColumn = (checked) => {
  tableColumns.value.forEach((column) => {
    if (!column.disabled) { // 跳过禁用的列
      column.show = checked;
    }
  });
};


// 重置列设置
const resetColumns = () => {
  tableColumns.value.forEach((column) => {
    if (!column.disabled) { // 跳过禁用的列
      column.show = true;
    }
  });
};

const tableData = ref([]);

const dialogAddVisible = ref(false);
const dialogEditVisible = ref(false);
const r_id = ref(0);

let form = reactive({
    name: '',
    number: '',
    pagesize: 20,
});

let total = ref(0);
const filterCount = ref(0);
let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/test_prototypes', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleAdd() {
    dialogAddVisible.value = true;
};

function handleEdit(row) {
    r_id.value = row.id;
    dialogEditVisible.value = true;
};

function handleDelete(row) {
    ElMessageBox.confirm(
        '确定删除吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.delete(`/test_prototypes/${row.id}`).then(res => {
            ElMessage({
                message: '删除成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessage({
                type: 'error',
                message: err.response.data.msg
            });
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};

function onAddSuccess() {
    dialogAddVisible.value = false;
    update_table();
};

function onEditSuccess() {
    dialogEditVisible.value = false;
    update_table();
};

function handleRefresh() {
    update_table();
};

onMounted(() => {
    update_table();
});

</script>


<style scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    display: flex;
    justify-content: flex-start;
}


.column-popper-title {
  border-bottom: 1px solid #ebeef5;
}

/* 自定义滚动条样式 */
.column-content::-webkit-scrollbar {
  width: 6px; /* 滚动条宽度 */
}

.column-content::-webkit-scrollbar-track {
  background: #f1f1f1; /* 轨道背景色 */
  border-radius: 4px; /* 轨道圆角 */
}

/* 滚动条滑块默认样式（浅色） */
.column-content::-webkit-scrollbar-thumb {
  background: #e4e3e3; /* 浅色 */
  border-radius: 4px; /* 滑块圆角 */
}

/* 滚动条滑块悬停样式（深色） */
.column-content::-webkit-scrollbar-thumb:hover {
  background: #c7c7c7; /* 深色 */
}
</style>

<script>
export default {
  data() {
    return {
    };
  },
  methods: {
    statusFormatter(row, column, cellValue) {
      const statusMap = {
        0: '正常',
        1: '故障',
        2: '拆解',
        3: '报废'
      };
      return statusMap[cellValue]; 
    },
    
    typeFormatter(row, column, cellValue) {
      const typeMap = {
        0: '总成',
        1: 'VDS',
        2: '主机',
        3: 'PCBA',
        4: '屏模组'
      };
      return typeMap[cellValue];  
    },
    
    stageFormatter(row, column, cellValue) {
      const testMap = {
        0: 'DV',
        1: 'PV'
      };
      return testMap[cellValue];
    }

  }
};
</script>