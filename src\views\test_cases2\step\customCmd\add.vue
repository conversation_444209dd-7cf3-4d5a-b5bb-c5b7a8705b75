<template>
    <el-form :model="form" :rules="rules" ref="formRef">
        <el-form-item label="指令" prop="cmd">
            <el-select v-model="form.cmd" placeholder="请选择指令" class="full-width" @change="onCmdChange" filterable>
                <el-option v-for="item in cmdOptions" :key="item.value" :label="item.label"
                    :value="item.value"></el-option>
            </el-select>
        </el-form-item>

        <el-form-item v-if="currentCmd?.params" v-for="(param, index) in currentCmd.params" :key="index"
            :label="param.param_name" :prop="'params.' + index"
            :rules="[{ required: true, message: '请输入' + param.param_name, trigger: 'change' }]">

            <el-input v-if="param.param_type === 'el-input'" v-model="form.params[index]" />

            <el-input-number v-else-if="param.param_type === 'el-input-number'" v-model="form.params[index]"
                class="half-width" v-bind="extraParam(param)" />

            <el-select v-else-if="param.param_type === 'el-select'" v-model="form.params[index]">
                <el-option v-for="item in param.param_options" :key="item.value" :label="item.label"
                    :value="item.value"></el-option>
            </el-select>

            <el-color-picker v-if="param.param_type === 'el-color-picker'" v-model="form.params[index]"
                :predefine="predefineColors" />

        </el-form-item>

    </el-form>
</template>


<script setup>
import { ref, watch, onMounted } from 'vue';
import http from '@/utils/http/http.js';

const props = defineProps({
    initData: {
        type: Object,
        default: () => ({}),
    },
});

const model = defineModel();

const formRef = ref(null);

const form = ref({
    cmd: '',
    params: [],
    params2: [],
});

const rules = ref({
    cmd: [
        { required: true, message: '请选择指令', trigger: 'change' },
    ],
});

const cmdOptions = ref([]);
const currentCmd = ref(null);

const onCmdChange = (value) => {
    const cmd = cmdOptions.value.find((item) => item.value === value);
    currentCmd.value = cmd;

    if (cmd?.params) {
        form.value.params = cmd.params.map((item) => item.param_value);
        form.value.params2 = cmd.params.map((item) => item.param_code);
    } else {
        form.value.params = [];
        form.value.params2 = [];
    }
};

const extraParam = (param) => {
    if (param.param_type !== 'el-input-number') {
        let res = {};
        if (param.param_min !== undefined && param.param_min !== null && param.param_min !== '') {
            res.min = param.param_min;
        }
        if (param.param_max !== undefined && param.param_max !== null && param.param_max !== '') {
            res.max = param.param_max;
        }
        if (param.param_step !== undefined && param.param_step !== null && param.param_step !== '') {
            res.step = param.param_step;
        }     
    }
    return {};
};

const validate = (callback) => {
    formRef.value.validate(callback);
};

watch(form, () => {
    model.value = form.value;
}, { immediate: true });

watch([() => props.initData, cmdOptions], () => {
    currentCmd.value = cmdOptions.value.find((item) => item.value === props.initData.cmd);
    Object.assign(form.value, props.initData);
}, { immediate: true });

watch([() => form.value.cmd, cmdOptions], () => {
    if (form.value.cmd != props.initData.cmd) {
        if (currentCmd.value?.params) {
            form.value.params = currentCmd.value.params.map((item) => item.param_value);
            form.value.params2 = currentCmd.value.params.map((item) => item.param_code);
        } else {
            form.value.params = [];
            form.value.params2 = [];
        }
    } else {
        form.value.params = props.initData.params || [];
        form.value.params2 = currentCmd.value.params.map((item) => item.param_code);
    }
});

defineExpose({
    validate,
});

const predefineColors = ref([
    '#ffffff',
    '#000000',
    '#aa0000',
    '#005500',
    '#aa5500',
    "#00aa00",
    "#aaaa00",
    "#00ff00", 
]);

onMounted(() => {
    http.get('/custom_cmds', {params: {pagesize: 100000}}).then(res => {
        cmdOptions.value = res.data.data.results.map(item => {
            return {
                label: item.cmd_name,
                value: item.cmd_code,
                params: item.params,
            };
        });
    });
});

</script>

<style scoped>
.full-width {
    width: 100%;
}

.half-width {
    width: 50%;
}
</style>